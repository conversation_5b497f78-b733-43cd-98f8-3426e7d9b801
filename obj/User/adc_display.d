User/adc_display.o: ../User/adc_display.c ../User/adc_display.h \
 ../User/ch32v00x_conf.h \
 /home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x_adc.h \
 /home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x.h \
 /home/<USER>/mounriver-studio-projects/CH32V003/Core/core_riscv.h \
 /home/<USER>/mounriver-studio-projects/CH32V003/User/system_ch32v00x.h \
 /home/<USER>/mounriver-studio-projects/CH32V003/User/ch32v00x_conf.h \
 /home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x_dbgmcu.h \
 /home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x_dma.h \
 /home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x_exti.h \
 /home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x_flash.h \
 /home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x_gpio.h \
 /home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x_i2c.h \
 /home/<USER>/mounriver-studio-projects/CH32V003/User/ch32v00x_it.h \
 /home/<USER>/mounriver-studio-projects/CH32V003/Debug/debug.h \
 /home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x_iwdg.h \
 /home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x_misc.h \
 /home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x_pwr.h \
 /home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x_rcc.h \
 /home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x_spi.h \
 /home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x_tim.h \
 /home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x_usart.h \
 /home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x_wwdg.h \
 /home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x_opa.h \
 /home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x.h \
 ../User/st7735.h ../User/adc_config.h ../User/touch_button.h

../User/adc_display.h:

../User/ch32v00x_conf.h:

/home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x_adc.h:

/home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x.h:

/home/<USER>/mounriver-studio-projects/CH32V003/Core/core_riscv.h:

/home/<USER>/mounriver-studio-projects/CH32V003/User/system_ch32v00x.h:

/home/<USER>/mounriver-studio-projects/CH32V003/User/ch32v00x_conf.h:

/home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x_dbgmcu.h:

/home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x_dma.h:

/home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x_exti.h:

/home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x_flash.h:

/home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x_gpio.h:

/home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x_i2c.h:

/home/<USER>/mounriver-studio-projects/CH32V003/User/ch32v00x_it.h:

/home/<USER>/mounriver-studio-projects/CH32V003/Debug/debug.h:

/home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x_iwdg.h:

/home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x_misc.h:

/home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x_pwr.h:

/home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x_rcc.h:

/home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x_spi.h:

/home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x_tim.h:

/home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x_usart.h:

/home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x_wwdg.h:

/home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x_opa.h:

/home/<USER>/mounriver-studio-projects/CH32V003/Peripheral/inc/ch32v00x.h:

../User/st7735.h:

../User/adc_config.h:

../User/touch_button.h:
