################################################################################
# MRS Version: 2.2.0
# Automatically-generated file. Do not edit!
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../User/adc_config.c \
../User/adc_display.c \
../User/ch32v00x_it.c \
../User/display_control.c \
../User/display_text.c \
../User/main.c \
../User/pwm_config.c \
../User/st7735.c \
../User/system_ch32v00x.c \
../User/touch_button.c 

C_DEPS += \
./User/adc_config.d \
./User/adc_display.d \
./User/ch32v00x_it.d \
./User/display_control.d \
./User/display_text.d \
./User/main.d \
./User/pwm_config.d \
./User/st7735.d \
./User/system_ch32v00x.d \
./User/touch_button.d 

OBJS += \
./User/adc_config.o \
./User/adc_display.o \
./User/ch32v00x_it.o \
./User/display_control.o \
./User/display_text.o \
./User/main.o \
./User/pwm_config.o \
./User/st7735.o \
./User/system_ch32v00x.o \
./User/touch_button.o 


EXPANDS += \
./User/adc_config.c.234r.expand \
./User/adc_display.c.234r.expand \
./User/ch32v00x_it.c.234r.expand \
./User/display_control.c.234r.expand \
./User/display_text.c.234r.expand \
./User/main.c.234r.expand \
./User/pwm_config.c.234r.expand \
./User/st7735.c.234r.expand \
./User/system_ch32v00x.c.234r.expand \
./User/touch_button.c.234r.expand 



# Each subdirectory must supply rules for building sources it contributes
User/%.o: ../User/%.c
	@	riscv-none-embed-gcc -march=rv32ecxw -mabi=ilp32e -msmall-data-limit=0 -msave-restore -fmax-errors=20 -Os -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-common -Wunused -Wuninitialized -g -DPLATFORMIO=1 -I"/home/<USER>/mounriver-studio-projects/CH32V003F4U6/Debug" -I"/home/<USER>/mounriver-studio-projects/CH32V003F4U6/Core" -I"/home/<USER>/mounriver-studio-projects/CH32V003F4U6/User" -I"/home/<USER>/mounriver-studio-projects/CH32V003F4U6/Peripheral/inc" -std=gnu99 -MMD -MP -MF"$(@:%.o=%.d)" -MT"$(@)" -c -o "$@" "$<"

