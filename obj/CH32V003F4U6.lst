
CH32V003F4U6.elf:     file format elf32-littleriscv
CH32V003F4U6.elf
architecture: riscv:rv32, flags 0x00000112:
EXEC_P, HAS_SYMS, D_PAGED
start address 0x00000000

Program Header:
    LOAD off    0x00001000 vaddr 0x00000000 paddr 0x00000000 align 2**12
         filesz 0x00002d60 memsz 0x00002d60 flags r-x
    LOAD off    0x00004000 vaddr 0x20000000 paddr 0x00002d60 align 2**12
         filesz 0x00000040 memsz 0x000001ec flags rw-
    LOAD off    0x00004700 vaddr 0x20000700 paddr 0x20000700 align 2**12
         filesz 0x00000000 memsz 0x00000100 flags rw-

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .init         000000a0  00000000  00000000  00001000  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  1 .highcodelalign 00000000  000000a0  000000a0  00004040  2**0
                  CONTENTS
  2 .highcode     00000000  20000000  20000000  00004040  2**0
                  CONTENTS
  3 .text         00002cc0  000000a0  000000a0  000010a0  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  4 .fini         00000000  00002d60  00002d60  00004040  2**0
                  CONTENTS, ALLOC, LOAD, CODE
  5 .dalign       00000000  20000000  20000000  00004040  2**0
                  CONTENTS
  6 .dlalign      00000000  00002d60  00002d60  00004040  2**0
                  CONTENTS
  7 .data         00000040  20000000  00002d60  00004000  2**2
                  CONTENTS, ALLOC, LOAD, DATA
  8 .bss          000001ac  20000040  00002da0  00004040  2**2
                  ALLOC
  9 .stack        00000100  20000700  20000700  00004700  2**0
                  ALLOC
 10 .debug_info   00014fce  00000000  00000000  00004040  2**0
                  CONTENTS, READONLY, DEBUGGING
 11 .debug_abbrev 00003ade  00000000  00000000  0001900e  2**0
                  CONTENTS, READONLY, DEBUGGING
 12 .debug_loc    00005699  00000000  00000000  0001caec  2**0
                  CONTENTS, READONLY, DEBUGGING
 13 .debug_aranges 00000b88  00000000  00000000  00022188  2**3
                  CONTENTS, READONLY, DEBUGGING
 14 .debug_ranges 00000da8  00000000  00000000  00022d10  2**3
                  CONTENTS, READONLY, DEBUGGING
 15 .debug_line   0000de67  00000000  00000000  00023ab8  2**0
                  CONTENTS, READONLY, DEBUGGING
 16 .debug_str    00003694  00000000  00000000  0003191f  2**0
                  CONTENTS, READONLY, DEBUGGING
 17 .comment      00000033  00000000  00000000  00034fb3  2**0
                  CONTENTS, READONLY
 18 .debug_frame  00001ac8  00000000  00000000  00034fe8  2**2
                  CONTENTS, READONLY, DEBUGGING
SYMBOL TABLE:
00000000 l    d  .init	00000000 .init
000000a0 l    d  .highcodelalign	00000000 .highcodelalign
20000000 l    d  .highcode	00000000 .highcode
000000a0 l    d  .text	00000000 .text
00002d60 l    d  .fini	00000000 .fini
20000000 l    d  .dalign	00000000 .dalign
00002d60 l    d  .dlalign	00000000 .dlalign
20000000 l    d  .data	00000000 .data
20000040 l    d  .bss	00000000 .bss
20000700 l    d  .stack	00000000 .stack
00000000 l    d  .debug_info	00000000 .debug_info
00000000 l    d  .debug_abbrev	00000000 .debug_abbrev
00000000 l    d  .debug_loc	00000000 .debug_loc
00000000 l    d  .debug_aranges	00000000 .debug_aranges
00000000 l    d  .debug_ranges	00000000 .debug_ranges
00000000 l    d  .debug_line	00000000 .debug_line
00000000 l    d  .debug_str	00000000 .debug_str
00000000 l    d  .comment	00000000 .comment
00000000 l    d  .debug_frame	00000000 .debug_frame
00000000 l    df *ABS*	00000000 adc_config.c
00000000 l    df *ABS*	00000000 adc_display.c
00000334 l     F .text	00000048 ADC_Display_Format_Voltage.part.0
00002698 l     O .text	00000008 CSWTCH.3
00000000 l    df *ABS*	00000000 ch32v00x_it.c
00000000 l    df *ABS*	00000000 display_control.c
00000000 l    df *ABS*	00000000 display_text.c
00000000 l    df *ABS*	00000000 main.c
00000000 l    df *ABS*	00000000 pwm_config.c
00000000 l    df *ABS*	00000000 st7735.c
00000eb4 l     F .text	0000003e SPI_send_DMA
00000ef2 l     F .text	00000012 SPI_send
00000f04 l     F .text	00000016 write_command_8
00000f1a l     F .text	00000020 write_data_16
00000f3a l     F .text	0000003c tft_set_window
2000007a l     O .bss	00000002 _bg_color
2000007c l     O .bss	00000140 _buffer
200001bc l     O .bss	00000002 _cursor_x
200001be l     O .bss	00000002 _cursor_y
200001c0 l     O .bss	0000000c str.4169
20000000 l     O .data	00000002 _color
00002858 l     O .text	00000500 font
00000000 l    df *ABS*	00000000 system_ch32v00x.c
00000000 l    df *ABS*	00000000 touch_button.c
00000000 l    df *ABS*	00000000 ch32v00x_adc.c
00000000 l    df *ABS*	00000000 ch32v00x_dbgmcu.c
00000000 l    df *ABS*	00000000 ch32v00x_exti.c
00000000 l    df *ABS*	00000000 ch32v00x_gpio.c
00000000 l    df *ABS*	00000000 ch32v00x_misc.c
00000000 l    df *ABS*	00000000 ch32v00x_rcc.c
20000018 l     O .data	00000014 ADCPrescTable
2000002c l     O .data	00000010 APBAHBPrescTable
00000000 l    df *ABS*	00000000 ch32v00x_tim.c
00000000 l    df *ABS*	00000000 ch32v00x_usart.c
00000000 l    df *ABS*	00000000 debug.c
200001e8 l     O .bss	00000002 p_ms
200001ea l     O .bss	00000001 p_us
00000000 l    df *ABS*	00000000 wchprintf.c
00000000 l    df *ABS*	00000000 memcpy.c
00002156  w    F .text	00000004 printDouble
00000400 g     F .text	00000026 ADC_Display_Init
20000040 g     O .bss	0000000c adc_display_config
0000215a  w    F .text	00000360 print
000024ba  w    F .text	00000024 printf
20000840 g       .data	00000000 __global_pointer$
00000a70 g     F .text	00000026 Display_Control_Init
000016fc  w      .text	00000000 TIM1_CC_IRQHandler
0000090c g     F .text	00000010 HardFault_Handler
00002038  w    F .text	0000011a printInt
00001cba g     F .text	0000000e TIM_OC1PreloadConfig
00001608 g     F .text	0000002a Touch_Button_Init
000016fc  w      .text	00000000 SysTick_Handler
00001816 g     F .text	0000000a ADC_StartCalibration
0000051a g     F .text	00000020 ADC_Display_Should_Update
00001a56 g     F .text	00000062 NVIC_Init
000016fc  w      .text	00000000 PVD_IRQHandler
000024de g     F .text	0000002a snprintf
0000090a g     F .text	00000002 NMI_Handler
0000190c g     F .text	0000000a DBGMCU_GetCHIPID
200001cc g     O .bss	00000004 system_tick_ms
00001106 g     F .text	0000000e tft_set_cursor
00001ddc g     F .text	0000000a USART_GetFlagStatus
20000040 g       .bss	00000000 _sbss
00000100 g       *ABS*	00000000 __stack_size
000018fa g     F .text	0000000a ADC_GetFlagStatus
00001e54 g     F .text	0000005c USART_Printf_Init
00000b24 g     F .text	0000001a Display_Text_Clear_Screen
000000aa g     F .text	0000000a .hidden __riscv_restore_2
00001c78 g     F .text	00000016 TIM_CtrlPWMOutputs
00002552 g     F .text	000000d2 memcpy
000017fc g     F .text	00000010 ADC_Cmd
00002152  w    F .text	00000004 printLongLongInt
000003a4 g     F .text	0000005c ADC_Display_Draw_Channel_Labels
00001c60 g     F .text	00000018 TIM_Cmd
00002508 g     F .text	0000004a puts
0000037c g     F .text	00000028 ADC_Display_Draw_Header
20000014 g     O .data	00000004 SystemCoreClock
2000004c g     O .bss	0000000c display_control
00000986 g     F .text	00000018 Display_Control_Turn_On
000000d4 g     F .text	0000002c .hidden __udivsi3
000000a0 g       .init	00000000 _einit
00001ce4 g     F .text	0000000c TIM_ClearITPendingBit
00001b6c g     F .text	0000001e RCC_APB2PeriphClockCmd
000019a8 g     F .text	0000007c GPIO_Init
00000234 g     F .text	00000060 ADC_Config_Init
200001e4 g     O .bss	00000004 NVIC_Priority_Group
000016fc  w      .text	00000000 SPI1_IRQHandler
0000180c g     F .text	0000000a ADC_ResetCalibration
00001dbe g     F .text	00000016 USART_Cmd
000000a0 g     F .text	0000000a .hidden __riscv_save_1
0000120c g     F .text	00000020 tft_print
0000053a g     F .text	00000036 ADC_Display_Update
000000aa g     F .text	0000000a .hidden __riscv_restore_0
000016fc  w      .text	00000000 AWU_IRQHandler
000005e8 g     F .text	00000086 ADC_Display_Draw_Logo_Channels
0000091c g     F .text	00000008 EXTI7_0_IRQHandler
00000b3e g     F .text	0000004e Display_Text_Welcome
00001b8a g     F .text	0000001e RCC_APB1PeriphClockCmd
20000700 g       .stack	00000000 _heap_end
00000948 g     F .text	0000003e Display_Control_Update
000016fc  w      .text	00000000 DMA1_Channel4_IRQHandler
00001838 g     F .text	000000ba ADC_RegularChannelConfig
000016fc  w      .text	00000000 ADC1_IRQHandler
000018f2 g     F .text	00000008 ADC_GetConversionValue
000001f2 g     F .text	00000042 ADC_GPIO_Config
00001632 g     F .text	00000072 Touch_Button_Update
0000199e g     F .text	0000000a EXTI_ClearITPendingBit
200001ec g       .bss	00000000 _ebss
000016fc  w      .text	00000000 DMA1_Channel7_IRQHandler
00001bf6 g     F .text	0000006a TIM_OC1Init
00001820 g     F .text	00000018 ADC_SoftwareStartConvCmd
00001de6 g     F .text	00000034 Delay_Init
000015c6 g     F .text	00000042 Touch_Button_EXTI_Config
00001ca0 g     F .text	0000001a TIM_ARRPreloadConfig
0000111e g     F .text	00000006 tft_set_background_color
00000100 g     F .text	00000008 .hidden __umodsi3
00000d56 g     F .text	0000009a PWM_Timer_Config
000016fc  w      .text	00000000 I2C1_EV_IRQHandler
00000858 g     F .text	00000082 ADC_Display_Draw_Logo_Header
00000294 g     F .text	00000046 ADC_Read_Channel
00001ccc g     F .text	00000018 TIM_GetITStatus
00001aca g     F .text	000000a2 RCC_GetClocksFreq
00001540 g     F .text	00000030 Touch_Button_GPIO_Config
000016fc  w      .text	00000000 DMA1_Channel6_IRQHandler
00001cf0 g     F .text	000000ce USART_Init
000016fc  w      .text	00000000 RCC_IRQHandler
000016fc  w      .text	00000000 TIM1_TRG_COM_IRQHandler
000016fc  w      .text	00000000 DMA1_Channel1_IRQHandler
00000000 g       .init	00000000 _start
20000004 g     O .data	00000010 AHBPrescTable
00000ea0 g     F .text	0000000e PWM_Turn_Off
000009e0 g     F .text	0000001a Display_Control_Clear_Screen
00001f4a  w    F .text	000000ee prints
00001c8e g     F .text	00000012 TIM_ITConfig
20000000 g       .highcode	00000000 _highcode_vma_start
00001916 g     F .text	0000006a EXTI_Init
000000b4 g     F .text	00000014 .hidden __mulsi3
000009ba g     F .text	0000001a Display_Control_Toggle
20000000 g       .dalign	00000000 _data_vma
00000e1c g     F .text	00000022 PWM_Config_Init
000016a4 g     F .text	0000000c Touch_Button_Get_Event
00000ade g     F .text	00000046 Display_Text_Centered
000008da g     F .text	00000030 ADC_Display_Draw_Logo_Style
00001a2e g     F .text	00000022 GPIO_EXTILineConfig
0000099e g     F .text	0000001c Display_Control_Turn_Off
2000006c g     O .bss	00000004 last_adc_read_time
000009d4 g     F .text	0000000c Display_Control_Is_On
00001980 g     F .text	0000001e EXTI_GetITStatus
0000014a g     F .text	000000a8 memset
00001ab8 g     F .text	00000012 RCC_AdjustHSICalibrationValue
00000bf2 g     F .text	0000012e main
20000058 g     O .bss	00000014 adc_data
00000b8c g     F .text	00000066 System_Init
0000066e g     F .text	00000072 ADC_Display_Draw_Logo_Main_Voltage
000016fc  w      .text	00000000 DMA1_Channel5_IRQHandler
00000a96 g     F .text	00000048 Display_Text_Custom
000000cc g     F .text	00000058 .hidden __divsi3
00001e1a g     F .text	0000003a Delay_Ms
0000122c g     F .text	000000aa tft_print_number
000002da g     F .text	0000005a ADC_Read_All_Channels
00000f76 g     F .text	00000190 tft_init
000000a0 g       .highcodelalign	00000000 _highcode_lma
000013a0 g     F .text	00000134 SystemInit
00000eae g     F .text	00000006 PWM_Get_Brightness
00001f00  w    F .text	0000004a printchar
00000000 g       .init	00000000 _sinit
00001312 g     F .text	0000008e tft_fill_rect
000016fc  w      .text	00000000 DMA1_Channel3_IRQHandler
00000e90 g     F .text	00000010 PWM_Turn_On
000016fc  w      .text	00000000 TIM1_UP_IRQHandler
20000070 g     O .bss	00000001 system_initialized
000012d6 g     F .text	0000003c tft_draw_pixel
000016fc  w      .text	00000000 WWDG_IRQHandler
00001904 g     F .text	00000008 ADC_ClearFlag
00000d20 g     F .text	00000036 PWM_GPIO_Config
00000924 g     F .text	00000024 TIM2_IRQHandler
20000800 g       .stack	00000000 _eusrstack
000000a0 g     F .text	0000000a .hidden __riscv_save_2
000016fc  w      .text	00000000 SW_Handler
20000074 g     O .bss	00000006 pwm_control
000016fc  w      .text	00000000 TIM1_BRK_IRQHandler
000017ae g     F .text	0000004e ADC_Init
00001dd4 g     F .text	00000008 USART_SendData
00001eb0 g     F .text	00000050 _write
20000040 g       .data	00000000 _edata
200001ec g       .bss	00000000 _end
20000000 g       .highcode	00000000 _highcode_vma_end
00001ba8 g     F .text	0000004e TIM_TimeBaseInit
00002d60 g       .dlalign	00000000 _data_lma
000014d4 g     F .text	0000006c SystemCoreClockUpdate
000016b6 g     F .text	00000046 Touch_Button_IRQ_Handler
00000124 g     F .text	00000024 .hidden __modsi3
000016fc  w      .text	00000000 DMA1_Channel2_IRQHandler
000016b0 g     F .text	00000006 Touch_Button_Get_Time_Ms
000016fe  w      .text	00000000 handle_reset
000016fc  w      .text	00000000 FLASH_IRQHandler
000000a0 g     F .text	0000000a .hidden __riscv_save_0
000016fc  w      .text	00000000 USART1_IRQHandler
000006e0 g     F .text	00000178 ADC_Display_Draw_Vietnam_Flag
00001570 g     F .text	00000056 Touch_Button_Timer_Init
00001114 g     F .text	0000000a tft_set_color
00000df0 g     F .text	0000002c PWM_Set_Brightness
000016fc  w      .text	00000000 I2C1_ER_IRQHandler
000004e4 g     F .text	0000001c ADC_Display_Draw_All_Channels
00001a50 g     F .text	00000006 NVIC_PriorityGroupConfig
00001124 g     F .text	000000e8 tft_print_char
00000e3e g     F .text	00000052 PWM_Update_Fade
00000570 g     F .text	00000078 ADC_Display_Draw_Logo_Border
000009fa g     F .text	00000076 Display_Control_Show_Startup_Message
000000aa g     F .text	0000000a .hidden __riscv_restore_1
00001cc8 g     F .text	00000004 TIM_SetCompare1
00000500 g     F .text	0000001a ADC_Display_Clear_Values_Area
00001a24 g     F .text	0000000a GPIO_ReadInputDataBit
00000426 g     F .text	000000be ADC_Display_Draw_Channel_Data
200001d0 g     O .bss	00000014 touch_button



Disassembly of section .init:

00000000 <_sinit>:
   0:	6fe0106f          	j	16fe <handle_reset>
   4:	0000                	unimp
   6:	0000                	unimp
   8:	090a                	slli	s2,s2,0x2
   a:	0000                	unimp
   c:	090c                	addi	a1,sp,144
	...
  2e:	0000                	unimp
  30:	16fc                	addi	a5,sp,876
  32:	0000                	unimp
  34:	0000                	unimp
  36:	0000                	unimp
  38:	16fc                	addi	a5,sp,876
  3a:	0000                	unimp
  3c:	0000                	unimp
  3e:	0000                	unimp
  40:	16fc                	addi	a5,sp,876
  42:	0000                	unimp
  44:	16fc                	addi	a5,sp,876
  46:	0000                	unimp
  48:	16fc                	addi	a5,sp,876
  4a:	0000                	unimp
  4c:	16fc                	addi	a5,sp,876
  4e:	0000                	unimp
  50:	091c                	addi	a5,sp,144
  52:	0000                	unimp
  54:	16fc                	addi	a5,sp,876
  56:	0000                	unimp
  58:	16fc                	addi	a5,sp,876
  5a:	0000                	unimp
  5c:	16fc                	addi	a5,sp,876
  5e:	0000                	unimp
  60:	16fc                	addi	a5,sp,876
  62:	0000                	unimp
  64:	16fc                	addi	a5,sp,876
  66:	0000                	unimp
  68:	16fc                	addi	a5,sp,876
  6a:	0000                	unimp
  6c:	16fc                	addi	a5,sp,876
  6e:	0000                	unimp
  70:	16fc                	addi	a5,sp,876
  72:	0000                	unimp
  74:	16fc                	addi	a5,sp,876
  76:	0000                	unimp
  78:	16fc                	addi	a5,sp,876
  7a:	0000                	unimp
  7c:	16fc                	addi	a5,sp,876
  7e:	0000                	unimp
  80:	16fc                	addi	a5,sp,876
  82:	0000                	unimp
  84:	16fc                	addi	a5,sp,876
  86:	0000                	unimp
  88:	16fc                	addi	a5,sp,876
  8a:	0000                	unimp
  8c:	16fc                	addi	a5,sp,876
  8e:	0000                	unimp
  90:	16fc                	addi	a5,sp,876
  92:	0000                	unimp
  94:	16fc                	addi	a5,sp,876
  96:	0000                	unimp
  98:	0924                	addi	s1,sp,152
  9a:	0000                	unimp
  9c:	0000                	unimp
	...

Disassembly of section .text:

000000a0 <__riscv_save_0>:
      a0:	1151                	addi	sp,sp,-12
      a2:	c026                	sw	s1,0(sp)
      a4:	c222                	sw	s0,4(sp)
      a6:	c406                	sw	ra,8(sp)
      a8:	8282                	jr	t0

000000aa <__riscv_restore_0>:
      aa:	4482                	lw	s1,0(sp)
      ac:	4412                	lw	s0,4(sp)
      ae:	40a2                	lw	ra,8(sp)
      b0:	0131                	addi	sp,sp,12
      b2:	8082                	ret

000000b4 <__mulsi3>:
      b4:	862a                	mv	a2,a0
      b6:	4501                	li	a0,0
      b8:	0015f693          	andi	a3,a1,1
      bc:	c291                	beqz	a3,c0 <__mulsi3+0xc>
      be:	9532                	add	a0,a0,a2
      c0:	8185                	srli	a1,a1,0x1
      c2:	0606                	slli	a2,a2,0x1
      c4:	f9f5                	bnez	a1,b8 <__mulsi3+0x4>
      c6:	8082                	ret
      c8:	0000                	unimp
	...

000000cc <__divsi3>:
      cc:	02054e63          	bltz	a0,108 <__umodsi3+0x8>
      d0:	0405c363          	bltz	a1,116 <__umodsi3+0x16>

000000d4 <__udivsi3>:
      d4:	862e                	mv	a2,a1
      d6:	85aa                	mv	a1,a0
      d8:	557d                	li	a0,-1
      da:	c215                	beqz	a2,fe <__udivsi3+0x2a>
      dc:	4685                	li	a3,1
      de:	00b67863          	bgeu	a2,a1,ee <__udivsi3+0x1a>
      e2:	00c05663          	blez	a2,ee <__udivsi3+0x1a>
      e6:	0606                	slli	a2,a2,0x1
      e8:	0686                	slli	a3,a3,0x1
      ea:	feb66ce3          	bltu	a2,a1,e2 <__udivsi3+0xe>
      ee:	4501                	li	a0,0
      f0:	00c5e463          	bltu	a1,a2,f8 <__udivsi3+0x24>
      f4:	8d91                	sub	a1,a1,a2
      f6:	8d55                	or	a0,a0,a3
      f8:	8285                	srli	a3,a3,0x1
      fa:	8205                	srli	a2,a2,0x1
      fc:	faf5                	bnez	a3,f0 <__udivsi3+0x1c>
      fe:	8082                	ret

00000100 <__umodsi3>:
     100:	8286                	mv	t0,ra
     102:	3fc9                	jal	d4 <__udivsi3>
     104:	852e                	mv	a0,a1
     106:	8282                	jr	t0
     108:	40a00533          	neg	a0,a0
     10c:	0005d763          	bgez	a1,11a <__umodsi3+0x1a>
     110:	40b005b3          	neg	a1,a1
     114:	b7c1                	j	d4 <__udivsi3>
     116:	40b005b3          	neg	a1,a1
     11a:	8286                	mv	t0,ra
     11c:	3f65                	jal	d4 <__udivsi3>
     11e:	40a00533          	neg	a0,a0
     122:	8282                	jr	t0

00000124 <__modsi3>:
     124:	8286                	mv	t0,ra
     126:	0005c763          	bltz	a1,134 <__modsi3+0x10>
     12a:	00054963          	bltz	a0,13c <__modsi3+0x18>
     12e:	375d                	jal	d4 <__udivsi3>
     130:	852e                	mv	a0,a1
     132:	8282                	jr	t0
     134:	40b005b3          	neg	a1,a1
     138:	fe055be3          	bgez	a0,12e <__modsi3+0xa>
     13c:	40a00533          	neg	a0,a0
     140:	3f51                	jal	d4 <__udivsi3>
     142:	40b00533          	neg	a0,a1
     146:	8282                	jr	t0
	...

0000014a <memset>:
     14a:	433d                	li	t1,15
     14c:	872a                	mv	a4,a0
     14e:	02c37363          	bgeu	t1,a2,174 <memset+0x2a>
     152:	00f77793          	andi	a5,a4,15
     156:	efbd                	bnez	a5,1d4 <memset+0x8a>
     158:	e5ad                	bnez	a1,1c2 <memset+0x78>
     15a:	ff067693          	andi	a3,a2,-16
     15e:	8a3d                	andi	a2,a2,15
     160:	96ba                	add	a3,a3,a4
     162:	c30c                	sw	a1,0(a4)
     164:	c34c                	sw	a1,4(a4)
     166:	c70c                	sw	a1,8(a4)
     168:	c74c                	sw	a1,12(a4)
     16a:	0741                	addi	a4,a4,16
     16c:	fed76be3          	bltu	a4,a3,162 <memset+0x18>
     170:	e211                	bnez	a2,174 <memset+0x2a>
     172:	8082                	ret
     174:	40c306b3          	sub	a3,t1,a2
     178:	068a                	slli	a3,a3,0x2
     17a:	00000297          	auipc	t0,0x0
     17e:	9696                	add	a3,a3,t0
     180:	00a68067          	jr	10(a3)
     184:	00b70723          	sb	a1,14(a4)
     188:	00b706a3          	sb	a1,13(a4)
     18c:	00b70623          	sb	a1,12(a4)
     190:	00b705a3          	sb	a1,11(a4)
     194:	00b70523          	sb	a1,10(a4)
     198:	00b704a3          	sb	a1,9(a4)
     19c:	00b70423          	sb	a1,8(a4)
     1a0:	00b703a3          	sb	a1,7(a4)
     1a4:	00b70323          	sb	a1,6(a4)
     1a8:	00b702a3          	sb	a1,5(a4)
     1ac:	00b70223          	sb	a1,4(a4)
     1b0:	00b701a3          	sb	a1,3(a4)
     1b4:	00b70123          	sb	a1,2(a4)
     1b8:	00b700a3          	sb	a1,1(a4)
     1bc:	00b70023          	sb	a1,0(a4)
     1c0:	8082                	ret
     1c2:	0ff5f593          	andi	a1,a1,255
     1c6:	00859693          	slli	a3,a1,0x8
     1ca:	8dd5                	or	a1,a1,a3
     1cc:	01059693          	slli	a3,a1,0x10
     1d0:	8dd5                	or	a1,a1,a3
     1d2:	b761                	j	15a <memset+0x10>
     1d4:	00279693          	slli	a3,a5,0x2
     1d8:	00000297          	auipc	t0,0x0
     1dc:	9696                	add	a3,a3,t0
     1de:	8286                	mv	t0,ra
     1e0:	fa8680e7          	jalr	-88(a3)
     1e4:	8096                	mv	ra,t0
     1e6:	17c1                	addi	a5,a5,-16
     1e8:	8f1d                	sub	a4,a4,a5
     1ea:	963e                	add	a2,a2,a5
     1ec:	f8c374e3          	bgeu	t1,a2,174 <memset+0x2a>
     1f0:	b7a5                	j	158 <memset+0xe>

000001f2 <ADC_GPIO_Config>:
     1f2:	eafff2ef          	jal	t0,a0 <__riscv_save_0>
     1f6:	1151                	addi	sp,sp,-12
     1f8:	40011537          	lui	a0,0x40011
     1fc:	09800793          	li	a5,152
     200:	858a                	mv	a1,sp
     202:	40050513          	addi	a0,a0,1024 # 40011400 <__global_pointer$+0x20010bc0>
     206:	807c                	sh	a5,0(sp)
     208:	00011123          	sh	zero,2(sp)
     20c:	00011223          	sh	zero,4(sp)
     210:	00011323          	sh	zero,6(sp)
     214:	00011423          	sh	zero,8(sp)
     218:	00011523          	sh	zero,10(sp)
     21c:	78c010ef          	jal	ra,19a8 <GPIO_Init>
     220:	47c1                	li	a5,16
     222:	858a                	mv	a1,sp
     224:	40011537          	lui	a0,0x40011
     228:	807c                	sh	a5,0(sp)
     22a:	c402                	sw	zero,8(sp)
     22c:	77c010ef          	jal	ra,19a8 <GPIO_Init>
     230:	0131                	addi	sp,sp,12
     232:	bda5                	j	aa <__riscv_restore_0>

00000234 <ADC_Config_Init>:
     234:	e6dff2ef          	jal	t0,a0 <__riscv_save_0>
     238:	1121                	addi	sp,sp,-24
     23a:	4661                	li	a2,24
     23c:	4581                	li	a1,0
     23e:	850a                	mv	a0,sp
     240:	3729                	jal	14a <memset>
     242:	4585                	li	a1,1
     244:	23000513          	li	a0,560
     248:	125010ef          	jal	ra,1b6c <RCC_APB2PeriphClockCmd>
     24c:	40012437          	lui	s0,0x40012
     250:	374d                	jal	1f2 <ADC_GPIO_Config>
     252:	40040513          	addi	a0,s0,1024 # 40012400 <__global_pointer$+0x20011bc0>
     256:	5b6010ef          	jal	ra,180c <ADC_ResetCalibration>
     25a:	40040513          	addi	a0,s0,1024
     25e:	5b8010ef          	jal	ra,1816 <ADC_StartCalibration>
     262:	000e07b7          	lui	a5,0xe0
     266:	c63e                	sw	a5,12(sp)
     268:	858a                	mv	a1,sp
     26a:	4785                	li	a5,1
     26c:	40040513          	addi	a0,s0,1024
     270:	00f10a23          	sb	a5,20(sp)
     274:	c002                	sw	zero,0(sp)
     276:	c202                	sw	zero,4(sp)
     278:	c402                	sw	zero,8(sp)
     27a:	c802                	sw	zero,16(sp)
     27c:	532010ef          	jal	ra,17ae <ADC_Init>
     280:	4585                	li	a1,1
     282:	40040513          	addi	a0,s0,1024
     286:	576010ef          	jal	ra,17fc <ADC_Cmd>
     28a:	4529                	li	a0,10
     28c:	38f010ef          	jal	ra,1e1a <Delay_Ms>
     290:	0161                	addi	sp,sp,24
     292:	bd21                	j	aa <__riscv_restore_0>

00000294 <ADC_Read_Channel>:
     294:	e0dff2ef          	jal	t0,a0 <__riscv_save_0>
     298:	40012437          	lui	s0,0x40012
     29c:	85aa                	mv	a1,a0
     29e:	469d                	li	a3,7
     2a0:	4605                	li	a2,1
     2a2:	40040513          	addi	a0,s0,1024 # 40012400 <__global_pointer$+0x20011bc0>
     2a6:	592010ef          	jal	ra,1838 <ADC_RegularChannelConfig>
     2aa:	4585                	li	a1,1
     2ac:	40040513          	addi	a0,s0,1024
     2b0:	570010ef          	jal	ra,1820 <ADC_SoftwareStartConvCmd>
     2b4:	40040493          	addi	s1,s0,1024
     2b8:	4589                	li	a1,2
     2ba:	8526                	mv	a0,s1
     2bc:	63e010ef          	jal	ra,18fa <ADC_GetFlagStatus>
     2c0:	dd65                	beqz	a0,2b8 <ADC_Read_Channel+0x24>
     2c2:	40040513          	addi	a0,s0,1024
     2c6:	62c010ef          	jal	ra,18f2 <ADC_GetConversionValue>
     2ca:	84aa                	mv	s1,a0
     2cc:	4589                	li	a1,2
     2ce:	40040513          	addi	a0,s0,1024
     2d2:	632010ef          	jal	ra,1904 <ADC_ClearFlag>
     2d6:	8526                	mv	a0,s1
     2d8:	bbc9                	j	aa <__riscv_restore_0>

000002da <ADC_Read_All_Channels>:
     2da:	cd21                	beqz	a0,332 <ADC_Read_All_Channels+0x58>
     2dc:	dc5ff2ef          	jal	t0,a0 <__riscv_save_0>
     2e0:	842a                	mv	s0,a0
     2e2:	1161                	addi	sp,sp,-8
     2e4:	4511                	li	a0,4
     2e6:	377d                	jal	294 <ADC_Read_Channel>
     2e8:	a00a                	sh	a0,0(s0)
     2ea:	450d                	li	a0,3
     2ec:	3765                	jal	294 <ADC_Read_Channel>
     2ee:	a02a                	sh	a0,2(s0)
     2f0:	451d                	li	a0,7
     2f2:	374d                	jal	294 <ADC_Read_Channel>
     2f4:	a04a                	sh	a0,4(s0)
     2f6:	4509                	li	a0,2
     2f8:	3f71                	jal	294 <ADC_Read_Channel>
     2fa:	6785                	lui	a5,0x1
     2fc:	a06a                	sh	a0,6(s0)
     2fe:	01040493          	addi	s1,s0,16
     302:	00840713          	addi	a4,s0,8
     306:	ce478793          	addi	a5,a5,-796 # ce4 <main+0xf2>
     30a:	4685                	li	a3,1
     30c:	200a                	lhu	a0,0(s0)
     30e:	85be                	mv	a1,a5
     310:	c236                	sw	a3,4(sp)
     312:	c03a                	sw	a4,0(sp)
     314:	3345                	jal	b4 <__mulsi3>
     316:	4692                	lw	a3,4(sp)
     318:	8131                	srli	a0,a0,0xc
     31a:	4702                	lw	a4,0(sp)
     31c:	a40a                	sh	a0,8(s0)
     31e:	a094                	sb	a3,0(s1)
     320:	6785                	lui	a5,0x1
     322:	0409                	addi	s0,s0,2
     324:	0485                	addi	s1,s1,1
     326:	ce478793          	addi	a5,a5,-796 # ce4 <main+0xf2>
     32a:	fee411e3          	bne	s0,a4,30c <ADC_Read_All_Channels+0x32>
     32e:	0121                	addi	sp,sp,8
     330:	bbad                	j	aa <__riscv_restore_0>
     332:	8082                	ret

00000334 <ADC_Display_Format_Voltage.part.0>:
     334:	d6dff2ef          	jal	t0,a0 <__riscv_save_0>
     338:	1161                	addi	sp,sp,-8
     33a:	842e                	mv	s0,a1
     33c:	3e800593          	li	a1,1000
     340:	84b2                	mv	s1,a2
     342:	c22a                	sw	a0,4(sp)
     344:	3b75                	jal	100 <__umodsi3>
     346:	0542                	slli	a0,a0,0x10
     348:	45a9                	li	a1,10
     34a:	8141                	srli	a0,a0,0x10
     34c:	3361                	jal	d4 <__udivsi3>
     34e:	4792                	lw	a5,4(sp)
     350:	01051713          	slli	a4,a0,0x10
     354:	8341                	srli	a4,a4,0x10
     356:	853e                	mv	a0,a5
     358:	3e800593          	li	a1,1000
     35c:	c03a                	sw	a4,0(sp)
     35e:	3b9d                	jal	d4 <__udivsi3>
     360:	4702                	lw	a4,0(sp)
     362:	01051693          	slli	a3,a0,0x10
     366:	00002637          	lui	a2,0x2
     36a:	82c1                	srli	a3,a3,0x10
     36c:	68c60613          	addi	a2,a2,1676 # 268c <memcpy+0x13a>
     370:	85a6                	mv	a1,s1
     372:	8522                	mv	a0,s0
     374:	16a020ef          	jal	ra,24de <snprintf>
     378:	0121                	addi	sp,sp,8
     37a:	bb05                	j	aa <__riscv_restore_0>

0000037c <ADC_Display_Draw_Header>:
     37c:	d25ff2ef          	jal	t0,a0 <__riscv_save_0>
     380:	6541                	lui	a0,0x10
     382:	157d                	addi	a0,a0,-1
     384:	591000ef          	jal	ra,1114 <tft_set_color>
     388:	4501                	li	a0,0
     38a:	595000ef          	jal	ra,111e <tft_set_background_color>
     38e:	4581                	li	a1,0
     390:	4515                	li	a0,5
     392:	575000ef          	jal	ra,1106 <tft_set_cursor>
     396:	00002537          	lui	a0,0x2
     39a:	65450513          	addi	a0,a0,1620 # 2654 <memcpy+0x102>
     39e:	66f000ef          	jal	ra,120c <tft_print>
     3a2:	b321                	j	aa <__riscv_restore_0>

000003a4 <ADC_Display_Draw_Channel_Labels>:
     3a4:	cfdff2ef          	jal	t0,a0 <__riscv_save_0>
     3a8:	1171                	addi	sp,sp,-4
     3aa:	4501                	li	a0,0
     3ac:	573000ef          	jal	ra,111e <tft_set_background_color>
     3b0:	6789                	lui	a5,0x2
     3b2:	69878793          	addi	a5,a5,1688 # 2698 <CSWTCH.3>
     3b6:	4451                	li	s0,20
     3b8:	4485                	li	s1,1
     3ba:	238a                	lhu	a0,0(a5)
     3bc:	c03e                	sw	a5,0(sp)
     3be:	557000ef          	jal	ra,1114 <tft_set_color>
     3c2:	85a2                	mv	a1,s0
     3c4:	4515                	li	a0,5
     3c6:	541000ef          	jal	ra,1106 <tft_set_cursor>
     3ca:	000027b7          	lui	a5,0x2
     3ce:	64c78513          	addi	a0,a5,1612 # 264c <memcpy+0xfa>
     3d2:	63b000ef          	jal	ra,120c <tft_print>
     3d6:	8526                	mv	a0,s1
     3d8:	4585                	li	a1,1
     3da:	653000ef          	jal	ra,122c <tft_print_number>
     3de:	00002537          	lui	a0,0x2
     3e2:	65050513          	addi	a0,a0,1616 # 2650 <memcpy+0xfe>
     3e6:	627000ef          	jal	ra,120c <tft_print>
     3ea:	4782                	lw	a5,0(sp)
     3ec:	0449                	addi	s0,s0,18
     3ee:	0442                	slli	s0,s0,0x10
     3f0:	0485                	addi	s1,s1,1
     3f2:	4695                	li	a3,5
     3f4:	0789                	addi	a5,a5,2
     3f6:	8041                	srli	s0,s0,0x10
     3f8:	fcd491e3          	bne	s1,a3,3ba <ADC_Display_Draw_Channel_Labels+0x16>
     3fc:	0111                	addi	sp,sp,4
     3fe:	b175                	j	aa <__riscv_restore_0>

00000400 <ADC_Display_Init>:
     400:	ca1ff2ef          	jal	t0,a0 <__riscv_save_0>
     404:	200007b7          	lui	a5,0x20000
     408:	02010737          	lui	a4,0x2010
     40c:	04078793          	addi	a5,a5,64 # 20000040 <_edata>
     410:	10070713          	addi	a4,a4,256 # 2010100 <_data_lma+0x200d3a0>
     414:	c398                	sw	a4,0(a5)
     416:	06400713          	li	a4,100
     41a:	a3da                	sh	a4,4(a5)
     41c:	0007a423          	sw	zero,8(a5)
     420:	3fb1                	jal	37c <ADC_Display_Draw_Header>
     422:	3749                	jal	3a4 <ADC_Display_Draw_Channel_Labels>
     424:	b159                	j	aa <__riscv_restore_0>

00000426 <ADC_Display_Draw_Channel_Data>:
     426:	478d                	li	a5,3
     428:	0aa7ed63          	bltu	a5,a0,4e2 <ADC_Display_Draw_Channel_Data+0xbc>
     42c:	c9dd                	beqz	a1,4e2 <ADC_Display_Draw_Channel_Data+0xbc>
     42e:	c73ff2ef          	jal	t0,a0 <__riscv_save_0>
     432:	00351493          	slli	s1,a0,0x3
     436:	94aa                	add	s1,s1,a0
     438:	842a                	mv	s0,a0
     43a:	6531                	lui	a0,0xc
     43c:	1121                	addi	sp,sp,-24
     43e:	61850513          	addi	a0,a0,1560 # c618 <_data_lma+0x98b8>
     442:	c02e                	sw	a1,0(sp)
     444:	4d1000ef          	jal	ra,1114 <tft_set_color>
     448:	4501                	li	a0,0
     44a:	4d5000ef          	jal	ra,111e <tft_set_background_color>
     44e:	200007b7          	lui	a5,0x20000
     452:	04078713          	addi	a4,a5,64 # 20000040 <_edata>
     456:	3318                	lbu	a4,1(a4)
     458:	04078793          	addi	a5,a5,64
     45c:	0486                	slli	s1,s1,0x1
     45e:	c23e                	sw	a5,4(sp)
     460:	04d1                	addi	s1,s1,20
     462:	c30d                	beqz	a4,484 <ADC_Display_Draw_Channel_Data+0x5e>
     464:	85a6                	mv	a1,s1
     466:	03c00513          	li	a0,60
     46a:	49d000ef          	jal	ra,1106 <tft_set_cursor>
     46e:	4702                	lw	a4,0(sp)
     470:	00141793          	slli	a5,s0,0x1
     474:	4641                	li	a2,16
     476:	97ba                	add	a5,a5,a4
     478:	278a                	lhu	a0,8(a5)
     47a:	002c                	addi	a1,sp,8
     47c:	3d65                	jal	334 <ADC_Display_Format_Voltage.part.0>
     47e:	0028                	addi	a0,sp,8
     480:	58d000ef          	jal	ra,120c <tft_print>
     484:	4792                	lw	a5,4(sp)
     486:	23bc                	lbu	a5,2(a5)
     488:	cf85                	beqz	a5,4c0 <ADC_Display_Draw_Channel_Data+0x9a>
     48a:	85a6                	mv	a1,s1
     48c:	06e00513          	li	a0,110
     490:	477000ef          	jal	ra,1106 <tft_set_cursor>
     494:	4702                	lw	a4,0(sp)
     496:	00141793          	slli	a5,s0,0x1
     49a:	00002637          	lui	a2,0x2
     49e:	97ba                	add	a5,a5,a4
     4a0:	239e                	lhu	a5,0(a5)
     4a2:	64460613          	addi	a2,a2,1604 # 2644 <memcpy+0xf2>
     4a6:	45c1                	li	a1,16
     4a8:	00179693          	slli	a3,a5,0x1
     4ac:	96be                	add	a3,a3,a5
     4ae:	068e                	slli	a3,a3,0x3
     4b0:	96be                	add	a3,a3,a5
     4b2:	82a9                	srli	a3,a3,0xa
     4b4:	0028                	addi	a0,sp,8
     4b6:	028020ef          	jal	ra,24de <snprintf>
     4ba:	0028                	addi	a0,sp,8
     4bc:	551000ef          	jal	ra,120c <tft_print>
     4c0:	4792                	lw	a5,4(sp)
     4c2:	239c                	lbu	a5,0(a5)
     4c4:	cf89                	beqz	a5,4de <ADC_Display_Draw_Channel_Data+0xb8>
     4c6:	85a6                	mv	a1,s1
     4c8:	08c00513          	li	a0,140
     4cc:	43b000ef          	jal	ra,1106 <tft_set_cursor>
     4d0:	4782                	lw	a5,0(sp)
     4d2:	0406                	slli	s0,s0,0x1
     4d4:	4591                	li	a1,4
     4d6:	943e                	add	s0,s0,a5
     4d8:	200a                	lhu	a0,0(s0)
     4da:	553000ef          	jal	ra,122c <tft_print_number>
     4de:	0161                	addi	sp,sp,24
     4e0:	b6e9                	j	aa <__riscv_restore_0>
     4e2:	8082                	ret

000004e4 <ADC_Display_Draw_All_Channels>:
     4e4:	bbdff2ef          	jal	t0,a0 <__riscv_save_0>
     4e8:	84aa                	mv	s1,a0
     4ea:	4401                	li	s0,0
     4ec:	8522                	mv	a0,s0
     4ee:	85a6                	mv	a1,s1
     4f0:	3f1d                	jal	426 <ADC_Display_Draw_Channel_Data>
     4f2:	0405                	addi	s0,s0,1
     4f4:	0ff47413          	andi	s0,s0,255
     4f8:	4791                	li	a5,4
     4fa:	fef419e3          	bne	s0,a5,4ec <ADC_Display_Draw_All_Channels+0x8>
     4fe:	b675                	j	aa <__riscv_restore_0>

00000500 <ADC_Display_Clear_Values_Area>:
     500:	ba1ff2ef          	jal	t0,a0 <__riscv_save_0>
     504:	4701                	li	a4,0
     506:	04800693          	li	a3,72
     50a:	06900613          	li	a2,105
     50e:	45d1                	li	a1,20
     510:	03700513          	li	a0,55
     514:	5ff000ef          	jal	ra,1312 <tft_fill_rect>
     518:	be49                	j	aa <__riscv_restore_0>

0000051a <ADC_Display_Should_Update>:
     51a:	b87ff2ef          	jal	t0,a0 <__riscv_save_0>
     51e:	192010ef          	jal	ra,16b0 <Touch_Button_Get_Time_Ms>
     522:	20000737          	lui	a4,0x20000
     526:	04070713          	addi	a4,a4,64 # 20000040 <_edata>
     52a:	471c                	lw	a5,8(a4)
     52c:	8d1d                	sub	a0,a0,a5
     52e:	235e                	lhu	a5,4(a4)
     530:	00f53533          	sltu	a0,a0,a5
     534:	00154513          	xori	a0,a0,1
     538:	be8d                	j	aa <__riscv_restore_0>

0000053a <ADC_Display_Update>:
     53a:	c915                	beqz	a0,56e <ADC_Display_Update+0x34>
     53c:	b65ff2ef          	jal	t0,a0 <__riscv_save_0>
     540:	84ae                	mv	s1,a1
     542:	842a                	mv	s0,a0
     544:	3fd9                	jal	51a <ADC_Display_Should_Update>
     546:	cd01                	beqz	a0,55e <ADC_Display_Update+0x24>
     548:	4785                	li	a5,1
     54a:	02f48063          	beq	s1,a5,56a <ADC_Display_Update+0x30>
     54e:	c889                	beqz	s1,560 <ADC_Display_Update+0x26>
     550:	4789                	li	a5,2
     552:	00f48963          	beq	s1,a5,564 <ADC_Display_Update+0x2a>
     556:	15a010ef          	jal	ra,16b0 <Touch_Button_Get_Time_Ms>
     55a:	80a1a423          	sw	a0,-2040(gp) # 20000048 <_edata+0x8>
     55e:	b6b1                	j	aa <__riscv_restore_0>
     560:	3d31                	jal	37c <ADC_Display_Draw_Header>
     562:	3589                	jal	3a4 <ADC_Display_Draw_Channel_Labels>
     564:	8522                	mv	a0,s0
     566:	3fbd                	jal	4e4 <ADC_Display_Draw_All_Channels>
     568:	b7fd                	j	556 <ADC_Display_Update+0x1c>
     56a:	3f59                	jal	500 <ADC_Display_Clear_Values_Area>
     56c:	bfe5                	j	564 <ADC_Display_Update+0x2a>
     56e:	8082                	ret

00000570 <ADC_Display_Draw_Logo_Border>:
     570:	b31ff2ef          	jal	t0,a0 <__riscv_save_0>
     574:	4a000713          	li	a4,1184
     578:	05000693          	li	a3,80
     57c:	0a000613          	li	a2,160
     580:	4581                	li	a1,0
     582:	4501                	li	a0,0
     584:	58f000ef          	jal	ra,1312 <tft_fill_rect>
     588:	4701                	li	a4,0
     58a:	04a00693          	li	a3,74
     58e:	09a00613          	li	a2,154
     592:	458d                	li	a1,3
     594:	450d                	li	a0,3
     596:	57d000ef          	jal	ra,1312 <tft_fill_rect>
     59a:	7ff00713          	li	a4,2047
     59e:	4685                	li	a3,1
     5a0:	09600613          	li	a2,150
     5a4:	4595                	li	a1,5
     5a6:	4515                	li	a0,5
     5a8:	56b000ef          	jal	ra,1312 <tft_fill_rect>
     5ac:	7ff00713          	li	a4,2047
     5b0:	4685                	li	a3,1
     5b2:	09600613          	li	a2,150
     5b6:	04a00593          	li	a1,74
     5ba:	4515                	li	a0,5
     5bc:	557000ef          	jal	ra,1312 <tft_fill_rect>
     5c0:	7ff00713          	li	a4,2047
     5c4:	04600693          	li	a3,70
     5c8:	4605                	li	a2,1
     5ca:	4595                	li	a1,5
     5cc:	4515                	li	a0,5
     5ce:	545000ef          	jal	ra,1312 <tft_fill_rect>
     5d2:	7ff00713          	li	a4,2047
     5d6:	04600693          	li	a3,70
     5da:	4605                	li	a2,1
     5dc:	4595                	li	a1,5
     5de:	09a00513          	li	a0,154
     5e2:	531000ef          	jal	ra,1312 <tft_fill_rect>
     5e6:	b4d1                	j	aa <__riscv_restore_0>

000005e8 <ADC_Display_Draw_Logo_Channels>:
     5e8:	c151                	beqz	a0,66c <ADC_Display_Draw_Logo_Channels+0x84>
     5ea:	ab7ff2ef          	jal	t0,a0 <__riscv_save_0>
     5ee:	1141                	addi	sp,sp,-16
     5f0:	c02a                	sw	a0,0(sp)
     5f2:	7ff00513          	li	a0,2047
     5f6:	31f000ef          	jal	ra,1114 <tft_set_color>
     5fa:	4501                	li	a0,0
     5fc:	323000ef          	jal	ra,111e <tft_set_background_color>
     600:	4405                	li	s0,1
     602:	000024b7          	lui	s1,0x2
     606:	00241793          	slli	a5,s0,0x2
     60a:	97a2                	add	a5,a5,s0
     60c:	0786                	slli	a5,a5,0x1
     60e:	07e5                	addi	a5,a5,25
     610:	07c2                	slli	a5,a5,0x10
     612:	83c1                	srli	a5,a5,0x10
     614:	85be                	mv	a1,a5
     616:	4529                	li	a0,10
     618:	c23e                	sw	a5,4(sp)
     61a:	2ed000ef          	jal	ra,1106 <tft_set_cursor>
     61e:	86a2                	mv	a3,s0
     620:	66048613          	addi	a2,s1,1632 # 2660 <memcpy+0x10e>
     624:	45a1                	li	a1,8
     626:	0028                	addi	a0,sp,8
     628:	6b7010ef          	jal	ra,24de <snprintf>
     62c:	0028                	addi	a0,sp,8
     62e:	3df000ef          	jal	ra,120c <tft_print>
     632:	4792                	lw	a5,4(sp)
     634:	02300513          	li	a0,35
     638:	85be                	mv	a1,a5
     63a:	2cd000ef          	jal	ra,1106 <tft_set_cursor>
     63e:	4782                	lw	a5,0(sp)
     640:	97a2                	add	a5,a5,s0
     642:	37f4                	lbu	a3,15(a5)
     644:	ce99                	beqz	a3,662 <ADC_Display_Draw_Logo_Channels+0x7a>
     646:	97a2                	add	a5,a5,s0
     648:	23ea                	lhu	a0,6(a5)
     64a:	4621                	li	a2,8
     64c:	002c                	addi	a1,sp,8
     64e:	31dd                	jal	334 <ADC_Display_Format_Voltage.part.0>
     650:	0028                	addi	a0,sp,8
     652:	3bb000ef          	jal	ra,120c <tft_print>
     656:	0405                	addi	s0,s0,1
     658:	4795                	li	a5,5
     65a:	faf416e3          	bne	s0,a5,606 <ADC_Display_Draw_Logo_Channels+0x1e>
     65e:	0141                	addi	sp,sp,16
     660:	b4a9                	j	aa <__riscv_restore_0>
     662:	00002537          	lui	a0,0x2
     666:	66850513          	addi	a0,a0,1640 # 2668 <memcpy+0x116>
     66a:	b7e5                	j	652 <ADC_Display_Draw_Logo_Channels+0x6a>
     66c:	8082                	ret

0000066e <ADC_Display_Draw_Logo_Main_Voltage>:
     66e:	a33ff2ef          	jal	t0,a0 <__riscv_save_0>
     672:	842a                	mv	s0,a0
     674:	6541                	lui	a0,0x10
     676:	1141                	addi	sp,sp,-16
     678:	157d                	addi	a0,a0,-1
     67a:	29b000ef          	jal	ra,1114 <tft_set_color>
     67e:	4501                	li	a0,0
     680:	29f000ef          	jal	ra,111e <tft_set_background_color>
     684:	3e800593          	li	a1,1000
     688:	8522                	mv	a0,s0
     68a:	3c9d                	jal	100 <__umodsi3>
     68c:	01051713          	slli	a4,a0,0x10
     690:	8341                	srli	a4,a4,0x10
     692:	3e800593          	li	a1,1000
     696:	8522                	mv	a0,s0
     698:	c03a                	sw	a4,0(sp)
     69a:	3c2d                	jal	d4 <__udivsi3>
     69c:	4702                	lw	a4,0(sp)
     69e:	01051693          	slli	a3,a0,0x10
     6a2:	00002637          	lui	a2,0x2
     6a6:	82c1                	srli	a3,a3,0x10
     6a8:	68060613          	addi	a2,a2,1664 # 2680 <memcpy+0x12e>
     6ac:	45b1                	li	a1,12
     6ae:	0048                	addi	a0,sp,4
     6b0:	62f010ef          	jal	ra,24de <snprintf>
     6b4:	02d00593          	li	a1,45
     6b8:	06400513          	li	a0,100
     6bc:	24b000ef          	jal	ra,1106 <tft_set_cursor>
     6c0:	0048                	addi	a0,sp,4
     6c2:	34b000ef          	jal	ra,120c <tft_print>
     6c6:	7ff00713          	li	a4,2047
     6ca:	4689                	li	a3,2
     6cc:	4609                	li	a2,2
     6ce:	03200593          	li	a1,50
     6d2:	09100513          	li	a0,145
     6d6:	43d000ef          	jal	ra,1312 <tft_fill_rect>
     6da:	0141                	addi	sp,sp,16
     6dc:	9cfff06f          	j	aa <__riscv_restore_0>

000006e0 <ADC_Display_Draw_Vietnam_Flag>:
     6e0:	9c1ff2ef          	jal	t0,a0 <__riscv_save_0>
     6e4:	6739                	lui	a4,0xe
     6e6:	1101                	addi	sp,sp,-32
     6e8:	92370713          	addi	a4,a4,-1757 # d923 <_data_lma+0xabc3>
     6ec:	c836                	sw	a3,16(sp)
     6ee:	c22a                	sw	a0,4(sp)
     6f0:	c42e                	sw	a1,8(sp)
     6f2:	c632                	sw	a2,12(sp)
     6f4:	41f000ef          	jal	ra,1312 <tft_fill_rect>
     6f8:	47b2                	lw	a5,12(sp)
     6fa:	4712                	lw	a4,4(sp)
     6fc:	46c2                	lw	a3,16(sp)
     6fe:	8385                	srli	a5,a5,0x1
     700:	97ba                	add	a5,a5,a4
     702:	07c2                	slli	a5,a5,0x10
     704:	83c1                	srli	a5,a5,0x10
     706:	c03e                	sw	a5,0(sp)
     708:	47c2                	lw	a5,16(sp)
     70a:	4732                	lw	a4,12(sp)
     70c:	0017d413          	srli	s0,a5,0x1
     710:	47a2                	lw	a5,8(sp)
     712:	943e                	add	s0,s0,a5
     714:	0442                	slli	s0,s0,0x10
     716:	8041                	srli	s0,s0,0x10
     718:	863c                	lhu	a5,12(sp)
     71a:	00e6f363          	bgeu	a3,a4,720 <ADC_Display_Draw_Vietnam_Flag+0x40>
     71e:	80bc                	lhu	a5,16(sp)
     720:	07c2                	slli	a5,a5,0x10
     722:	83c1                	srli	a5,a5,0x10
     724:	458d                	li	a1,3
     726:	853e                	mv	a0,a5
     728:	cc3e                	sw	a5,24(sp)
     72a:	9a3ff0ef          	jal	ra,cc <__divsi3>
     72e:	4782                	lw	a5,0(sp)
     730:	01051493          	slli	s1,a0,0x10
     734:	80c1                	srli	s1,s1,0x10
     736:	0014d313          	srli	t1,s1,0x1
     73a:	406405b3          	sub	a1,s0,t1
     73e:	fff78513          	addi	a0,a5,-1
     742:	62c1                	lui	t0,0x10
     744:	05c2                	slli	a1,a1,0x10
     746:	0542                	slli	a0,a0,0x10
     748:	fe028713          	addi	a4,t0,-32 # ffe0 <_data_lma+0xd280>
     74c:	86a6                	mv	a3,s1
     74e:	4609                	li	a2,2
     750:	81c1                	srli	a1,a1,0x10
     752:	8141                	srli	a0,a0,0x10
     754:	ca1a                	sw	t1,20(sp)
     756:	3bd000ef          	jal	ra,1312 <tft_fill_rect>
     75a:	4782                	lw	a5,0(sp)
     75c:	4352                	lw	t1,20(sp)
     75e:	fff40593          	addi	a1,s0,-1
     762:	62c1                	lui	t0,0x10
     764:	40678533          	sub	a0,a5,t1
     768:	05c2                	slli	a1,a1,0x10
     76a:	0542                	slli	a0,a0,0x10
     76c:	fe028713          	addi	a4,t0,-32 # ffe0 <_data_lma+0xd280>
     770:	4689                	li	a3,2
     772:	8626                	mv	a2,s1
     774:	81c1                	srli	a1,a1,0x10
     776:	8141                	srli	a0,a0,0x10
     778:	39b000ef          	jal	ra,1312 <tft_fill_rect>
     77c:	47e2                	lw	a5,24(sp)
     77e:	472d                	li	a4,11
     780:	08f77163          	bgeu	a4,a5,802 <ADC_Display_Draw_Vietnam_Flag+0x122>
     784:	853e                	mv	a0,a5
     786:	55dd                	li	a1,-9
     788:	945ff0ef          	jal	ra,cc <__divsi3>
     78c:	ca2a                	sw	a0,20(sp)
     78e:	458d                	li	a1,3
     790:	8526                	mv	a0,s1
     792:	943ff0ef          	jal	ra,d4 <__udivsi3>
     796:	47d2                	lw	a5,20(sp)
     798:	46b2                	lw	a3,12(sp)
     79a:	4612                	lw	a2,4(sp)
     79c:	01051493          	slli	s1,a0,0x10
     7a0:	80c1                	srli	s1,s1,0x10
     7a2:	873e                	mv	a4,a5
     7a4:	00c68333          	add	t1,a3,a2
     7a8:	06e4d063          	bge	s1,a4,808 <ADC_Display_Draw_Vietnam_Flag+0x128>
     7ac:	4732                	lw	a4,12(sp)
     7ae:	4692                	lw	a3,4(sp)
     7b0:	96ba                	add	a3,a3,a4
     7b2:	4702                	lw	a4,0(sp)
     7b4:	4612                	lw	a2,4(sp)
     7b6:	8f1d                	sub	a4,a4,a5
     7b8:	04c74263          	blt	a4,a2,7fc <ADC_Display_Draw_Vietnam_Flag+0x11c>
     7bc:	04d75063          	bge	a4,a3,7fc <ADC_Display_Draw_Vietnam_Flag+0x11c>
     7c0:	4622                	lw	a2,8(sp)
     7c2:	00f40733          	add	a4,s0,a5
     7c6:	02c74b63          	blt	a4,a2,7fc <ADC_Display_Draw_Vietnam_Flag+0x11c>
     7ca:	4642                	lw	a2,16(sp)
     7cc:	45a2                	lw	a1,8(sp)
     7ce:	962e                	add	a2,a2,a1
     7d0:	02c75663          	bge	a4,a2,7fc <ADC_Display_Draw_Vietnam_Flag+0x11c>
     7d4:	01079513          	slli	a0,a5,0x10
     7d8:	c63e                	sw	a5,12(sp)
     7da:	4782                	lw	a5,0(sp)
     7dc:	8141                	srli	a0,a0,0x10
     7de:	008505b3          	add	a1,a0,s0
     7e2:	40a78533          	sub	a0,a5,a0
     7e6:	6641                	lui	a2,0x10
     7e8:	05c2                	slli	a1,a1,0x10
     7ea:	0542                	slli	a0,a0,0x10
     7ec:	1601                	addi	a2,a2,-32
     7ee:	81c1                	srli	a1,a1,0x10
     7f0:	8141                	srli	a0,a0,0x10
     7f2:	ca36                	sw	a3,20(sp)
     7f4:	2e3000ef          	jal	ra,12d6 <tft_draw_pixel>
     7f8:	46d2                	lw	a3,20(sp)
     7fa:	47b2                	lw	a5,12(sp)
     7fc:	0785                	addi	a5,a5,1
     7fe:	faf4dae3          	bge	s1,a5,7b2 <ADC_Display_Draw_Vietnam_Flag+0xd2>
     802:	6105                	addi	sp,sp,32
     804:	8a7ff06f          	j	aa <__riscv_restore_0>
     808:	4682                	lw	a3,0(sp)
     80a:	4612                	lw	a2,4(sp)
     80c:	96ba                	add	a3,a3,a4
     80e:	04c6c363          	blt	a3,a2,854 <ADC_Display_Draw_Vietnam_Flag+0x174>
     812:	0466d163          	bge	a3,t1,854 <ADC_Display_Draw_Vietnam_Flag+0x174>
     816:	4622                	lw	a2,8(sp)
     818:	00e406b3          	add	a3,s0,a4
     81c:	02c6cc63          	blt	a3,a2,854 <ADC_Display_Draw_Vietnam_Flag+0x174>
     820:	4642                	lw	a2,16(sp)
     822:	45a2                	lw	a1,8(sp)
     824:	962e                	add	a2,a2,a1
     826:	02c6d763          	bge	a3,a2,854 <ADC_Display_Draw_Vietnam_Flag+0x174>
     82a:	cc3e                	sw	a5,24(sp)
     82c:	4782                	lw	a5,0(sp)
     82e:	01071513          	slli	a0,a4,0x10
     832:	8141                	srli	a0,a0,0x10
     834:	00a405b3          	add	a1,s0,a0
     838:	953e                	add	a0,a0,a5
     83a:	6641                	lui	a2,0x10
     83c:	05c2                	slli	a1,a1,0x10
     83e:	0542                	slli	a0,a0,0x10
     840:	1601                	addi	a2,a2,-32
     842:	81c1                	srli	a1,a1,0x10
     844:	8141                	srli	a0,a0,0x10
     846:	ce1a                	sw	t1,28(sp)
     848:	ca3a                	sw	a4,20(sp)
     84a:	28d000ef          	jal	ra,12d6 <tft_draw_pixel>
     84e:	4372                	lw	t1,28(sp)
     850:	47e2                	lw	a5,24(sp)
     852:	4752                	lw	a4,20(sp)
     854:	0705                	addi	a4,a4,1
     856:	bf89                	j	7a8 <ADC_Display_Draw_Vietnam_Flag+0xc8>

00000858 <ADC_Display_Draw_Logo_Header>:
     858:	849ff2ef          	jal	t0,a0 <__riscv_save_0>
     85c:	7ff00513          	li	a0,2047
     860:	0b5000ef          	jal	ra,1114 <tft_set_color>
     864:	4501                	li	a0,0
     866:	0b9000ef          	jal	ra,111e <tft_set_background_color>
     86a:	46b1                	li	a3,12
     86c:	4651                	li	a2,20
     86e:	45a9                	li	a1,10
     870:	4529                	li	a0,10
     872:	35bd                	jal	6e0 <ADC_Display_Draw_Vietnam_Flag>
     874:	45a9                	li	a1,10
     876:	02300513          	li	a0,35
     87a:	08d000ef          	jal	ra,1106 <tft_set_cursor>
     87e:	00002537          	lui	a0,0x2
     882:	67050513          	addi	a0,a0,1648 # 2670 <memcpy+0x11e>
     886:	187000ef          	jal	ra,120c <tft_print>
     88a:	45d1                	li	a1,20
     88c:	02300513          	li	a0,35
     890:	077000ef          	jal	ra,1106 <tft_set_cursor>
     894:	00002537          	lui	a0,0x2
     898:	67850513          	addi	a0,a0,1656 # 2678 <memcpy+0x126>
     89c:	171000ef          	jal	ra,120c <tft_print>
     8a0:	7ff00713          	li	a4,2047
     8a4:	4685                	li	a3,1
     8a6:	4679                	li	a2,30
     8a8:	45b1                	li	a1,12
     8aa:	05a00513          	li	a0,90
     8ae:	265000ef          	jal	ra,1312 <tft_fill_rect>
     8b2:	7ff00713          	li	a4,2047
     8b6:	4685                	li	a3,1
     8b8:	4665                	li	a2,25
     8ba:	45bd                	li	a1,15
     8bc:	05a00513          	li	a0,90
     8c0:	253000ef          	jal	ra,1312 <tft_fill_rect>
     8c4:	7ff00713          	li	a4,2047
     8c8:	4685                	li	a3,1
     8ca:	4651                	li	a2,20
     8cc:	45c9                	li	a1,18
     8ce:	05a00513          	li	a0,90
     8d2:	241000ef          	jal	ra,1312 <tft_fill_rect>
     8d6:	fd4ff06f          	j	aa <__riscv_restore_0>

000008da <ADC_Display_Draw_Logo_Style>:
     8da:	fc6ff2ef          	jal	t0,a0 <__riscv_save_0>
     8de:	842a                	mv	s0,a0
     8e0:	4a000713          	li	a4,1184
     8e4:	4501                	li	a0,0
     8e6:	05000693          	li	a3,80
     8ea:	0a000613          	li	a2,160
     8ee:	4581                	li	a1,0
     8f0:	223000ef          	jal	ra,1312 <tft_fill_rect>
     8f4:	39b5                	jal	570 <ADC_Display_Draw_Logo_Border>
     8f6:	378d                	jal	858 <ADC_Display_Draw_Logo_Header>
     8f8:	8522                	mv	a0,s0
     8fa:	31fd                	jal	5e8 <ADC_Display_Draw_Logo_Channels>
     8fc:	c409                	beqz	s0,906 <ADC_Display_Draw_Logo_Style+0x2c>
     8fe:	281c                	lbu	a5,16(s0)
     900:	c399                	beqz	a5,906 <ADC_Display_Draw_Logo_Style+0x2c>
     902:	240a                	lhu	a0,8(s0)
     904:	33ad                	jal	66e <ADC_Display_Draw_Logo_Main_Voltage>
     906:	fa4ff06f          	j	aa <__riscv_restore_0>

0000090a <NMI_Handler>:
     90a:	a001                	j	90a <NMI_Handler>

0000090c <HardFault_Handler>:
     90c:	beef07b7          	lui	a5,0xbeef0
     910:	e000e737          	lui	a4,0xe000e
     914:	08078793          	addi	a5,a5,128 # beef0080 <__global_pointer$+0x9eeef840>
     918:	c73c                	sw	a5,72(a4)
     91a:	a001                	j	91a <HardFault_Handler+0xe>

0000091c <EXTI7_0_IRQHandler>:
     91c:	59b000ef          	jal	ra,16b6 <Touch_Button_IRQ_Handler>
     920:	30200073          	mret

00000924 <TIM2_IRQHandler>:
     924:	4585                	li	a1,1
     926:	40000537          	lui	a0,0x40000
     92a:	3a2010ef          	jal	ra,1ccc <TIM_GetITStatus>
     92e:	c919                	beqz	a0,944 <TIM2_IRQHandler+0x20>
     930:	98c1a783          	lw	a5,-1652(gp) # 200001cc <system_tick_ms>
     934:	4585                	li	a1,1
     936:	40000537          	lui	a0,0x40000
     93a:	0785                	addi	a5,a5,1
     93c:	98f1a623          	sw	a5,-1652(gp) # 200001cc <system_tick_ms>
     940:	3a4010ef          	jal	ra,1ce4 <TIM_ClearITPendingBit>
     944:	30200073          	mret

00000948 <Display_Control_Update>:
     948:	f58ff2ef          	jal	t0,a0 <__riscv_save_0>
     94c:	80c18413          	addi	s0,gp,-2036 # 2000004c <display_control>
     950:	205c                	lbu	a5,4(s0)
     952:	cb89                	beqz	a5,964 <Display_Control_Update+0x1c>
     954:	21ed                	jal	e3e <PWM_Update_Fade>
     956:	401c                	lw	a5,0(s0)
     958:	4705                	li	a4,1
     95a:	00e78763          	beq	a5,a4,968 <Display_Control_Update+0x20>
     95e:	470d                	li	a4,3
     960:	00e78b63          	beq	a5,a4,976 <Display_Control_Update+0x2e>
     964:	f46ff06f          	j	aa <__riscv_restore_0>
     968:	8381c703          	lbu	a4,-1992(gp) # 20000078 <pwm_control+0x4>
     96c:	ff65                	bnez	a4,964 <Display_Control_Update+0x1c>
     96e:	4709                	li	a4,2
     970:	c018                	sw	a4,0(s0)
     972:	b05c                	sb	a5,5(s0)
     974:	bfc5                	j	964 <Display_Control_Update+0x1c>
     976:	8381c783          	lbu	a5,-1992(gp) # 20000078 <pwm_control+0x4>
     97a:	f7ed                	bnez	a5,964 <Display_Control_Update+0x1c>
     97c:	2b0d                	jal	eae <PWM_Get_Brightness>
     97e:	f17d                	bnez	a0,964 <Display_Control_Update+0x1c>
     980:	00042023          	sw	zero,0(s0)
     984:	b7c5                	j	964 <Display_Control_Update+0x1c>

00000986 <Display_Control_Turn_On>:
     986:	80c18793          	addi	a5,gp,-2036 # 2000004c <display_control>
     98a:	4398                	lw	a4,0(a5)
     98c:	eb01                	bnez	a4,99c <Display_Control_Turn_On+0x16>
     98e:	f12ff2ef          	jal	t0,a0 <__riscv_save_0>
     992:	4705                	li	a4,1
     994:	c398                	sw	a4,0(a5)
     996:	29ed                	jal	e90 <PWM_Turn_On>
     998:	f12ff06f          	j	aa <__riscv_restore_0>
     99c:	8082                	ret

0000099e <Display_Control_Turn_Off>:
     99e:	80c18793          	addi	a5,gp,-2036 # 2000004c <display_control>
     9a2:	4394                	lw	a3,0(a5)
     9a4:	4709                	li	a4,2
     9a6:	00e69963          	bne	a3,a4,9b8 <Display_Control_Turn_Off+0x1a>
     9aa:	ef6ff2ef          	jal	t0,a0 <__riscv_save_0>
     9ae:	470d                	li	a4,3
     9b0:	c398                	sw	a4,0(a5)
     9b2:	21fd                	jal	ea0 <PWM_Turn_Off>
     9b4:	ef6ff06f          	j	aa <__riscv_restore_0>
     9b8:	8082                	ret

000009ba <Display_Control_Toggle>:
     9ba:	ee6ff2ef          	jal	t0,a0 <__riscv_save_0>
     9be:	80c1a783          	lw	a5,-2036(gp) # 2000004c <display_control>
     9c2:	e781                	bnez	a5,9ca <Display_Control_Toggle+0x10>
     9c4:	37c9                	jal	986 <Display_Control_Turn_On>
     9c6:	ee4ff06f          	j	aa <__riscv_restore_0>
     9ca:	4709                	li	a4,2
     9cc:	fee79de3          	bne	a5,a4,9c6 <Display_Control_Toggle+0xc>
     9d0:	37f9                	jal	99e <Display_Control_Turn_Off>
     9d2:	bfd5                	j	9c6 <Display_Control_Toggle+0xc>

000009d4 <Display_Control_Is_On>:
     9d4:	80c1a503          	lw	a0,-2036(gp) # 2000004c <display_control>
     9d8:	157d                	addi	a0,a0,-1
     9da:	00253513          	sltiu	a0,a0,2
     9de:	8082                	ret

000009e0 <Display_Control_Clear_Screen>:
     9e0:	ec0ff2ef          	jal	t0,a0 <__riscv_save_0>
     9e4:	4701                	li	a4,0
     9e6:	05000693          	li	a3,80
     9ea:	0a000613          	li	a2,160
     9ee:	4581                	li	a1,0
     9f0:	4501                	li	a0,0
     9f2:	121000ef          	jal	ra,1312 <tft_fill_rect>
     9f6:	eb4ff06f          	j	aa <__riscv_restore_0>

000009fa <Display_Control_Show_Startup_Message>:
     9fa:	ea6ff2ef          	jal	t0,a0 <__riscv_save_0>
     9fe:	37cd                	jal	9e0 <Display_Control_Clear_Screen>
     a00:	6441                	lui	s0,0x10
     a02:	fff40513          	addi	a0,s0,-1 # ffff <_data_lma+0xd29f>
     a06:	70e000ef          	jal	ra,1114 <tft_set_color>
     a0a:	4501                	li	a0,0
     a0c:	712000ef          	jal	ra,111e <tft_set_background_color>
     a10:	45a9                	li	a1,10
     a12:	4529                	li	a0,10
     a14:	6f2000ef          	jal	ra,1106 <tft_set_cursor>
     a18:	00002537          	lui	a0,0x2
     a1c:	6a050513          	addi	a0,a0,1696 # 26a0 <CSWTCH.3+0x8>
     a20:	7ec000ef          	jal	ra,120c <tft_print>
     a24:	45e5                	li	a1,25
     a26:	4529                	li	a0,10
     a28:	6de000ef          	jal	ra,1106 <tft_set_cursor>
     a2c:	00002537          	lui	a0,0x2
     a30:	6b050513          	addi	a0,a0,1712 # 26b0 <CSWTCH.3+0x18>
     a34:	7d8000ef          	jal	ra,120c <tft_print>
     a38:	02d00593          	li	a1,45
     a3c:	4515                	li	a0,5
     a3e:	6c8000ef          	jal	ra,1106 <tft_set_cursor>
     a42:	fe040513          	addi	a0,s0,-32
     a46:	6ce000ef          	jal	ra,1114 <tft_set_color>
     a4a:	00002537          	lui	a0,0x2
     a4e:	6c050513          	addi	a0,a0,1728 # 26c0 <CSWTCH.3+0x28>
     a52:	7ba000ef          	jal	ra,120c <tft_print>
     a56:	03c00593          	li	a1,60
     a5a:	4515                	li	a0,5
     a5c:	6aa000ef          	jal	ra,1106 <tft_set_cursor>
     a60:	00002537          	lui	a0,0x2
     a64:	6d050513          	addi	a0,a0,1744 # 26d0 <CSWTCH.3+0x38>
     a68:	7a4000ef          	jal	ra,120c <tft_print>
     a6c:	e3eff06f          	j	aa <__riscv_restore_0>

00000a70 <Display_Control_Init>:
     a70:	e30ff2ef          	jal	t0,a0 <__riscv_save_0>
     a74:	80c18413          	addi	s0,gp,-2036 # 2000004c <display_control>
     a78:	10000793          	li	a5,256
     a7c:	a05e                	sh	a5,4(s0)
     a7e:	00042023          	sw	zero,0(s0)
     a82:	00042423          	sw	zero,8(s0)
     a86:	29c5                	jal	f76 <tft_init>
     a88:	3fa1                	jal	9e0 <Display_Control_Clear_Screen>
     a8a:	3f85                	jal	9fa <Display_Control_Show_Startup_Message>
     a8c:	4785                	li	a5,1
     a8e:	a05c                	sb	a5,4(s0)
     a90:	2101                	jal	e90 <PWM_Turn_On>
     a92:	e18ff06f          	j	aa <__riscv_restore_0>

00000a96 <Display_Text_Custom>:
     a96:	e0aff2ef          	jal	t0,a0 <__riscv_save_0>
     a9a:	1151                	addi	sp,sp,-12
     a9c:	842a                	mv	s0,a0
     a9e:	8536                	mv	a0,a3
     aa0:	c43e                	sw	a5,8(sp)
     aa2:	c02e                	sw	a1,0(sp)
     aa4:	84b2                	mv	s1,a2
     aa6:	c23a                	sw	a4,4(sp)
     aa8:	66c000ef          	jal	ra,1114 <tft_set_color>
     aac:	4712                	lw	a4,4(sp)
     aae:	853a                	mv	a0,a4
     ab0:	66e000ef          	jal	ra,111e <tft_set_background_color>
     ab4:	47a2                	lw	a5,8(sp)
     ab6:	4712                	lw	a4,4(sp)
     ab8:	cb89                	beqz	a5,aca <Display_Text_Custom+0x34>
     aba:	05000693          	li	a3,80
     abe:	0a000613          	li	a2,160
     ac2:	4581                	li	a1,0
     ac4:	4501                	li	a0,0
     ac6:	04d000ef          	jal	ra,1312 <tft_fill_rect>
     aca:	4502                	lw	a0,0(sp)
     acc:	85a6                	mv	a1,s1
     ace:	638000ef          	jal	ra,1106 <tft_set_cursor>
     ad2:	8522                	mv	a0,s0
     ad4:	738000ef          	jal	ra,120c <tft_print>
     ad8:	0131                	addi	sp,sp,12
     ada:	dd0ff06f          	j	aa <__riscv_restore_0>

00000ade <Display_Text_Centered>:
     ade:	dc2ff2ef          	jal	t0,a0 <__riscv_save_0>
     ae2:	87ba                	mv	a5,a4
     ae4:	4301                	li	t1,0
     ae6:	00130293          	addi	t0,t1,1
     aea:	00550733          	add	a4,a0,t0
     aee:	fff70703          	lb	a4,-1(a4) # e000dfff <__global_pointer$+0xc000d7bf>
     af2:	e71d                	bnez	a4,b20 <Display_Text_Centered+0x42>
     af4:	0342                	slli	t1,t1,0x10
     af6:	01035313          	srli	t1,t1,0x10
     afa:	00231713          	slli	a4,t1,0x2
     afe:	40e30333          	sub	t1,t1,a4
     b02:	05030313          	addi	t1,t1,80
     b06:	0342                	slli	t1,t1,0x10
     b08:	01035313          	srli	t1,t1,0x10
     b0c:	e199                	bnez	a1,b12 <Display_Text_Centered+0x34>
     b0e:	02400593          	li	a1,36
     b12:	8736                	mv	a4,a3
     b14:	86b2                	mv	a3,a2
     b16:	862e                	mv	a2,a1
     b18:	859a                	mv	a1,t1
     b1a:	3fb5                	jal	a96 <Display_Text_Custom>
     b1c:	d8eff06f          	j	aa <__riscv_restore_0>
     b20:	8316                	mv	t1,t0
     b22:	b7d1                	j	ae6 <Display_Text_Centered+0x8>

00000b24 <Display_Text_Clear_Screen>:
     b24:	d7cff2ef          	jal	t0,a0 <__riscv_save_0>
     b28:	872a                	mv	a4,a0
     b2a:	05000693          	li	a3,80
     b2e:	0a000613          	li	a2,160
     b32:	4581                	li	a1,0
     b34:	4501                	li	a0,0
     b36:	7dc000ef          	jal	ra,1312 <tft_fill_rect>
     b3a:	d70ff06f          	j	aa <__riscv_restore_0>

00000b3e <Display_Text_Welcome>:
     b3e:	d62ff2ef          	jal	t0,a0 <__riscv_save_0>
     b42:	4501                	li	a0,0
     b44:	37c5                	jal	b24 <Display_Text_Clear_Screen>
     b46:	00002537          	lui	a0,0x2
     b4a:	4701                	li	a4,0
     b4c:	4681                	li	a3,0
     b4e:	7ff00613          	li	a2,2047
     b52:	45d1                	li	a1,20
     b54:	6e850513          	addi	a0,a0,1768 # 26e8 <CSWTCH.3+0x50>
     b58:	3759                	jal	ade <Display_Text_Centered>
     b5a:	6441                	lui	s0,0x10
     b5c:	00002537          	lui	a0,0x2
     b60:	fff40613          	addi	a2,s0,-1 # ffff <_data_lma+0xd29f>
     b64:	4701                	li	a4,0
     b66:	4681                	li	a3,0
     b68:	02300593          	li	a1,35
     b6c:	65450513          	addi	a0,a0,1620 # 2654 <memcpy+0x102>
     b70:	37bd                	jal	ade <Display_Text_Centered>
     b72:	00002537          	lui	a0,0x2
     b76:	4701                	li	a4,0
     b78:	4681                	li	a3,0
     b7a:	fe040613          	addi	a2,s0,-32
     b7e:	03200593          	li	a1,50
     b82:	6e050513          	addi	a0,a0,1760 # 26e0 <CSWTCH.3+0x48>
     b86:	3fa1                	jal	ade <Display_Text_Centered>
     b88:	d22ff06f          	j	aa <__riscv_restore_0>

00000b8c <System_Init>:
     b8c:	d14ff2ef          	jal	t0,a0 <__riscv_save_0>
     b90:	ea4ff0ef          	jal	ra,234 <ADC_Config_Init>
     b94:	00002537          	lui	a0,0x2
     b98:	6f450513          	addi	a0,a0,1780 # 26f4 <CSWTCH.3+0x5c>
     b9c:	16d010ef          	jal	ra,2508 <puts>
     ba0:	2cb5                	jal	e1c <PWM_Config_Init>
     ba2:	00002537          	lui	a0,0x2
     ba6:	70850513          	addi	a0,a0,1800 # 2708 <CSWTCH.3+0x70>
     baa:	15f010ef          	jal	ra,2508 <puts>
     bae:	25b000ef          	jal	ra,1608 <Touch_Button_Init>
     bb2:	00002537          	lui	a0,0x2
     bb6:	71c50513          	addi	a0,a0,1820 # 271c <CSWTCH.3+0x84>
     bba:	14f010ef          	jal	ra,2508 <puts>
     bbe:	3d4d                	jal	a70 <Display_Control_Init>
     bc0:	00002537          	lui	a0,0x2
     bc4:	73850513          	addi	a0,a0,1848 # 2738 <CSWTCH.3+0xa0>
     bc8:	141010ef          	jal	ra,2508 <puts>
     bcc:	835ff0ef          	jal	ra,400 <ADC_Display_Init>
     bd0:	00002537          	lui	a0,0x2
     bd4:	75850513          	addi	a0,a0,1880 # 2758 <CSWTCH.3+0xc0>
     bd8:	131010ef          	jal	ra,2508 <puts>
     bdc:	00002537          	lui	a0,0x2
     be0:	4705                	li	a4,1
     be2:	77450513          	addi	a0,a0,1908 # 2774 <CSWTCH.3+0xdc>
     be6:	82e18823          	sb	a4,-2000(gp) # 20000070 <system_initialized>
     bea:	11f010ef          	jal	ra,2508 <puts>
     bee:	cbcff06f          	j	aa <__riscv_restore_0>

00000bf2 <main>:
     bf2:	caeff2ef          	jal	t0,a0 <__riscv_save_0>
     bf6:	1121                	addi	sp,sp,-24
     bf8:	4505                	li	a0,1
     bfa:	657000ef          	jal	ra,1a50 <NVIC_PriorityGroupConfig>
     bfe:	0d7000ef          	jal	ra,14d4 <SystemCoreClockUpdate>
     c02:	1e4010ef          	jal	ra,1de6 <Delay_Init>
     c06:	6571                	lui	a0,0x1c
     c08:	20050513          	addi	a0,a0,512 # 1c200 <_data_lma+0x194a0>
     c0c:	248010ef          	jal	ra,1e54 <USART_Printf_Init>
     c10:	00002537          	lui	a0,0x2
     c14:	79450513          	addi	a0,a0,1940 # 2794 <CSWTCH.3+0xfc>
     c18:	0f1010ef          	jal	ra,2508 <puts>
     c1c:	200007b7          	lui	a5,0x20000
     c20:	0147a583          	lw	a1,20(a5) # 20000014 <SystemCoreClock>
     c24:	00002537          	lui	a0,0x2
     c28:	7b450513          	addi	a0,a0,1972 # 27b4 <CSWTCH.3+0x11c>
     c2c:	08f010ef          	jal	ra,24ba <printf>
     c30:	4dd000ef          	jal	ra,190c <DBGMCU_GetCHIPID>
     c34:	85aa                	mv	a1,a0
     c36:	00002537          	lui	a0,0x2
     c3a:	7c850513          	addi	a0,a0,1992 # 27c8 <CSWTCH.3+0x130>
     c3e:	07d010ef          	jal	ra,24ba <printf>
     c42:	37a9                	jal	b8c <System_Init>
     c44:	3ded                	jal	b3e <Display_Text_Welcome>
     c46:	7d000513          	li	a0,2000
     c4a:	1d0010ef          	jal	ra,1e1a <Delay_Ms>
     c4e:	0ce447b7          	lui	a5,0xce44
     c52:	ead78793          	addi	a5,a5,-339 # ce43ead <_data_lma+0xce4114d>
     c56:	c63e                	sw	a5,12(sp)
     c58:	138807b7          	lui	a5,0x13880
     c5c:	67278793          	addi	a5,a5,1650 # 13880672 <_data_lma+0x1387d912>
     c60:	c83e                	sw	a5,16(sp)
     c62:	0044                	addi	s1,sp,4
     c64:	6785                	lui	a5,0x1
     c66:	c202                	sw	zero,4(sp)
     c68:	c402                	sw	zero,8(sp)
     c6a:	ca02                	sw	zero,20(sp)
     c6c:	8426                	mv	s0,s1
     c6e:	4605                	li	a2,1
     c70:	ce478793          	addi	a5,a5,-796 # ce4 <main+0xf2>
     c74:	a890                	sb	a2,16(s1)
     c76:	240a                	lhu	a0,8(s0)
     c78:	85be                	mv	a1,a5
     c7a:	c032                	sw	a2,0(sp)
     c7c:	0532                	slli	a0,a0,0xc
     c7e:	c4eff0ef          	jal	ra,cc <__divsi3>
     c82:	a00a                	sh	a0,0(s0)
     c84:	6785                	lui	a5,0x1
     c86:	0409                	addi	s0,s0,2
     c88:	0074                	addi	a3,sp,12
     c8a:	0485                	addi	s1,s1,1
     c8c:	ce478793          	addi	a5,a5,-796 # ce4 <main+0xf2>
     c90:	4602                	lw	a2,0(sp)
     c92:	0058                	addi	a4,sp,4
     c94:	fed410e3          	bne	s0,a3,c74 <main+0x82>
     c98:	853a                	mv	a0,a4
     c9a:	3181                	jal	8da <ADC_Display_Draw_Logo_Style>
     c9c:	000034b7          	lui	s1,0x3
     ca0:	193000ef          	jal	ra,1632 <Touch_Button_Update>
     ca4:	201000ef          	jal	ra,16a4 <Touch_Button_Get_Event>
     ca8:	4789                	li	a5,2
     caa:	04f50d63          	beq	a0,a5,d04 <main+0x112>
     cae:	478d                	li	a5,3
     cb0:	06f50063          	beq	a0,a5,d10 <main+0x11e>
     cb4:	4785                	li	a5,1
     cb6:	00f51963          	bne	a0,a5,cc8 <main+0xd6>
     cba:	00002537          	lui	a0,0x2
     cbe:	7d850513          	addi	a0,a0,2008 # 27d8 <CSWTCH.3+0x140>
     cc2:	047010ef          	jal	ra,2508 <puts>
     cc6:	31c1                	jal	986 <Display_Control_Turn_On>
     cc8:	3141                	jal	948 <Display_Control_Update>
     cca:	1e7000ef          	jal	ra,16b0 <Touch_Button_Get_Time_Ms>
     cce:	82c18413          	addi	s0,gp,-2004 # 2000006c <last_adc_read_time>
     cd2:	401c                	lw	a5,0(s0)
     cd4:	c02a                	sw	a0,0(sp)
     cd6:	06300693          	li	a3,99
     cda:	40f507b3          	sub	a5,a0,a5
     cde:	00f6ff63          	bgeu	a3,a5,cfc <main+0x10a>
     ce2:	81818513          	addi	a0,gp,-2024 # 20000058 <adc_data>
     ce6:	df4ff0ef          	jal	ra,2da <ADC_Read_All_Channels>
     cea:	4702                	lw	a4,0(sp)
     cec:	c018                	sw	a4,0(s0)
     cee:	31dd                	jal	9d4 <Display_Control_Is_On>
     cf0:	c511                	beqz	a0,cfc <main+0x10a>
     cf2:	4585                	li	a1,1
     cf4:	81818513          	addi	a0,gp,-2024 # 20000058 <adc_data>
     cf8:	843ff0ef          	jal	ra,53a <ADC_Display_Update>
     cfc:	4505                	li	a0,1
     cfe:	11c010ef          	jal	ra,1e1a <Delay_Ms>
     d02:	bf79                	j	ca0 <main+0xae>
     d04:	80448513          	addi	a0,s1,-2044 # 2804 <CSWTCH.3+0x16c>
     d08:	001010ef          	jal	ra,2508 <puts>
     d0c:	317d                	jal	9ba <Display_Control_Toggle>
     d0e:	bf6d                	j	cc8 <main+0xd6>
     d10:	00003537          	lui	a0,0x3
     d14:	83050513          	addi	a0,a0,-2000 # 2830 <CSWTCH.3+0x198>
     d18:	7f0010ef          	jal	ra,2508 <puts>
     d1c:	3149                	jal	99e <Display_Control_Turn_Off>
     d1e:	b76d                	j	cc8 <main+0xd6>

00000d20 <PWM_GPIO_Config>:
     d20:	b80ff2ef          	jal	t0,a0 <__riscv_save_0>
     d24:	1151                	addi	sp,sp,-12
     d26:	4585                	li	a1,1
     d28:	02000513          	li	a0,32
     d2c:	c002                	sw	zero,0(sp)
     d2e:	c202                	sw	zero,4(sp)
     d30:	c402                	sw	zero,8(sp)
     d32:	63b000ef          	jal	ra,1b6c <RCC_APB2PeriphClockCmd>
     d36:	4791                	li	a5,4
     d38:	807c                	sh	a5,0(sp)
     d3a:	40011537          	lui	a0,0x40011
     d3e:	47e1                	li	a5,24
     d40:	c43e                	sw	a5,8(sp)
     d42:	858a                	mv	a1,sp
     d44:	478d                	li	a5,3
     d46:	40050513          	addi	a0,a0,1024 # 40011400 <__global_pointer$+0x20010bc0>
     d4a:	c23e                	sw	a5,4(sp)
     d4c:	45d000ef          	jal	ra,19a8 <GPIO_Init>
     d50:	0131                	addi	sp,sp,12
     d52:	b58ff06f          	j	aa <__riscv_restore_0>

00000d56 <PWM_Timer_Config>:
     d56:	b4aff2ef          	jal	t0,a0 <__riscv_save_0>
     d5a:	6505                	lui	a0,0x1
     d5c:	1111                	addi	sp,sp,-28
     d5e:	4585                	li	a1,1
     d60:	80050513          	addi	a0,a0,-2048 # 800 <ADC_Display_Draw_Vietnam_Flag+0x120>
     d64:	c002                	sw	zero,0(sp)
     d66:	c202                	sw	zero,4(sp)
     d68:	00011423          	sh	zero,8(sp)
     d6c:	c602                	sw	zero,12(sp)
     d6e:	c802                	sw	zero,16(sp)
     d70:	ca02                	sw	zero,20(sp)
     d72:	cc02                	sw	zero,24(sp)
     d74:	5f9000ef          	jal	ra,1b6c <RCC_APB2PeriphClockCmd>
     d78:	200007b7          	lui	a5,0x20000
     d7c:	0147a503          	lw	a0,20(a5) # 20000014 <SystemCoreClock>
     d80:	000f45b7          	lui	a1,0xf4
     d84:	24058593          	addi	a1,a1,576 # f4240 <_data_lma+0xf14e0>
     d88:	b4cff0ef          	jal	ra,d4 <__udivsi3>
     d8c:	40013437          	lui	s0,0x40013
     d90:	157d                	addi	a0,a0,-1
     d92:	8068                	sh	a0,0(sp)
     d94:	3e700793          	li	a5,999
     d98:	858a                	mv	a1,sp
     d9a:	c0040513          	addi	a0,s0,-1024 # 40012c00 <__global_pointer$+0x200123c0>
     d9e:	c23e                	sw	a5,4(sp)
     da0:	00011123          	sh	zero,2(sp)
     da4:	605000ef          	jal	ra,1ba8 <TIM_TimeBaseInit>
     da8:	67c1                	lui	a5,0x10
     daa:	06078793          	addi	a5,a5,96 # 10060 <_data_lma+0xd300>
     dae:	006c                	addi	a1,sp,12
     db0:	c0040513          	addi	a0,s0,-1024
     db4:	c63e                	sw	a5,12(sp)
     db6:	00011923          	sh	zero,18(sp)
     dba:	00011a23          	sh	zero,20(sp)
     dbe:	639000ef          	jal	ra,1bf6 <TIM_OC1Init>
     dc2:	c0040513          	addi	a0,s0,-1024
     dc6:	45a1                	li	a1,8
     dc8:	6f3000ef          	jal	ra,1cba <TIM_OC1PreloadConfig>
     dcc:	c0040513          	addi	a0,s0,-1024
     dd0:	4585                	li	a1,1
     dd2:	6cf000ef          	jal	ra,1ca0 <TIM_ARRPreloadConfig>
     dd6:	c0040513          	addi	a0,s0,-1024
     dda:	4585                	li	a1,1
     ddc:	685000ef          	jal	ra,1c60 <TIM_Cmd>
     de0:	4585                	li	a1,1
     de2:	c0040513          	addi	a0,s0,-1024
     de6:	693000ef          	jal	ra,1c78 <TIM_CtrlPWMOutputs>
     dea:	0171                	addi	sp,sp,28
     dec:	abeff06f          	j	aa <__riscv_restore_0>

00000df0 <PWM_Set_Brightness>:
     df0:	ab0ff2ef          	jal	t0,a0 <__riscv_save_0>
     df4:	3e800793          	li	a5,1000
     df8:	3e800413          	li	s0,1000
     dfc:	00a7e363          	bltu	a5,a0,e02 <PWM_Set_Brightness+0x12>
     e00:	842a                	mv	s0,a0
     e02:	01041593          	slli	a1,s0,0x10
     e06:	40013537          	lui	a0,0x40013
     e0a:	81c1                	srli	a1,a1,0x10
     e0c:	c0050513          	addi	a0,a0,-1024 # 40012c00 <__global_pointer$+0x200123c0>
     e10:	6b9000ef          	jal	ra,1cc8 <TIM_SetCompare1>
     e14:	82819a23          	sh	s0,-1996(gp) # 20000074 <pwm_control>
     e18:	a92ff06f          	j	aa <__riscv_restore_0>

00000e1c <PWM_Config_Init>:
     e1c:	a84ff2ef          	jal	t0,a0 <__riscv_save_0>
     e20:	83418793          	addi	a5,gp,-1996 # 20000074 <pwm_control>
     e24:	01f40737          	lui	a4,0x1f40
     e28:	c398                	sw	a4,0(a5)
     e2a:	6705                	lui	a4,0x1
     e2c:	a0070713          	addi	a4,a4,-1536 # a00 <Display_Control_Show_Startup_Message+0x6>
     e30:	a3da                	sh	a4,4(a5)
     e32:	35fd                	jal	d20 <PWM_GPIO_Config>
     e34:	370d                	jal	d56 <PWM_Timer_Config>
     e36:	4501                	li	a0,0
     e38:	3f65                	jal	df0 <PWM_Set_Brightness>
     e3a:	a70ff06f          	j	aa <__riscv_restore_0>

00000e3e <PWM_Update_Fade>:
     e3e:	83418793          	addi	a5,gp,-1996 # 20000074 <pwm_control>
     e42:	23d8                	lbu	a4,4(a5)
     e44:	c729                	beqz	a4,e8e <PWM_Update_Fade+0x50>
     e46:	a5aff2ef          	jal	t0,a0 <__riscv_save_0>
     e4a:	238a                	lhu	a0,0(a5)
     e4c:	23ba                	lhu	a4,2(a5)
     e4e:	00e57e63          	bgeu	a0,a4,e6a <PWM_Update_Fade+0x2c>
     e52:	33d4                	lbu	a3,5(a5)
     e54:	9536                	add	a0,a0,a3
     e56:	0542                	slli	a0,a0,0x10
     e58:	8141                	srli	a0,a0,0x10
     e5a:	00e56563          	bltu	a0,a4,e64 <PWM_Update_Fade+0x26>
     e5e:	00078223          	sb	zero,4(a5)
     e62:	853a                	mv	a0,a4
     e64:	3771                	jal	df0 <PWM_Set_Brightness>
     e66:	a44ff06f          	j	aa <__riscv_restore_0>
     e6a:	00a77f63          	bgeu	a4,a0,e88 <PWM_Update_Fade+0x4a>
     e6e:	83418693          	addi	a3,gp,-1996 # 20000074 <pwm_control>
     e72:	32dc                	lbu	a5,5(a3)
     e74:	00f56763          	bltu	a0,a5,e82 <PWM_Update_Fade+0x44>
     e78:	8d1d                	sub	a0,a0,a5
     e7a:	0542                	slli	a0,a0,0x10
     e7c:	8141                	srli	a0,a0,0x10
     e7e:	fea763e3          	bltu	a4,a0,e64 <PWM_Update_Fade+0x26>
     e82:	00068223          	sb	zero,4(a3)
     e86:	bff1                	j	e62 <PWM_Update_Fade+0x24>
     e88:	00078223          	sb	zero,4(a5)
     e8c:	bfe9                	j	e66 <PWM_Update_Fade+0x28>
     e8e:	8082                	ret

00000e90 <PWM_Turn_On>:
     e90:	83418793          	addi	a5,gp,-1996 # 20000074 <pwm_control>
     e94:	1f400713          	li	a4,500
     e98:	a3ba                	sh	a4,2(a5)
     e9a:	4705                	li	a4,1
     e9c:	a3d8                	sb	a4,4(a5)
     e9e:	8082                	ret

00000ea0 <PWM_Turn_Off>:
     ea0:	83418793          	addi	a5,gp,-1996 # 20000074 <pwm_control>
     ea4:	4705                	li	a4,1
     ea6:	00079123          	sh	zero,2(a5)
     eaa:	a3d8                	sb	a4,4(a5)
     eac:	8082                	ret

00000eae <PWM_Get_Brightness>:
     eae:	8341d503          	lhu	a0,-1996(gp) # 20000074 <pwm_control>
     eb2:	8082                	ret

00000eb4 <SPI_send_DMA>:
     eb4:	400207b7          	lui	a5,0x40020
     eb8:	dfc8                	sw	a0,60(a5)
     eba:	dbcc                	sw	a1,52(a5)
     ebc:	5b98                	lw	a4,48(a5)
     ebe:	400206b7          	lui	a3,0x40020
     ec2:	20000593          	li	a1,512
     ec6:	00176713          	ori	a4,a4,1
     eca:	db98                	sw	a4,48(a5)
     ecc:	67c1                	lui	a5,0x10
     ece:	17fd                	addi	a5,a5,-1
     ed0:	167d                	addi	a2,a2,-1
     ed2:	0642                	slli	a2,a2,0x10
     ed4:	8241                	srli	a2,a2,0x10
     ed6:	00f61863          	bne	a2,a5,ee6 <SPI_send_DMA+0x32>
     eda:	40020737          	lui	a4,0x40020
     ede:	5b1c                	lw	a5,48(a4)
     ee0:	9bf9                	andi	a5,a5,-2
     ee2:	db1c                	sw	a5,48(a4)
     ee4:	8082                	ret
     ee6:	c2cc                	sw	a1,4(a3)
     ee8:	4298                	lw	a4,0(a3)
     eea:	20077713          	andi	a4,a4,512
     eee:	df6d                	beqz	a4,ee8 <SPI_send_DMA+0x34>
     ef0:	b7c5                	j	ed0 <SPI_send_DMA+0x1c>

00000ef2 <SPI_send>:
     ef2:	400137b7          	lui	a5,0x40013
     ef6:	a7ca                	sh	a0,12(a5)
     ef8:	40013737          	lui	a4,0x40013
     efc:	271e                	lhu	a5,8(a4)
     efe:	8b89                	andi	a5,a5,2
     f00:	dff5                	beqz	a5,efc <SPI_send+0xa>
     f02:	8082                	ret

00000f04 <write_command_8>:
     f04:	99cff2ef          	jal	t0,a0 <__riscv_save_0>
     f08:	40011737          	lui	a4,0x40011
     f0c:	4b5c                	lw	a5,20(a4)
     f0e:	0087e793          	ori	a5,a5,8
     f12:	cb5c                	sw	a5,20(a4)
     f14:	3ff9                	jal	ef2 <SPI_send>
     f16:	994ff06f          	j	aa <__riscv_restore_0>

00000f1a <write_data_16>:
     f1a:	986ff2ef          	jal	t0,a0 <__riscv_save_0>
     f1e:	40011737          	lui	a4,0x40011
     f22:	4b1c                	lw	a5,16(a4)
     f24:	842a                	mv	s0,a0
     f26:	8121                	srli	a0,a0,0x8
     f28:	0087e793          	ori	a5,a5,8
     f2c:	cb1c                	sw	a5,16(a4)
     f2e:	37d1                	jal	ef2 <SPI_send>
     f30:	0ff47513          	andi	a0,s0,255
     f34:	3f7d                	jal	ef2 <SPI_send>
     f36:	974ff06f          	j	aa <__riscv_restore_0>

00000f3a <tft_set_window>:
     f3a:	966ff2ef          	jal	t0,a0 <__riscv_save_0>
     f3e:	1151                	addi	sp,sp,-12
     f40:	842a                	mv	s0,a0
     f42:	02a00513          	li	a0,42
     f46:	c036                	sw	a3,0(sp)
     f48:	c42e                	sw	a1,8(sp)
     f4a:	c232                	sw	a2,4(sp)
     f4c:	3f65                	jal	f04 <write_command_8>
     f4e:	8522                	mv	a0,s0
     f50:	37e9                	jal	f1a <write_data_16>
     f52:	4612                	lw	a2,4(sp)
     f54:	8532                	mv	a0,a2
     f56:	37d1                	jal	f1a <write_data_16>
     f58:	02b00513          	li	a0,43
     f5c:	3765                	jal	f04 <write_command_8>
     f5e:	45a2                	lw	a1,8(sp)
     f60:	852e                	mv	a0,a1
     f62:	3f65                	jal	f1a <write_data_16>
     f64:	4682                	lw	a3,0(sp)
     f66:	8536                	mv	a0,a3
     f68:	3f4d                	jal	f1a <write_data_16>
     f6a:	02c00513          	li	a0,44
     f6e:	3f59                	jal	f04 <write_command_8>
     f70:	0131                	addi	sp,sp,12
     f72:	938ff06f          	j	aa <__riscv_restore_0>

00000f76 <tft_init>:
     f76:	92aff2ef          	jal	t0,a0 <__riscv_save_0>
     f7a:	400216b7          	lui	a3,0x40021
     f7e:	4e9c                	lw	a5,24(a3)
     f80:	6705                	lui	a4,0x1
     f82:	0741                	addi	a4,a4,16
     f84:	8fd9                	or	a5,a5,a4
     f86:	40011437          	lui	s0,0x40011
     f8a:	ce9c                	sw	a5,24(a3)
     f8c:	401c                	lw	a5,0(s0)
     f8e:	777d                	lui	a4,0xfffff
     f90:	0ff70713          	addi	a4,a4,255 # fffff0ff <__global_pointer$+0xdfffe8bf>
     f94:	8ff9                	and	a5,a5,a4
     f96:	c01c                	sw	a5,0(s0)
     f98:	401c                	lw	a5,0(s0)
     f9a:	7745                	lui	a4,0xffff1
     f9c:	177d                	addi	a4,a4,-1
     f9e:	3007e793          	ori	a5,a5,768
     fa2:	c01c                	sw	a5,0(s0)
     fa4:	401c                	lw	a5,0(s0)
     fa6:	fff10637          	lui	a2,0xfff10
     faa:	167d                	addi	a2,a2,-1
     fac:	8ff9                	and	a5,a5,a4
     fae:	c01c                	sw	a5,0(s0)
     fb0:	401c                	lw	a5,0(s0)
     fb2:	670d                	lui	a4,0x3
     fb4:	1101                	addi	sp,sp,-32
     fb6:	8fd9                	or	a5,a5,a4
     fb8:	c01c                	sw	a5,0(s0)
     fba:	401c                	lw	a5,0(s0)
     fbc:	0b070713          	addi	a4,a4,176 # 30b0 <_data_lma+0x350>
     fc0:	03200513          	li	a0,50
     fc4:	8ff1                	and	a5,a5,a2
     fc6:	c01c                	sw	a5,0(s0)
     fc8:	401c                	lw	a5,0(s0)
     fca:	00030637          	lui	a2,0x30
     fce:	8fd1                	or	a5,a5,a2
     fd0:	c01c                	sw	a5,0(s0)
     fd2:	401c                	lw	a5,0(s0)
     fd4:	ff100637          	lui	a2,0xff100
     fd8:	167d                	addi	a2,a2,-1
     fda:	8ff1                	and	a5,a5,a2
     fdc:	c01c                	sw	a5,0(s0)
     fde:	401c                	lw	a5,0(s0)
     fe0:	00b00637          	lui	a2,0xb00
     fe4:	8fd1                	or	a5,a5,a2
     fe6:	c01c                	sw	a5,0(s0)
     fe8:	401c                	lw	a5,0(s0)
     fea:	f1000637          	lui	a2,0xf1000
     fee:	167d                	addi	a2,a2,-1
     ff0:	8ff1                	and	a5,a5,a2
     ff2:	c01c                	sw	a5,0(s0)
     ff4:	401c                	lw	a5,0(s0)
     ff6:	0b000637          	lui	a2,0xb000
     ffa:	8fd1                	or	a5,a5,a2
     ffc:	7671                	lui	a2,0xffffc
     ffe:	c01c                	sw	a5,0(s0)
    1000:	30460613          	addi	a2,a2,772 # ffffc304 <__global_pointer$+0xdfffbac4>
    1004:	400137b7          	lui	a5,0x40013
    1008:	a392                	sh	a2,0(a5)
    100a:	461d                	li	a2,7
    100c:	ab92                	sh	a2,16(a5)
    100e:	23d2                	lhu	a2,4(a5)
    1010:	07b1                	addi	a5,a5,12
    1012:	00266613          	ori	a2,a2,2
    1016:	fec79c23          	sh	a2,-8(a5) # 40012ff8 <__global_pointer$+0x200127b8>
    101a:	ff47d603          	lhu	a2,-12(a5)
    101e:	04066613          	ori	a2,a2,64
    1022:	fec79a23          	sh	a2,-12(a5)
    1026:	4ad0                	lw	a2,20(a3)
    1028:	00166613          	ori	a2,a2,1
    102c:	cad0                	sw	a2,20(a3)
    102e:	400206b7          	lui	a3,0x40020
    1032:	da98                	sw	a4,48(a3)
    1034:	de9c                	sw	a5,56(a3)
    1036:	485c                	lw	a5,20(s0)
    1038:	0047e793          	ori	a5,a5,4
    103c:	c85c                	sw	a5,20(s0)
    103e:	5dd000ef          	jal	ra,1e1a <Delay_Ms>
    1042:	481c                	lw	a5,16(s0)
    1044:	03200513          	li	a0,50
    1048:	0047e793          	ori	a5,a5,4
    104c:	c81c                	sw	a5,16(s0)
    104e:	5cd000ef          	jal	ra,1e1a <Delay_Ms>
    1052:	485c                	lw	a5,20(s0)
    1054:	4545                	li	a0,17
    1056:	0107e793          	ori	a5,a5,16
    105a:	c85c                	sw	a5,20(s0)
    105c:	3565                	jal	f04 <write_command_8>
    105e:	07800513          	li	a0,120
    1062:	5b9000ef          	jal	ra,1e1a <Delay_Ms>
    1066:	03600513          	li	a0,54
    106a:	3d69                	jal	f04 <write_command_8>
    106c:	481c                	lw	a5,16(s0)
    106e:	0a800513          	li	a0,168
    1072:	0087e793          	ori	a5,a5,8
    1076:	c81c                	sw	a5,16(s0)
    1078:	3dad                	jal	ef2 <SPI_send>
    107a:	03a00513          	li	a0,58
    107e:	3559                	jal	f04 <write_command_8>
    1080:	481c                	lw	a5,16(s0)
    1082:	4515                	li	a0,5
    1084:	0087e793          	ori	a5,a5,8
    1088:	c81c                	sw	a5,16(s0)
    108a:	35a5                	jal	ef2 <SPI_send>
    108c:	6589                	lui	a1,0x2
    108e:	62458493          	addi	s1,a1,1572 # 2624 <memcpy+0xd2>
    1092:	4641                	li	a2,16
    1094:	62458593          	addi	a1,a1,1572
    1098:	850a                	mv	a0,sp
    109a:	4b8010ef          	jal	ra,2552 <memcpy>
    109e:	0e000513          	li	a0,224
    10a2:	358d                	jal	f04 <write_command_8>
    10a4:	481c                	lw	a5,16(s0)
    10a6:	850a                	mv	a0,sp
    10a8:	4605                	li	a2,1
    10aa:	0087e793          	ori	a5,a5,8
    10ae:	c81c                	sw	a5,16(s0)
    10b0:	45c1                	li	a1,16
    10b2:	3509                	jal	eb4 <SPI_send_DMA>
    10b4:	01048593          	addi	a1,s1,16
    10b8:	4641                	li	a2,16
    10ba:	0808                	addi	a0,sp,16
    10bc:	496010ef          	jal	ra,2552 <memcpy>
    10c0:	0e100513          	li	a0,225
    10c4:	3581                	jal	f04 <write_command_8>
    10c6:	481c                	lw	a5,16(s0)
    10c8:	4605                	li	a2,1
    10ca:	45c1                	li	a1,16
    10cc:	0087e793          	ori	a5,a5,8
    10d0:	c81c                	sw	a5,16(s0)
    10d2:	0808                	addi	a0,sp,16
    10d4:	33c5                	jal	eb4 <SPI_send_DMA>
    10d6:	4529                	li	a0,10
    10d8:	543000ef          	jal	ra,1e1a <Delay_Ms>
    10dc:	02100513          	li	a0,33
    10e0:	3515                	jal	f04 <write_command_8>
    10e2:	454d                	li	a0,19
    10e4:	3505                	jal	f04 <write_command_8>
    10e6:	4529                	li	a0,10
    10e8:	533000ef          	jal	ra,1e1a <Delay_Ms>
    10ec:	02900513          	li	a0,41
    10f0:	3d11                	jal	f04 <write_command_8>
    10f2:	4529                	li	a0,10
    10f4:	527000ef          	jal	ra,1e1a <Delay_Ms>
    10f8:	481c                	lw	a5,16(s0)
    10fa:	0107e793          	ori	a5,a5,16
    10fe:	c81c                	sw	a5,16(s0)
    1100:	6105                	addi	sp,sp,32
    1102:	fa9fe06f          	j	aa <__riscv_restore_0>

00001106 <tft_set_cursor>:
    1106:	0505                	addi	a0,a0,1
    1108:	96a19e23          	sh	a0,-1668(gp) # 200001bc <_cursor_x>
    110c:	05e9                	addi	a1,a1,26
    110e:	96b19f23          	sh	a1,-1666(gp) # 200001be <_cursor_y>
    1112:	8082                	ret

00001114 <tft_set_color>:
    1114:	200007b7          	lui	a5,0x20000
    1118:	00a79023          	sh	a0,0(a5) # 20000000 <_highcode_vma_end>
    111c:	8082                	ret

0000111e <tft_set_background_color>:
    111e:	82a19d23          	sh	a0,-1990(gp) # 2000007a <_bg_color>
    1122:	8082                	ret

00001124 <tft_print_char>:
    1124:	f7dfe2ef          	jal	t0,a0 <__riscv_save_0>
    1128:	00251793          	slli	a5,a0,0x2
    112c:	953e                	add	a0,a0,a5
    112e:	83a1d783          	lhu	a5,-1990(gp) # 2000007a <_bg_color>
    1132:	1131                	addi	sp,sp,-20
    1134:	0087d713          	srli	a4,a5,0x8
    1138:	0ff7f793          	andi	a5,a5,255
    113c:	c63e                	sw	a5,12(sp)
    113e:	200007b7          	lui	a5,0x20000
    1142:	0007d783          	lhu	a5,0(a5) # 20000000 <_highcode_vma_end>
    1146:	c43a                	sw	a4,8(sp)
    1148:	4281                	li	t0,0
    114a:	0087d713          	srli	a4,a5,0x8
    114e:	0ff7f793          	andi	a5,a5,255
    1152:	c23e                	sw	a5,4(sp)
    1154:	678d                	lui	a5,0x3
    1156:	85878793          	addi	a5,a5,-1960 # 2858 <font>
    115a:	c03a                	sw	a4,0(sp)
    115c:	4681                	li	a3,0
    115e:	c83e                	sw	a5,16(sp)
    1160:	83c18313          	addi	t1,gp,-1988 # 2000007c <_buffer>
    1164:	4785                	li	a5,1
    1166:	005790b3          	sll	ra,a5,t0
    116a:	85b6                	mv	a1,a3
    116c:	4601                	li	a2,0
    116e:	44c2                	lw	s1,16(sp)
    1170:	00c503b3          	add	t2,a0,a2
    1174:	872e                	mv	a4,a1
    1176:	93a6                	add	t2,t2,s1
    1178:	0003c383          	lbu	t2,0(t2)
    117c:	00158793          	addi	a5,a1,1
    1180:	0589                	addi	a1,a1,2
    1182:	07c2                	slli	a5,a5,0x10
    1184:	05c2                	slli	a1,a1,0x10
    1186:	0013f3b3          	and	t2,t2,ra
    118a:	83c1                	srli	a5,a5,0x10
    118c:	81c1                	srli	a1,a1,0x10
    118e:	971a                	add	a4,a4,t1
    1190:	06038963          	beqz	t2,1202 <tft_print_char+0xde>
    1194:	4482                	lw	s1,0(sp)
    1196:	979a                	add	a5,a5,t1
    1198:	a304                	sb	s1,0(a4)
    119a:	4712                	lw	a4,4(sp)
    119c:	a398                	sb	a4,0(a5)
    119e:	0605                	addi	a2,a2,1
    11a0:	4795                	li	a5,5
    11a2:	fcf616e3          	bne	a2,a5,116e <tft_print_char+0x4a>
    11a6:	06a9                	addi	a3,a3,10
    11a8:	06c2                	slli	a3,a3,0x10
    11aa:	82c1                	srli	a3,a3,0x10
    11ac:	04600793          	li	a5,70
    11b0:	0285                	addi	t0,t0,1
    11b2:	faf699e3          	bne	a3,a5,1164 <tft_print_char+0x40>
    11b6:	400114b7          	lui	s1,0x40011
    11ba:	48dc                	lw	a5,20(s1)
    11bc:	0107e793          	ori	a5,a5,16
    11c0:	c8dc                	sw	a5,20(s1)
    11c2:	97c1d503          	lhu	a0,-1668(gp) # 200001bc <_cursor_x>
    11c6:	97e1d583          	lhu	a1,-1666(gp) # 200001be <_cursor_y>
    11ca:	00450613          	addi	a2,a0,4
    11ce:	0642                	slli	a2,a2,0x10
    11d0:	00658693          	addi	a3,a1,6
    11d4:	06c2                	slli	a3,a3,0x10
    11d6:	82c1                	srli	a3,a3,0x10
    11d8:	8241                	srli	a2,a2,0x10
    11da:	d61ff0ef          	jal	ra,f3a <tft_set_window>
    11de:	489c                	lw	a5,16(s1)
    11e0:	4605                	li	a2,1
    11e2:	04600593          	li	a1,70
    11e6:	0087e793          	ori	a5,a5,8
    11ea:	c89c                	sw	a5,16(s1)
    11ec:	83c18513          	addi	a0,gp,-1988 # 2000007c <_buffer>
    11f0:	cc5ff0ef          	jal	ra,eb4 <SPI_send_DMA>
    11f4:	489c                	lw	a5,16(s1)
    11f6:	0107e793          	ori	a5,a5,16
    11fa:	c89c                	sw	a5,16(s1)
    11fc:	0151                	addi	sp,sp,20
    11fe:	eadfe06f          	j	aa <__riscv_restore_0>
    1202:	44a2                	lw	s1,8(sp)
    1204:	979a                	add	a5,a5,t1
    1206:	a304                	sb	s1,0(a4)
    1208:	4732                	lw	a4,12(sp)
    120a:	bf49                	j	119c <tft_print_char+0x78>

0000120c <tft_print>:
    120c:	e95fe2ef          	jal	t0,a0 <__riscv_save_0>
    1210:	842a                	mv	s0,a0
    1212:	00040503          	lb	a0,0(s0) # 40011000 <__global_pointer$+0x200107c0>
    1216:	e119                	bnez	a0,121c <tft_print+0x10>
    1218:	e93fe06f          	j	aa <__riscv_restore_0>
    121c:	3721                	jal	1124 <tft_print_char>
    121e:	97c18713          	addi	a4,gp,-1668 # 200001bc <_cursor_x>
    1222:	231e                	lhu	a5,0(a4)
    1224:	0405                	addi	s0,s0,1
    1226:	0799                	addi	a5,a5,6
    1228:	a31e                	sh	a5,0(a4)
    122a:	b7e5                	j	1212 <tft_print+0x6>

0000122c <tft_print_number>:
    122c:	e75fe2ef          	jal	t0,a0 <__riscv_save_0>
    1230:	1141                	addi	sp,sp,-16
    1232:	87aa                	mv	a5,a0
    1234:	86ae                	mv	a3,a1
    1236:	4701                	li	a4,0
    1238:	00055563          	bgez	a0,1242 <tft_print_number+0x16>
    123c:	40a007b3          	neg	a5,a0
    1240:	4705                	li	a4,1
    1242:	98018613          	addi	a2,gp,-1664 # 200001c0 <str.4169>
    1246:	000605a3          	sb	zero,11(a2)
    124a:	442d                	li	s0,11
    124c:	98018493          	addi	s1,gp,-1664 # 200001c0 <str.4169>
    1250:	eba9                	bnez	a5,12a2 <tft_print_number+0x76>
    1252:	47ad                	li	a5,11
    1254:	00f41663          	bne	s0,a5,1260 <tft_print_number+0x34>
    1258:	03000793          	li	a5,48
    125c:	a4bc                	sb	a5,10(s1)
    125e:	4429                	li	s0,10
    1260:	cb09                	beqz	a4,1272 <tft_print_number+0x46>
    1262:	147d                	addi	s0,s0,-1
    1264:	0ff47413          	andi	s0,s0,255
    1268:	008487b3          	add	a5,s1,s0
    126c:	02d00713          	li	a4,45
    1270:	a398                	sb	a4,0(a5)
    1272:	472d                	li	a4,11
    1274:	8f01                	sub	a4,a4,s0
    1276:	00171793          	slli	a5,a4,0x1
    127a:	97ba                	add	a5,a5,a4
    127c:	0786                	slli	a5,a5,0x1
    127e:	17fd                	addi	a5,a5,-1
    1280:	07c2                	slli	a5,a5,0x10
    1282:	83c1                	srli	a5,a5,0x10
    1284:	00d7f963          	bgeu	a5,a3,1296 <tft_print_number+0x6a>
    1288:	97c18713          	addi	a4,gp,-1668 # 200001bc <_cursor_x>
    128c:	2312                	lhu	a2,0(a4)
    128e:	96b2                	add	a3,a3,a2
    1290:	40f687b3          	sub	a5,a3,a5
    1294:	a31e                	sh	a5,0(a4)
    1296:	00848533          	add	a0,s1,s0
    129a:	3f8d                	jal	120c <tft_print>
    129c:	0141                	addi	sp,sp,16
    129e:	e0dfe06f          	j	aa <__riscv_restore_0>
    12a2:	147d                	addi	s0,s0,-1
    12a4:	0ff47413          	andi	s0,s0,255
    12a8:	00848633          	add	a2,s1,s0
    12ac:	45a9                	li	a1,10
    12ae:	853e                	mv	a0,a5
    12b0:	c636                	sw	a3,12(sp)
    12b2:	c43a                	sw	a4,8(sp)
    12b4:	c232                	sw	a2,4(sp)
    12b6:	c03e                	sw	a5,0(sp)
    12b8:	e6dfe0ef          	jal	ra,124 <__modsi3>
    12bc:	4782                	lw	a5,0(sp)
    12be:	4612                	lw	a2,4(sp)
    12c0:	03050513          	addi	a0,a0,48
    12c4:	45a9                	li	a1,10
    12c6:	a208                	sb	a0,0(a2)
    12c8:	853e                	mv	a0,a5
    12ca:	e03fe0ef          	jal	ra,cc <__divsi3>
    12ce:	87aa                	mv	a5,a0
    12d0:	46b2                	lw	a3,12(sp)
    12d2:	4722                	lw	a4,8(sp)
    12d4:	bfb5                	j	1250 <tft_print_number+0x24>

000012d6 <tft_draw_pixel>:
    12d6:	dcbfe2ef          	jal	t0,a0 <__riscv_save_0>
    12da:	40011437          	lui	s0,0x40011
    12de:	485c                	lw	a5,20(s0)
    12e0:	84b2                	mv	s1,a2
    12e2:	01a58693          	addi	a3,a1,26
    12e6:	00150613          	addi	a2,a0,1
    12ea:	0642                	slli	a2,a2,0x10
    12ec:	06c2                	slli	a3,a3,0x10
    12ee:	8241                	srli	a2,a2,0x10
    12f0:	82c1                	srli	a3,a3,0x10
    12f2:	0107e793          	ori	a5,a5,16
    12f6:	c85c                	sw	a5,20(s0)
    12f8:	8532                	mv	a0,a2
    12fa:	85b6                	mv	a1,a3
    12fc:	c3fff0ef          	jal	ra,f3a <tft_set_window>
    1300:	8526                	mv	a0,s1
    1302:	c19ff0ef          	jal	ra,f1a <write_data_16>
    1306:	481c                	lw	a5,16(s0)
    1308:	0107e793          	ori	a5,a5,16
    130c:	c81c                	sw	a5,16(s0)
    130e:	d9dfe06f          	j	aa <__riscv_restore_0>

00001312 <tft_fill_rect>:
    1312:	d8ffe2ef          	jal	t0,a0 <__riscv_save_0>
    1316:	0505                	addi	a0,a0,1
    1318:	05e9                	addi	a1,a1,26
    131a:	0542                	slli	a0,a0,0x10
    131c:	05c2                	slli	a1,a1,0x10
    131e:	8336                	mv	t1,a3
    1320:	1171                	addi	sp,sp,-4
    1322:	8141                	srli	a0,a0,0x10
    1324:	81c1                	srli	a1,a1,0x10
    1326:	00875393          	srli	t2,a4,0x8
    132a:	4781                	li	a5,0
    132c:	83c18693          	addi	a3,gp,-1988 # 2000007c <_buffer>
    1330:	00179413          	slli	s0,a5,0x1
    1334:	0442                	slli	s0,s0,0x10
    1336:	8041                	srli	s0,s0,0x10
    1338:	04c79763          	bne	a5,a2,1386 <tft_fill_rect+0x74>
    133c:	400114b7          	lui	s1,0x40011
    1340:	48d8                	lw	a4,20(s1)
    1342:	fff30693          	addi	a3,t1,-1
    1346:	fff78613          	addi	a2,a5,-1
    134a:	96ae                	add	a3,a3,a1
    134c:	962a                	add	a2,a2,a0
    134e:	01076713          	ori	a4,a4,16
    1352:	06c2                	slli	a3,a3,0x10
    1354:	0642                	slli	a2,a2,0x10
    1356:	c8d8                	sw	a4,20(s1)
    1358:	82c1                	srli	a3,a3,0x10
    135a:	8241                	srli	a2,a2,0x10
    135c:	c01a                	sw	t1,0(sp)
    135e:	bddff0ef          	jal	ra,f3a <tft_set_window>
    1362:	489c                	lw	a5,16(s1)
    1364:	4302                	lw	t1,0(sp)
    1366:	0087e793          	ori	a5,a5,8
    136a:	c89c                	sw	a5,16(s1)
    136c:	861a                	mv	a2,t1
    136e:	85a2                	mv	a1,s0
    1370:	83c18513          	addi	a0,gp,-1988 # 2000007c <_buffer>
    1374:	b41ff0ef          	jal	ra,eb4 <SPI_send_DMA>
    1378:	489c                	lw	a5,16(s1)
    137a:	0107e793          	ori	a5,a5,16
    137e:	c89c                	sw	a5,16(s1)
    1380:	0111                	addi	sp,sp,4
    1382:	d29fe06f          	j	aa <__riscv_restore_0>
    1386:	008684b3          	add	s1,a3,s0
    138a:	0405                	addi	s0,s0,1
    138c:	0442                	slli	s0,s0,0x10
    138e:	8041                	srli	s0,s0,0x10
    1390:	0785                	addi	a5,a5,1
    1392:	00748023          	sb	t2,0(s1) # 40011000 <__global_pointer$+0x200107c0>
    1396:	9436                	add	s0,s0,a3
    1398:	07c2                	slli	a5,a5,0x10
    139a:	a018                	sb	a4,0(s0)
    139c:	83c1                	srli	a5,a5,0x10
    139e:	bf49                	j	1330 <tft_fill_rect+0x1e>

000013a0 <SystemInit>:
    13a0:	d01fe2ef          	jal	t0,a0 <__riscv_save_0>
    13a4:	40021437          	lui	s0,0x40021
    13a8:	401c                	lw	a5,0(s0)
    13aa:	f8ff0737          	lui	a4,0xf8ff0
    13ae:	1161                	addi	sp,sp,-8
    13b0:	0017e793          	ori	a5,a5,1
    13b4:	c01c                	sw	a5,0(s0)
    13b6:	405c                	lw	a5,4(s0)
    13b8:	4541                	li	a0,16
    13ba:	8ff9                	and	a5,a5,a4
    13bc:	c05c                	sw	a5,4(s0)
    13be:	401c                	lw	a5,0(s0)
    13c0:	fef70737          	lui	a4,0xfef70
    13c4:	177d                	addi	a4,a4,-1
    13c6:	8ff9                	and	a5,a5,a4
    13c8:	c01c                	sw	a5,0(s0)
    13ca:	401c                	lw	a5,0(s0)
    13cc:	fffc0737          	lui	a4,0xfffc0
    13d0:	177d                	addi	a4,a4,-1
    13d2:	8ff9                	and	a5,a5,a4
    13d4:	c01c                	sw	a5,0(s0)
    13d6:	405c                	lw	a5,4(s0)
    13d8:	7741                	lui	a4,0xffff0
    13da:	177d                	addi	a4,a4,-1
    13dc:	8ff9                	and	a5,a5,a4
    13de:	c05c                	sw	a5,4(s0)
    13e0:	009f07b7          	lui	a5,0x9f0
    13e4:	c41c                	sw	a5,8(s0)
    13e6:	2dc9                	jal	1ab8 <RCC_AdjustHSICalibrationValue>
    13e8:	4c1c                	lw	a5,24(s0)
    13ea:	00020637          	lui	a2,0x20
    13ee:	0207e793          	ori	a5,a5,32
    13f2:	cc1c                	sw	a5,24(s0)
    13f4:	400117b7          	lui	a5,0x40011
    13f8:	4007a703          	lw	a4,1024(a5) # 40011400 <__global_pointer$+0x20010bc0>
    13fc:	40078693          	addi	a3,a5,1024
    1400:	f0f77713          	andi	a4,a4,-241
    1404:	40e7a023          	sw	a4,1024(a5)
    1408:	4007a703          	lw	a4,1024(a5)
    140c:	08076713          	ori	a4,a4,128
    1410:	40e7a023          	sw	a4,1024(a5)
    1414:	4789                	li	a5,2
    1416:	ca9c                	sw	a5,16(a3)
    1418:	c002                	sw	zero,0(sp)
    141a:	c202                	sw	zero,4(sp)
    141c:	4c1c                	lw	a5,24(s0)
    141e:	40010737          	lui	a4,0x40010
    1422:	66a1                	lui	a3,0x8
    1424:	0017e793          	ori	a5,a5,1
    1428:	cc1c                	sw	a5,24(s0)
    142a:	435c                	lw	a5,4(a4)
    142c:	8fd5                	or	a5,a5,a3
    142e:	c35c                	sw	a5,4(a4)
    1430:	401c                	lw	a5,0(s0)
    1432:	6741                	lui	a4,0x10
    1434:	400216b7          	lui	a3,0x40021
    1438:	8fd9                	or	a5,a5,a4
    143a:	c01c                	sw	a5,0(s0)
    143c:	6709                	lui	a4,0x2
    143e:	429c                	lw	a5,0(a3)
    1440:	8ff1                	and	a5,a5,a2
    1442:	c23e                	sw	a5,4(sp)
    1444:	4782                	lw	a5,0(sp)
    1446:	0785                	addi	a5,a5,1
    1448:	c03e                	sw	a5,0(sp)
    144a:	4792                	lw	a5,4(sp)
    144c:	e781                	bnez	a5,1454 <SystemInit+0xb4>
    144e:	4782                	lw	a5,0(sp)
    1450:	fee797e3          	bne	a5,a4,143e <SystemInit+0x9e>
    1454:	400217b7          	lui	a5,0x40021
    1458:	439c                	lw	a5,0(a5)
    145a:	00e79713          	slli	a4,a5,0xe
    145e:	06075963          	bgez	a4,14d0 <SystemInit+0x130>
    1462:	4785                	li	a5,1
    1464:	c23e                	sw	a5,4(sp)
    1466:	4712                	lw	a4,4(sp)
    1468:	4785                	li	a5,1
    146a:	06f71063          	bne	a4,a5,14ca <SystemInit+0x12a>
    146e:	400227b7          	lui	a5,0x40022
    1472:	4398                	lw	a4,0(a5)
    1474:	76c1                	lui	a3,0xffff0
    1476:	16fd                	addi	a3,a3,-1
    1478:	9b71                	andi	a4,a4,-4
    147a:	c398                	sw	a4,0(a5)
    147c:	4398                	lw	a4,0(a5)
    147e:	00176713          	ori	a4,a4,1
    1482:	c398                	sw	a4,0(a5)
    1484:	400217b7          	lui	a5,0x40021
    1488:	43d8                	lw	a4,4(a5)
    148a:	c3d8                	sw	a4,4(a5)
    148c:	43d8                	lw	a4,4(a5)
    148e:	8f75                	and	a4,a4,a3
    1490:	c3d8                	sw	a4,4(a5)
    1492:	43d8                	lw	a4,4(a5)
    1494:	66c1                	lui	a3,0x10
    1496:	8f55                	or	a4,a4,a3
    1498:	c3d8                	sw	a4,4(a5)
    149a:	4398                	lw	a4,0(a5)
    149c:	010006b7          	lui	a3,0x1000
    14a0:	8f55                	or	a4,a4,a3
    14a2:	c398                	sw	a4,0(a5)
    14a4:	4398                	lw	a4,0(a5)
    14a6:	00671693          	slli	a3,a4,0x6
    14aa:	fe06dde3          	bgez	a3,14a4 <SystemInit+0x104>
    14ae:	43d8                	lw	a4,4(a5)
    14b0:	400216b7          	lui	a3,0x40021
    14b4:	9b71                	andi	a4,a4,-4
    14b6:	c3d8                	sw	a4,4(a5)
    14b8:	43d8                	lw	a4,4(a5)
    14ba:	00276713          	ori	a4,a4,2
    14be:	c3d8                	sw	a4,4(a5)
    14c0:	4721                	li	a4,8
    14c2:	42dc                	lw	a5,4(a3)
    14c4:	8bb1                	andi	a5,a5,12
    14c6:	fee79ee3          	bne	a5,a4,14c2 <SystemInit+0x122>
    14ca:	0121                	addi	sp,sp,8
    14cc:	bdffe06f          	j	aa <__riscv_restore_0>
    14d0:	c202                	sw	zero,4(sp)
    14d2:	bf51                	j	1466 <SystemInit+0xc6>

000014d4 <SystemCoreClockUpdate>:
    14d4:	bcdfe2ef          	jal	t0,a0 <__riscv_save_0>
    14d8:	40021737          	lui	a4,0x40021
    14dc:	435c                	lw	a5,4(a4)
    14de:	20000437          	lui	s0,0x20000
    14e2:	4691                	li	a3,4
    14e4:	8bb1                	andi	a5,a5,12
    14e6:	01440413          	addi	s0,s0,20 # 20000014 <SystemCoreClock>
    14ea:	00d78563          	beq	a5,a3,14f4 <SystemCoreClockUpdate+0x20>
    14ee:	46a1                	li	a3,8
    14f0:	04d78263          	beq	a5,a3,1534 <SystemCoreClockUpdate+0x60>
    14f4:	016e37b7          	lui	a5,0x16e3
    14f8:	60078793          	addi	a5,a5,1536 # 16e3600 <_data_lma+0x16e08a0>
    14fc:	c01c                	sw	a5,0(s0)
    14fe:	400216b7          	lui	a3,0x40021
    1502:	42dc                	lw	a5,4(a3)
    1504:	4008                	lw	a0,0(s0)
    1506:	8391                	srli	a5,a5,0x4
    1508:	00f7f713          	andi	a4,a5,15
    150c:	200007b7          	lui	a5,0x20000
    1510:	00478793          	addi	a5,a5,4 # 20000004 <AHBPrescTable>
    1514:	97ba                	add	a5,a5,a4
    1516:	238c                	lbu	a1,0(a5)
    1518:	42dc                	lw	a5,4(a3)
    151a:	0ff5f593          	andi	a1,a1,255
    151e:	0807f793          	andi	a5,a5,128
    1522:	00b55733          	srl	a4,a0,a1
    1526:	e781                	bnez	a5,152e <SystemCoreClockUpdate+0x5a>
    1528:	badfe0ef          	jal	ra,d4 <__udivsi3>
    152c:	872a                	mv	a4,a0
    152e:	c018                	sw	a4,0(s0)
    1530:	b7bfe06f          	j	aa <__riscv_restore_0>
    1534:	435c                	lw	a5,4(a4)
    1536:	02dc77b7          	lui	a5,0x2dc7
    153a:	c0078793          	addi	a5,a5,-1024 # 2dc6c00 <_data_lma+0x2dc3ea0>
    153e:	bf7d                	j	14fc <SystemCoreClockUpdate+0x28>

00001540 <Touch_Button_GPIO_Config>:
    1540:	b61fe2ef          	jal	t0,a0 <__riscv_save_0>
    1544:	1151                	addi	sp,sp,-12
    1546:	4585                	li	a1,1
    1548:	02000513          	li	a0,32
    154c:	c002                	sw	zero,0(sp)
    154e:	c202                	sw	zero,4(sp)
    1550:	c402                	sw	zero,8(sp)
    1552:	2d29                	jal	1b6c <RCC_APB2PeriphClockCmd>
    1554:	4785                	li	a5,1
    1556:	40011537          	lui	a0,0x40011
    155a:	807c                	sh	a5,0(sp)
    155c:	858a                	mv	a1,sp
    155e:	04800793          	li	a5,72
    1562:	40050513          	addi	a0,a0,1024 # 40011400 <__global_pointer$+0x20010bc0>
    1566:	c43e                	sw	a5,8(sp)
    1568:	2181                	jal	19a8 <GPIO_Init>
    156a:	0131                	addi	sp,sp,12
    156c:	b3ffe06f          	j	aa <__riscv_restore_0>

00001570 <Touch_Button_Timer_Init>:
    1570:	b31fe2ef          	jal	t0,a0 <__riscv_save_0>
    1574:	1131                	addi	sp,sp,-20
    1576:	4585                	li	a1,1
    1578:	4505                	li	a0,1
    157a:	c402                	sw	zero,8(sp)
    157c:	c602                	sw	zero,12(sp)
    157e:	00011823          	sh	zero,16(sp)
    1582:	c002                	sw	zero,0(sp)
    1584:	c202                	sw	zero,4(sp)
    1586:	2511                	jal	1b8a <RCC_APB1PeriphClockCmd>
    1588:	02f00793          	li	a5,47
    158c:	c43e                	sw	a5,8(sp)
    158e:	002c                	addi	a1,sp,8
    1590:	3e700793          	li	a5,999
    1594:	40000537          	lui	a0,0x40000
    1598:	c63e                	sw	a5,12(sp)
    159a:	2539                	jal	1ba8 <TIM_TimeBaseInit>
    159c:	4605                	li	a2,1
    159e:	4585                	li	a1,1
    15a0:	40000537          	lui	a0,0x40000
    15a4:	25ed                	jal	1c8e <TIM_ITConfig>
    15a6:	22600793          	li	a5,550
    15aa:	807c                	sh	a5,0(sp)
    15ac:	850a                	mv	a0,sp
    15ae:	4785                	li	a5,1
    15b0:	c23e                	sw	a5,4(sp)
    15b2:	00010123          	sb	zero,2(sp)
    15b6:	2145                	jal	1a56 <NVIC_Init>
    15b8:	4585                	li	a1,1
    15ba:	40000537          	lui	a0,0x40000
    15be:	254d                	jal	1c60 <TIM_Cmd>
    15c0:	0151                	addi	sp,sp,20
    15c2:	ae9fe06f          	j	aa <__riscv_restore_0>

000015c6 <Touch_Button_EXTI_Config>:
    15c6:	adbfe2ef          	jal	t0,a0 <__riscv_save_0>
    15ca:	1121                	addi	sp,sp,-24
    15cc:	4585                	li	a1,1
    15ce:	4505                	li	a0,1
    15d0:	c402                	sw	zero,8(sp)
    15d2:	c602                	sw	zero,12(sp)
    15d4:	c802                	sw	zero,16(sp)
    15d6:	ca02                	sw	zero,20(sp)
    15d8:	c002                	sw	zero,0(sp)
    15da:	c202                	sw	zero,4(sp)
    15dc:	2b41                	jal	1b6c <RCC_APB2PeriphClockCmd>
    15de:	4581                	li	a1,0
    15e0:	450d                	li	a0,3
    15e2:	21b1                	jal	1a2e <GPIO_EXTILineConfig>
    15e4:	4405                	li	s0,1
    15e6:	47c1                	li	a5,16
    15e8:	0028                	addi	a0,sp,8
    15ea:	c422                	sw	s0,8(sp)
    15ec:	c83e                	sw	a5,16(sp)
    15ee:	ca22                	sw	s0,20(sp)
    15f0:	c602                	sw	zero,12(sp)
    15f2:	2615                	jal	1916 <EXTI_Init>
    15f4:	11400793          	li	a5,276
    15f8:	850a                	mv	a0,sp
    15fa:	807c                	sh	a5,0(sp)
    15fc:	8140                	sb	s0,2(sp)
    15fe:	c222                	sw	s0,4(sp)
    1600:	2999                	jal	1a56 <NVIC_Init>
    1602:	0161                	addi	sp,sp,24
    1604:	aa7fe06f          	j	aa <__riscv_restore_0>

00001608 <Touch_Button_Init>:
    1608:	a99fe2ef          	jal	t0,a0 <__riscv_save_0>
    160c:	99018793          	addi	a5,gp,-1648 # 200001d0 <touch_button>
    1610:	00079823          	sh	zero,16(a5)
    1614:	0007a023          	sw	zero,0(a5)
    1618:	0007a223          	sw	zero,4(a5)
    161c:	0007a423          	sw	zero,8(a5)
    1620:	0007a623          	sw	zero,12(a5)
    1624:	00078923          	sb	zero,18(a5)
    1628:	3f21                	jal	1540 <Touch_Button_GPIO_Config>
    162a:	3f71                	jal	15c6 <Touch_Button_EXTI_Config>
    162c:	3791                	jal	1570 <Touch_Button_Timer_Init>
    162e:	a7dfe06f          	j	aa <__riscv_restore_0>

00001632 <Touch_Button_Update>:
    1632:	98c1a683          	lw	a3,-1652(gp) # 200001cc <system_tick_ms>
    1636:	99018793          	addi	a5,gp,-1648 # 200001d0 <touch_button>
    163a:	4790                	lw	a2,8(a5)
    163c:	438c                	lw	a1,0(a5)
    163e:	4505                	li	a0,1
    1640:	40c68633          	sub	a2,a3,a2
    1644:	99018713          	addi	a4,gp,-1648 # 200001d0 <touch_button>
    1648:	02a58663          	beq	a1,a0,1674 <Touch_Button_Update+0x42>
    164c:	c589                	beqz	a1,1656 <Touch_Button_Update+0x24>
    164e:	478d                	li	a5,3
    1650:	04f58063          	beq	a1,a5,1690 <Touch_Button_Update+0x5e>
    1654:	8082                	ret
    1656:	3b98                	lbu	a4,17(a5)
    1658:	c729                	beqz	a4,16a2 <Touch_Button_Update+0x70>
    165a:	2bb8                	lbu	a4,18(a5)
    165c:	e339                	bnez	a4,16a2 <Touch_Button_Update+0x70>
    165e:	47d8                	lw	a4,12(a5)
    1660:	8e99                	sub	a3,a3,a4
    1662:	7cf00713          	li	a4,1999
    1666:	02d77e63          	bgeu	a4,a3,16a2 <Touch_Button_Update+0x70>
    166a:	470d                	li	a4,3
    166c:	000788a3          	sb	zero,17(a5)
    1670:	c3d8                	sw	a4,4(a5)
    1672:	8082                	ret
    1674:	3e700713          	li	a4,999
    1678:	02c77563          	bgeu	a4,a2,16a2 <Touch_Button_Update+0x70>
    167c:	4709                	li	a4,2
    167e:	c398                	sw	a4,0(a5)
    1680:	c3d8                	sw	a4,4(a5)
    1682:	2bb8                	lbu	a4,18(a5)
    1684:	bb8c                	sb	a1,17(a5)
    1686:	c7d4                	sw	a3,12(a5)
    1688:	00173713          	seqz	a4,a4
    168c:	abb8                	sb	a4,18(a5)
    168e:	8082                	ret
    1690:	3e700793          	li	a5,999
    1694:	00c7e563          	bltu	a5,a2,169e <Touch_Button_Update+0x6c>
    1698:	c348                	sw	a0,4(a4)
    169a:	bb08                	sb	a0,17(a4)
    169c:	c754                	sw	a3,12(a4)
    169e:	00072023          	sw	zero,0(a4) # 40021000 <__global_pointer$+0x200207c0>
    16a2:	8082                	ret

000016a4 <Touch_Button_Get_Event>:
    16a4:	99018793          	addi	a5,gp,-1648 # 200001d0 <touch_button>
    16a8:	43c8                	lw	a0,4(a5)
    16aa:	0007a223          	sw	zero,4(a5)
    16ae:	8082                	ret

000016b0 <Touch_Button_Get_Time_Ms>:
    16b0:	98c1a503          	lw	a0,-1652(gp) # 200001cc <system_tick_ms>
    16b4:	8082                	ret

000016b6 <Touch_Button_IRQ_Handler>:
    16b6:	9ebfe2ef          	jal	t0,a0 <__riscv_save_0>
    16ba:	4505                	li	a0,1
    16bc:	24d1                	jal	1980 <EXTI_GetITStatus>
    16be:	c505                	beqz	a0,16e6 <Touch_Button_IRQ_Handler+0x30>
    16c0:	40011537          	lui	a0,0x40011
    16c4:	4585                	li	a1,1
    16c6:	40050513          	addi	a0,a0,1024 # 40011400 <__global_pointer$+0x20010bc0>
    16ca:	98c1a403          	lw	s0,-1652(gp) # 200001cc <system_tick_ms>
    16ce:	2e99                	jal	1a24 <GPIO_ReadInputDataBit>
    16d0:	99018793          	addi	a5,gp,-1648 # 200001d0 <touch_button>
    16d4:	4398                	lw	a4,0(a5)
    16d6:	c911                	beqz	a0,16ea <Touch_Button_IRQ_Handler+0x34>
    16d8:	e709                	bnez	a4,16e2 <Touch_Button_IRQ_Handler+0x2c>
    16da:	4705                	li	a4,1
    16dc:	c398                	sw	a4,0(a5)
    16de:	c780                	sw	s0,8(a5)
    16e0:	ab98                	sb	a4,16(a5)
    16e2:	4505                	li	a0,1
    16e4:	2c6d                	jal	199e <EXTI_ClearITPendingBit>
    16e6:	9c5fe06f          	j	aa <__riscv_restore_0>
    16ea:	177d                	addi	a4,a4,-1
    16ec:	4685                	li	a3,1
    16ee:	fee6eae3          	bltu	a3,a4,16e2 <Touch_Button_IRQ_Handler+0x2c>
    16f2:	470d                	li	a4,3
    16f4:	c398                	sw	a4,0(a5)
    16f6:	00078823          	sb	zero,16(a5)
    16fa:	b7e5                	j	16e2 <Touch_Button_IRQ_Handler+0x2c>

000016fc <ADC1_IRQHandler>:
    16fc:	a001                	j	16fc <ADC1_IRQHandler>

000016fe <handle_reset>:
    16fe:	1ffff197          	auipc	gp,0x1ffff
    1702:	14218193          	addi	gp,gp,322 # 20000840 <__global_pointer$>
    1706:	fc018113          	addi	sp,gp,-64 # 20000800 <_eusrstack>
    170a:	0a000513          	li	a0,160
    170e:	1ffff597          	auipc	a1,0x1ffff
    1712:	8f258593          	addi	a1,a1,-1806 # 20000000 <_highcode_vma_end>
    1716:	1ffff617          	auipc	a2,0x1ffff
    171a:	8ea60613          	addi	a2,a2,-1814 # 20000000 <_highcode_vma_end>
    171e:	00c5fa63          	bgeu	a1,a2,1732 <handle_reset+0x34>
    1722:	00052283          	lw	t0,0(a0)
    1726:	0055a023          	sw	t0,0(a1)
    172a:	0511                	addi	a0,a0,4
    172c:	0591                	addi	a1,a1,4
    172e:	fec5eae3          	bltu	a1,a2,1722 <handle_reset+0x24>
    1732:	00001517          	auipc	a0,0x1
    1736:	62e50513          	addi	a0,a0,1582 # 2d60 <_data_lma>
    173a:	1ffff597          	auipc	a1,0x1ffff
    173e:	8c658593          	addi	a1,a1,-1850 # 20000000 <_highcode_vma_end>
    1742:	1ffff617          	auipc	a2,0x1ffff
    1746:	8fe60613          	addi	a2,a2,-1794 # 20000040 <_edata>
    174a:	00c5fa63          	bgeu	a1,a2,175e <handle_reset+0x60>
    174e:	00052283          	lw	t0,0(a0)
    1752:	0055a023          	sw	t0,0(a1)
    1756:	0511                	addi	a0,a0,4
    1758:	0591                	addi	a1,a1,4
    175a:	fec5eae3          	bltu	a1,a2,174e <handle_reset+0x50>
    175e:	1ffff517          	auipc	a0,0x1ffff
    1762:	8e250513          	addi	a0,a0,-1822 # 20000040 <_edata>
    1766:	9ac18593          	addi	a1,gp,-1620 # 200001ec <_ebss>
    176a:	00b57763          	bgeu	a0,a1,1778 <handle_reset+0x7a>
    176e:	00052023          	sw	zero,0(a0)
    1772:	0511                	addi	a0,a0,4
    1774:	feb56de3          	bltu	a0,a1,176e <handle_reset+0x70>
    1778:	000022b7          	lui	t0,0x2
    177c:	88028293          	addi	t0,t0,-1920 # 1880 <ADC_RegularChannelConfig+0x48>
    1780:	30029073          	csrw	mstatus,t0
    1784:	428d                	li	t0,3
    1786:	80429073          	csrw	0x804,t0
    178a:	fffff297          	auipc	t0,0xfffff
    178e:	87628293          	addi	t0,t0,-1930 # 0 <_sinit>
    1792:	0032e293          	ori	t0,t0,3
    1796:	30529073          	csrw	mtvec,t0
    179a:	c07ff0ef          	jal	ra,13a0 <SystemInit>
    179e:	fffff297          	auipc	t0,0xfffff
    17a2:	45428293          	addi	t0,t0,1108 # bf2 <main>
    17a6:	34129073          	csrw	mepc,t0
    17aa:	30200073          	mret

000017ae <ADC_Init>:
    17ae:	415c                	lw	a5,4(a0)
    17b0:	fff10737          	lui	a4,0xfff10
    17b4:	eff70713          	addi	a4,a4,-257 # fff0feff <__global_pointer$+0xdff0f6bf>
    17b8:	8f7d                	and	a4,a4,a5
    17ba:	41dc                	lw	a5,4(a1)
    17bc:	4194                	lw	a3,0(a1)
    17be:	07a2                	slli	a5,a5,0x8
    17c0:	8fd5                	or	a5,a5,a3
    17c2:	8fd9                	or	a5,a5,a4
    17c4:	c15c                	sw	a5,4(a0)
    17c6:	451c                	lw	a5,8(a0)
    17c8:	fff1f737          	lui	a4,0xfff1f
    17cc:	7fd70713          	addi	a4,a4,2045 # fff1f7fd <__global_pointer$+0xdff1efbd>
    17d0:	45d4                	lw	a3,12(a1)
    17d2:	8f7d                	and	a4,a4,a5
    17d4:	499c                	lw	a5,16(a1)
    17d6:	8fd5                	or	a5,a5,a3
    17d8:	4594                	lw	a3,8(a1)
    17da:	0686                	slli	a3,a3,0x1
    17dc:	8fd5                	or	a5,a5,a3
    17de:	8fd9                	or	a5,a5,a4
    17e0:	c51c                	sw	a5,8(a0)
    17e2:	555c                	lw	a5,44(a0)
    17e4:	ff100737          	lui	a4,0xff100
    17e8:	177d                	addi	a4,a4,-1
    17ea:	8f7d                	and	a4,a4,a5
    17ec:	29dc                	lbu	a5,20(a1)
    17ee:	17fd                	addi	a5,a5,-1
    17f0:	0ff7f793          	andi	a5,a5,255
    17f4:	07d2                	slli	a5,a5,0x14
    17f6:	8fd9                	or	a5,a5,a4
    17f8:	d55c                	sw	a5,44(a0)
    17fa:	8082                	ret

000017fc <ADC_Cmd>:
    17fc:	451c                	lw	a5,8(a0)
    17fe:	c589                	beqz	a1,1808 <ADC_Cmd+0xc>
    1800:	0017e793          	ori	a5,a5,1
    1804:	c51c                	sw	a5,8(a0)
    1806:	8082                	ret
    1808:	9bf9                	andi	a5,a5,-2
    180a:	bfed                	j	1804 <ADC_Cmd+0x8>

0000180c <ADC_ResetCalibration>:
    180c:	451c                	lw	a5,8(a0)
    180e:	0087e793          	ori	a5,a5,8
    1812:	c51c                	sw	a5,8(a0)
    1814:	8082                	ret

00001816 <ADC_StartCalibration>:
    1816:	451c                	lw	a5,8(a0)
    1818:	0047e793          	ori	a5,a5,4
    181c:	c51c                	sw	a5,8(a0)
    181e:	8082                	ret

00001820 <ADC_SoftwareStartConvCmd>:
    1820:	451c                	lw	a5,8(a0)
    1822:	c591                	beqz	a1,182e <ADC_SoftwareStartConvCmd+0xe>
    1824:	00500737          	lui	a4,0x500
    1828:	8fd9                	or	a5,a5,a4
    182a:	c51c                	sw	a5,8(a0)
    182c:	8082                	ret
    182e:	ffb00737          	lui	a4,0xffb00
    1832:	177d                	addi	a4,a4,-1
    1834:	8ff9                	and	a5,a5,a4
    1836:	bfd5                	j	182a <ADC_SoftwareStartConvCmd+0xa>

00001838 <ADC_RegularChannelConfig>:
    1838:	47a5                	li	a5,9
    183a:	04b7f863          	bgeu	a5,a1,188a <ADC_RegularChannelConfig+0x52>
    183e:	ff658713          	addi	a4,a1,-10
    1842:	00171793          	slli	a5,a4,0x1
    1846:	00c52283          	lw	t0,12(a0)
    184a:	97ba                	add	a5,a5,a4
    184c:	431d                	li	t1,7
    184e:	00f31333          	sll	t1,t1,a5
    1852:	fff34313          	not	t1,t1
    1856:	00537333          	and	t1,t1,t0
    185a:	00f697b3          	sll	a5,a3,a5
    185e:	0067e7b3          	or	a5,a5,t1
    1862:	c55c                	sw	a5,12(a0)
    1864:	4799                	li	a5,6
    1866:	04c7e363          	bltu	a5,a2,18ac <ADC_RegularChannelConfig+0x74>
    186a:	167d                	addi	a2,a2,-1
    186c:	00261793          	slli	a5,a2,0x2
    1870:	963e                	add	a2,a2,a5
    1872:	5958                	lw	a4,52(a0)
    1874:	47fd                	li	a5,31
    1876:	00c797b3          	sll	a5,a5,a2
    187a:	fff7c793          	not	a5,a5
    187e:	8ff9                	and	a5,a5,a4
    1880:	00c595b3          	sll	a1,a1,a2
    1884:	8ddd                	or	a1,a1,a5
    1886:	d94c                	sw	a1,52(a0)
    1888:	8082                	ret
    188a:	00159793          	slli	a5,a1,0x1
    188e:	01052303          	lw	t1,16(a0)
    1892:	97ae                	add	a5,a5,a1
    1894:	471d                	li	a4,7
    1896:	00f71733          	sll	a4,a4,a5
    189a:	fff74713          	not	a4,a4
    189e:	00677733          	and	a4,a4,t1
    18a2:	00f697b3          	sll	a5,a3,a5
    18a6:	8f5d                	or	a4,a4,a5
    18a8:	c918                	sw	a4,16(a0)
    18aa:	bf6d                	j	1864 <ADC_RegularChannelConfig+0x2c>
    18ac:	47b1                	li	a5,12
    18ae:	02c7e263          	bltu	a5,a2,18d2 <ADC_RegularChannelConfig+0x9a>
    18b2:	1665                	addi	a2,a2,-7
    18b4:	00261793          	slli	a5,a2,0x2
    18b8:	963e                	add	a2,a2,a5
    18ba:	5918                	lw	a4,48(a0)
    18bc:	47fd                	li	a5,31
    18be:	00c797b3          	sll	a5,a5,a2
    18c2:	fff7c793          	not	a5,a5
    18c6:	8ff9                	and	a5,a5,a4
    18c8:	00c595b3          	sll	a1,a1,a2
    18cc:	8ddd                	or	a1,a1,a5
    18ce:	d90c                	sw	a1,48(a0)
    18d0:	8082                	ret
    18d2:	164d                	addi	a2,a2,-13
    18d4:	00261713          	slli	a4,a2,0x2
    18d8:	5554                	lw	a3,44(a0)
    18da:	963a                	add	a2,a2,a4
    18dc:	47fd                	li	a5,31
    18de:	00c797b3          	sll	a5,a5,a2
    18e2:	fff7c793          	not	a5,a5
    18e6:	8ff5                	and	a5,a5,a3
    18e8:	00c595b3          	sll	a1,a1,a2
    18ec:	8ddd                	or	a1,a1,a5
    18ee:	d54c                	sw	a1,44(a0)
    18f0:	8082                	ret

000018f2 <ADC_GetConversionValue>:
    18f2:	4568                	lw	a0,76(a0)
    18f4:	0542                	slli	a0,a0,0x10
    18f6:	8141                	srli	a0,a0,0x10
    18f8:	8082                	ret

000018fa <ADC_GetFlagStatus>:
    18fa:	4108                	lw	a0,0(a0)
    18fc:	8d6d                	and	a0,a0,a1
    18fe:	00a03533          	snez	a0,a0
    1902:	8082                	ret

00001904 <ADC_ClearFlag>:
    1904:	fff5c593          	not	a1,a1
    1908:	c10c                	sw	a1,0(a0)
    190a:	8082                	ret

0000190c <DBGMCU_GetCHIPID>:
    190c:	1ffff7b7          	lui	a5,0x1ffff
    1910:	7c47a503          	lw	a0,1988(a5) # 1ffff7c4 <_data_lma+0x1fffca64>
    1914:	8082                	ret

00001916 <EXTI_Init>:
    1916:	4158                	lw	a4,4(a0)
    1918:	00052303          	lw	t1,0(a0)
    191c:	454c                	lw	a1,12(a0)
    191e:	40010637          	lui	a2,0x40010
    1922:	40060793          	addi	a5,a2,1024 # 40010400 <__global_pointer$+0x2000fbc0>
    1926:	973e                	add	a4,a4,a5
    1928:	fff34693          	not	a3,t1
    192c:	c5b1                	beqz	a1,1978 <EXTI_Init+0x62>
    192e:	40062583          	lw	a1,1024(a2)
    1932:	8df5                	and	a1,a1,a3
    1934:	40b62023          	sw	a1,1024(a2)
    1938:	43d0                	lw	a2,4(a5)
    193a:	8ef1                	and	a3,a3,a2
    193c:	c3d4                	sw	a3,4(a5)
    193e:	4314                	lw	a3,0(a4)
    1940:	0066e6b3          	or	a3,a3,t1
    1944:	c314                	sw	a3,0(a4)
    1946:	4118                	lw	a4,0(a0)
    1948:	4790                	lw	a2,8(a5)
    194a:	fff74693          	not	a3,a4
    194e:	8e75                	and	a2,a2,a3
    1950:	c790                	sw	a2,8(a5)
    1952:	47d0                	lw	a2,12(a5)
    1954:	8ef1                	and	a3,a3,a2
    1956:	c7d4                	sw	a3,12(a5)
    1958:	4514                	lw	a3,8(a0)
    195a:	4641                	li	a2,16
    195c:	00c69963          	bne	a3,a2,196e <EXTI_Init+0x58>
    1960:	4794                	lw	a3,8(a5)
    1962:	8ed9                	or	a3,a3,a4
    1964:	c794                	sw	a3,8(a5)
    1966:	47d4                	lw	a3,12(a5)
    1968:	8f55                	or	a4,a4,a3
    196a:	c7d8                	sw	a4,12(a5)
    196c:	8082                	ret
    196e:	97b6                	add	a5,a5,a3
    1970:	4394                	lw	a3,0(a5)
    1972:	8f55                	or	a4,a4,a3
    1974:	c398                	sw	a4,0(a5)
    1976:	8082                	ret
    1978:	431c                	lw	a5,0(a4)
    197a:	8ff5                	and	a5,a5,a3
    197c:	c31c                	sw	a5,0(a4)
    197e:	8082                	ret

00001980 <EXTI_GetITStatus>:
    1980:	400107b7          	lui	a5,0x40010
    1984:	40078713          	addi	a4,a5,1024 # 40010400 <__global_pointer$+0x2000fbc0>
    1988:	4007a783          	lw	a5,1024(a5)
    198c:	4b58                	lw	a4,20(a4)
    198e:	8f69                	and	a4,a4,a0
    1990:	c709                	beqz	a4,199a <EXTI_GetITStatus+0x1a>
    1992:	8d7d                	and	a0,a0,a5
    1994:	00a03533          	snez	a0,a0
    1998:	8082                	ret
    199a:	4501                	li	a0,0
    199c:	8082                	ret

0000199e <EXTI_ClearITPendingBit>:
    199e:	400107b7          	lui	a5,0x40010
    19a2:	40a7aa23          	sw	a0,1044(a5) # 40010414 <__global_pointer$+0x2000fbd4>
    19a6:	8082                	ret

000019a8 <GPIO_Init>:
    19a8:	4594                	lw	a3,8(a1)
    19aa:	0106f793          	andi	a5,a3,16
    19ae:	00f6f293          	andi	t0,a3,15
    19b2:	c781                	beqz	a5,19ba <GPIO_Init+0x12>
    19b4:	41dc                	lw	a5,4(a1)
    19b6:	00f2e2b3          	or	t0,t0,a5
    19ba:	0005d383          	lhu	t2,0(a1)
    19be:	0ff3f793          	andi	a5,t2,255
    19c2:	c3a5                	beqz	a5,1a22 <GPIO_Init+0x7a>
    19c4:	00052303          	lw	t1,0(a0)
    19c8:	1161                	addi	sp,sp,-8
    19ca:	c222                	sw	s0,4(sp)
    19cc:	c026                	sw	s1,0(sp)
    19ce:	4781                	li	a5,0
    19d0:	02800413          	li	s0,40
    19d4:	04800493          	li	s1,72
    19d8:	4705                	li	a4,1
    19da:	00f71633          	sll	a2,a4,a5
    19de:	00c3f733          	and	a4,t2,a2
    19e2:	02e61263          	bne	a2,a4,1a06 <GPIO_Init+0x5e>
    19e6:	00279593          	slli	a1,a5,0x2
    19ea:	473d                	li	a4,15
    19ec:	00b71733          	sll	a4,a4,a1
    19f0:	fff74713          	not	a4,a4
    19f4:	00677333          	and	t1,a4,t1
    19f8:	00b295b3          	sll	a1,t0,a1
    19fc:	0065e333          	or	t1,a1,t1
    1a00:	00869d63          	bne	a3,s0,1a1a <GPIO_Init+0x72>
    1a04:	c950                	sw	a2,20(a0)
    1a06:	0785                	addi	a5,a5,1
    1a08:	4721                	li	a4,8
    1a0a:	fce797e3          	bne	a5,a4,19d8 <GPIO_Init+0x30>
    1a0e:	4412                	lw	s0,4(sp)
    1a10:	00652023          	sw	t1,0(a0)
    1a14:	4482                	lw	s1,0(sp)
    1a16:	0121                	addi	sp,sp,8
    1a18:	8082                	ret
    1a1a:	fe9696e3          	bne	a3,s1,1a06 <GPIO_Init+0x5e>
    1a1e:	c910                	sw	a2,16(a0)
    1a20:	b7dd                	j	1a06 <GPIO_Init+0x5e>
    1a22:	8082                	ret

00001a24 <GPIO_ReadInputDataBit>:
    1a24:	4508                	lw	a0,8(a0)
    1a26:	8d6d                	and	a0,a0,a1
    1a28:	00a03533          	snez	a0,a0
    1a2c:	8082                	ret

00001a2e <GPIO_EXTILineConfig>:
    1a2e:	40010737          	lui	a4,0x40010
    1a32:	4714                	lw	a3,8(a4)
    1a34:	0586                	slli	a1,a1,0x1
    1a36:	478d                	li	a5,3
    1a38:	00b797b3          	sll	a5,a5,a1
    1a3c:	fff7c793          	not	a5,a5
    1a40:	8ff5                	and	a5,a5,a3
    1a42:	c71c                	sw	a5,8(a4)
    1a44:	471c                	lw	a5,8(a4)
    1a46:	00b515b3          	sll	a1,a0,a1
    1a4a:	8ddd                	or	a1,a1,a5
    1a4c:	c70c                	sw	a1,8(a4)
    1a4e:	8082                	ret

00001a50 <NVIC_PriorityGroupConfig>:
    1a50:	9aa1a223          	sw	a0,-1628(gp) # 200001e4 <NVIC_Priority_Group>
    1a54:	8082                	ret

00001a56 <NVIC_Init>:
    1a56:	9a41a683          	lw	a3,-1628(gp) # 200001e4 <NVIC_Priority_Group>
    1a5a:	4785                	li	a5,1
    1a5c:	2118                	lbu	a4,0(a0)
    1a5e:	02f69063          	bne	a3,a5,1a7e <NVIC_Init+0x28>
    1a62:	311c                	lbu	a5,1(a0)
    1a64:	02d79c63          	bne	a5,a3,1a9c <NVIC_Init+0x46>
    1a68:	213c                	lbu	a5,2(a0)
    1a6a:	079a                	slli	a5,a5,0x6
    1a6c:	f807e793          	ori	a5,a5,-128
    1a70:	e000e6b7          	lui	a3,0xe000e
    1a74:	0ff7f793          	andi	a5,a5,255
    1a78:	96ba                	add	a3,a3,a4
    1a7a:	40f68023          	sb	a5,1024(a3) # e000e400 <__global_pointer$+0xc000dbc0>
    1a7e:	4685                	li	a3,1
    1a80:	00575793          	srli	a5,a4,0x5
    1a84:	00e69733          	sll	a4,a3,a4
    1a88:	4154                	lw	a3,4(a0)
    1a8a:	ce89                	beqz	a3,1aa4 <NVIC_Init+0x4e>
    1a8c:	04078793          	addi	a5,a5,64
    1a90:	078a                	slli	a5,a5,0x2
    1a92:	e000e6b7          	lui	a3,0xe000e
    1a96:	97b6                	add	a5,a5,a3
    1a98:	c398                	sw	a4,0(a5)
    1a9a:	8082                	ret
    1a9c:	f3ed                	bnez	a5,1a7e <NVIC_Init+0x28>
    1a9e:	213c                	lbu	a5,2(a0)
    1aa0:	079a                	slli	a5,a5,0x6
    1aa2:	b7f9                	j	1a70 <NVIC_Init+0x1a>
    1aa4:	06078793          	addi	a5,a5,96
    1aa8:	e000e6b7          	lui	a3,0xe000e
    1aac:	078a                	slli	a5,a5,0x2
    1aae:	97b6                	add	a5,a5,a3
    1ab0:	c398                	sw	a4,0(a5)
    1ab2:	0000100f          	fence.i
    1ab6:	8082                	ret

00001ab8 <RCC_AdjustHSICalibrationValue>:
    1ab8:	40021737          	lui	a4,0x40021
    1abc:	431c                	lw	a5,0(a4)
    1abe:	050e                	slli	a0,a0,0x3
    1ac0:	f077f793          	andi	a5,a5,-249
    1ac4:	8d5d                	or	a0,a0,a5
    1ac6:	c308                	sw	a0,0(a4)
    1ac8:	8082                	ret

00001aca <RCC_GetClocksFreq>:
    1aca:	dd6fe2ef          	jal	t0,a0 <__riscv_save_0>
    1ace:	40021737          	lui	a4,0x40021
    1ad2:	435c                	lw	a5,4(a4)
    1ad4:	4691                	li	a3,4
    1ad6:	842a                	mv	s0,a0
    1ad8:	8bb1                	andi	a5,a5,12
    1ada:	00d78563          	beq	a5,a3,1ae4 <RCC_GetClocksFreq+0x1a>
    1ade:	46a1                	li	a3,8
    1ae0:	08d78063          	beq	a5,a3,1b60 <RCC_GetClocksFreq+0x96>
    1ae4:	016e37b7          	lui	a5,0x16e3
    1ae8:	60078793          	addi	a5,a5,1536 # 16e3600 <_data_lma+0x16e08a0>
    1aec:	c01c                	sw	a5,0(s0)
    1aee:	400216b7          	lui	a3,0x40021
    1af2:	42dc                	lw	a5,4(a3)
    1af4:	8391                	srli	a5,a5,0x4
    1af6:	00f7f713          	andi	a4,a5,15
    1afa:	200007b7          	lui	a5,0x20000
    1afe:	02c78793          	addi	a5,a5,44 # 2000002c <APBAHBPrescTable>
    1b02:	97ba                	add	a5,a5,a4
    1b04:	238c                	lbu	a1,0(a5)
    1b06:	42dc                	lw	a5,4(a3)
    1b08:	4018                	lw	a4,0(s0)
    1b0a:	0ff5f593          	andi	a1,a1,255
    1b0e:	0807f793          	andi	a5,a5,128
    1b12:	00b75533          	srl	a0,a4,a1
    1b16:	e781                	bnez	a5,1b1e <RCC_GetClocksFreq+0x54>
    1b18:	853a                	mv	a0,a4
    1b1a:	dbafe0ef          	jal	ra,d4 <__udivsi3>
    1b1e:	c048                	sw	a0,4(s0)
    1b20:	c408                	sw	a0,8(s0)
    1b22:	c448                	sw	a0,12(s0)
    1b24:	400217b7          	lui	a5,0x40021
    1b28:	43dc                	lw	a5,4(a5)
    1b2a:	468d                	li	a3,3
    1b2c:	83ad                	srli	a5,a5,0xb
    1b2e:	8bfd                	andi	a5,a5,31
    1b30:	0037d713          	srli	a4,a5,0x3
    1b34:	078a                	slli	a5,a5,0x2
    1b36:	8bf1                	andi	a5,a5,28
    1b38:	8fd9                	or	a5,a5,a4
    1b3a:	0137f613          	andi	a2,a5,19
    1b3e:	0037f713          	andi	a4,a5,3
    1b42:	00c6f463          	bgeu	a3,a2,1b4a <RCC_GetClocksFreq+0x80>
    1b46:	ff478713          	addi	a4,a5,-12 # 40020ff4 <__global_pointer$+0x200207b4>
    1b4a:	200007b7          	lui	a5,0x20000
    1b4e:	01878793          	addi	a5,a5,24 # 20000018 <ADCPrescTable>
    1b52:	97ba                	add	a5,a5,a4
    1b54:	238c                	lbu	a1,0(a5)
    1b56:	d7efe0ef          	jal	ra,d4 <__udivsi3>
    1b5a:	c808                	sw	a0,16(s0)
    1b5c:	d4efe06f          	j	aa <__riscv_restore_0>
    1b60:	435c                	lw	a5,4(a4)
    1b62:	02dc77b7          	lui	a5,0x2dc7
    1b66:	c0078793          	addi	a5,a5,-1024 # 2dc6c00 <_data_lma+0x2dc3ea0>
    1b6a:	b749                	j	1aec <RCC_GetClocksFreq+0x22>

00001b6c <RCC_APB2PeriphClockCmd>:
    1b6c:	c599                	beqz	a1,1b7a <RCC_APB2PeriphClockCmd+0xe>
    1b6e:	40021737          	lui	a4,0x40021
    1b72:	4f1c                	lw	a5,24(a4)
    1b74:	8d5d                	or	a0,a0,a5
    1b76:	cf08                	sw	a0,24(a4)
    1b78:	8082                	ret
    1b7a:	400217b7          	lui	a5,0x40021
    1b7e:	4f98                	lw	a4,24(a5)
    1b80:	fff54513          	not	a0,a0
    1b84:	8d79                	and	a0,a0,a4
    1b86:	cf88                	sw	a0,24(a5)
    1b88:	8082                	ret

00001b8a <RCC_APB1PeriphClockCmd>:
    1b8a:	c599                	beqz	a1,1b98 <RCC_APB1PeriphClockCmd+0xe>
    1b8c:	40021737          	lui	a4,0x40021
    1b90:	4f5c                	lw	a5,28(a4)
    1b92:	8d5d                	or	a0,a0,a5
    1b94:	cf48                	sw	a0,28(a4)
    1b96:	8082                	ret
    1b98:	400217b7          	lui	a5,0x40021
    1b9c:	4fd8                	lw	a4,28(a5)
    1b9e:	fff54513          	not	a0,a0
    1ba2:	8d79                	and	a0,a0,a4
    1ba4:	cfc8                	sw	a0,28(a5)
    1ba6:	8082                	ret

00001ba8 <TIM_TimeBaseInit>:
    1ba8:	211e                	lhu	a5,0(a0)
    1baa:	40013737          	lui	a4,0x40013
    1bae:	c0070713          	addi	a4,a4,-1024 # 40012c00 <__global_pointer$+0x200123c0>
    1bb2:	07c2                	slli	a5,a5,0x10
    1bb4:	83c1                	srli	a5,a5,0x10
    1bb6:	00e50663          	beq	a0,a4,1bc2 <TIM_TimeBaseInit+0x1a>
    1bba:	40000737          	lui	a4,0x40000
    1bbe:	00e51663          	bne	a0,a4,1bca <TIM_TimeBaseInit+0x22>
    1bc2:	21ba                	lhu	a4,2(a1)
    1bc4:	f8f7f793          	andi	a5,a5,-113
    1bc8:	8fd9                	or	a5,a5,a4
    1bca:	21fa                	lhu	a4,6(a1)
    1bcc:	cff7f793          	andi	a5,a5,-769
    1bd0:	07c2                	slli	a5,a5,0x10
    1bd2:	83c1                	srli	a5,a5,0x10
    1bd4:	8fd9                	or	a5,a5,a4
    1bd6:	a11e                	sh	a5,0(a0)
    1bd8:	21de                	lhu	a5,4(a1)
    1bda:	b55e                	sh	a5,44(a0)
    1bdc:	219e                	lhu	a5,0(a1)
    1bde:	b51e                	sh	a5,40(a0)
    1be0:	400137b7          	lui	a5,0x40013
    1be4:	c0078793          	addi	a5,a5,-1024 # 40012c00 <__global_pointer$+0x200123c0>
    1be8:	00f51463          	bne	a0,a5,1bf0 <TIM_TimeBaseInit+0x48>
    1bec:	259c                	lbu	a5,8(a1)
    1bee:	b91e                	sh	a5,48(a0)
    1bf0:	4785                	li	a5,1
    1bf2:	a95e                	sh	a5,20(a0)
    1bf4:	8082                	ret

00001bf6 <TIM_OC1Init>:
    1bf6:	311e                	lhu	a5,32(a0)
    1bf8:	2192                	lhu	a2,0(a1)
    1bfa:	0025d303          	lhu	t1,2(a1)
    1bfe:	07c2                	slli	a5,a5,0x10
    1c00:	83c1                	srli	a5,a5,0x10
    1c02:	9bf9                	andi	a5,a5,-2
    1c04:	07c2                	slli	a5,a5,0x10
    1c06:	83c1                	srli	a5,a5,0x10
    1c08:	b11e                	sh	a5,32(a0)
    1c0a:	311e                	lhu	a5,32(a0)
    1c0c:	2156                	lhu	a3,4(a0)
    1c0e:	2d1a                	lhu	a4,24(a0)
    1c10:	07c2                	slli	a5,a5,0x10
    1c12:	83c1                	srli	a5,a5,0x10
    1c14:	0742                	slli	a4,a4,0x10
    1c16:	8341                	srli	a4,a4,0x10
    1c18:	f8c77713          	andi	a4,a4,-116
    1c1c:	8f51                	or	a4,a4,a2
    1c1e:	2592                	lhu	a2,8(a1)
    1c20:	9bf5                	andi	a5,a5,-3
    1c22:	06c2                	slli	a3,a3,0x10
    1c24:	00666633          	or	a2,a2,t1
    1c28:	8fd1                	or	a5,a5,a2
    1c2a:	40013637          	lui	a2,0x40013
    1c2e:	c0060613          	addi	a2,a2,-1024 # 40012c00 <__global_pointer$+0x200123c0>
    1c32:	82c1                	srli	a3,a3,0x10
    1c34:	02c51063          	bne	a0,a2,1c54 <TIM_OC1Init+0x5e>
    1c38:	25b2                	lhu	a2,10(a1)
    1c3a:	9bdd                	andi	a5,a5,-9
    1c3c:	00e5d303          	lhu	t1,14(a1)
    1c40:	8fd1                	or	a5,a5,a2
    1c42:	21d2                	lhu	a2,4(a1)
    1c44:	9bed                	andi	a5,a5,-5
    1c46:	cff6f693          	andi	a3,a3,-769
    1c4a:	8fd1                	or	a5,a5,a2
    1c4c:	25d2                	lhu	a2,12(a1)
    1c4e:	00666633          	or	a2,a2,t1
    1c52:	8ed1                	or	a3,a3,a2
    1c54:	a156                	sh	a3,4(a0)
    1c56:	ad1a                	sh	a4,24(a0)
    1c58:	21fa                	lhu	a4,6(a1)
    1c5a:	d958                	sw	a4,52(a0)
    1c5c:	b11e                	sh	a5,32(a0)
    1c5e:	8082                	ret

00001c60 <TIM_Cmd>:
    1c60:	211e                	lhu	a5,0(a0)
    1c62:	c589                	beqz	a1,1c6c <TIM_Cmd+0xc>
    1c64:	0017e793          	ori	a5,a5,1
    1c68:	a11e                	sh	a5,0(a0)
    1c6a:	8082                	ret
    1c6c:	07c2                	slli	a5,a5,0x10
    1c6e:	83c1                	srli	a5,a5,0x10
    1c70:	9bf9                	andi	a5,a5,-2
    1c72:	07c2                	slli	a5,a5,0x10
    1c74:	83c1                	srli	a5,a5,0x10
    1c76:	bfcd                	j	1c68 <TIM_Cmd+0x8>

00001c78 <TIM_CtrlPWMOutputs>:
    1c78:	04455783          	lhu	a5,68(a0)
    1c7c:	c591                	beqz	a1,1c88 <TIM_CtrlPWMOutputs+0x10>
    1c7e:	6721                	lui	a4,0x8
    1c80:	8fd9                	or	a5,a5,a4
    1c82:	04f51223          	sh	a5,68(a0)
    1c86:	8082                	ret
    1c88:	07c6                	slli	a5,a5,0x11
    1c8a:	83c5                	srli	a5,a5,0x11
    1c8c:	bfdd                	j	1c82 <TIM_CtrlPWMOutputs+0xa>

00001c8e <TIM_ITConfig>:
    1c8e:	255e                	lhu	a5,12(a0)
    1c90:	c601                	beqz	a2,1c98 <TIM_ITConfig+0xa>
    1c92:	8ddd                	or	a1,a1,a5
    1c94:	a54e                	sh	a1,12(a0)
    1c96:	8082                	ret
    1c98:	fff5c593          	not	a1,a1
    1c9c:	8dfd                	and	a1,a1,a5
    1c9e:	bfdd                	j	1c94 <TIM_ITConfig+0x6>

00001ca0 <TIM_ARRPreloadConfig>:
    1ca0:	211e                	lhu	a5,0(a0)
    1ca2:	c589                	beqz	a1,1cac <TIM_ARRPreloadConfig+0xc>
    1ca4:	0807e793          	ori	a5,a5,128
    1ca8:	a11e                	sh	a5,0(a0)
    1caa:	8082                	ret
    1cac:	07c2                	slli	a5,a5,0x10
    1cae:	83c1                	srli	a5,a5,0x10
    1cb0:	f7f7f793          	andi	a5,a5,-129
    1cb4:	07c2                	slli	a5,a5,0x10
    1cb6:	83c1                	srli	a5,a5,0x10
    1cb8:	bfc5                	j	1ca8 <TIM_ARRPreloadConfig+0x8>

00001cba <TIM_OC1PreloadConfig>:
    1cba:	2d1e                	lhu	a5,24(a0)
    1cbc:	07c2                	slli	a5,a5,0x10
    1cbe:	83c1                	srli	a5,a5,0x10
    1cc0:	9bdd                	andi	a5,a5,-9
    1cc2:	8ddd                	or	a1,a1,a5
    1cc4:	ad0e                	sh	a1,24(a0)
    1cc6:	8082                	ret

00001cc8 <TIM_SetCompare1>:
    1cc8:	d94c                	sw	a1,52(a0)
    1cca:	8082                	ret

00001ccc <TIM_GetITStatus>:
    1ccc:	291e                	lhu	a5,16(a0)
    1cce:	254a                	lhu	a0,12(a0)
    1cd0:	8fed                	and	a5,a5,a1
    1cd2:	0542                	slli	a0,a0,0x10
    1cd4:	8141                	srli	a0,a0,0x10
    1cd6:	c789                	beqz	a5,1ce0 <TIM_GetITStatus+0x14>
    1cd8:	8d6d                	and	a0,a0,a1
    1cda:	00a03533          	snez	a0,a0
    1cde:	8082                	ret
    1ce0:	4501                	li	a0,0
    1ce2:	8082                	ret

00001ce4 <TIM_ClearITPendingBit>:
    1ce4:	fff5c593          	not	a1,a1
    1ce8:	05c2                	slli	a1,a1,0x10
    1cea:	81c1                	srli	a1,a1,0x10
    1cec:	a90e                	sh	a1,16(a0)
    1cee:	8082                	ret

00001cf0 <USART_Init>:
    1cf0:	bb0fe2ef          	jal	t0,a0 <__riscv_save_0>
    1cf4:	2916                	lhu	a3,16(a0)
    1cf6:	77f5                	lui	a5,0xffffd
    1cf8:	17fd                	addi	a5,a5,-1
    1cfa:	8ff5                	and	a5,a5,a3
    1cfc:	21f6                	lhu	a3,6(a1)
    1cfe:	25da                	lhu	a4,12(a1)
    1d00:	1121                	addi	sp,sp,-24
    1d02:	8fd5                	or	a5,a5,a3
    1d04:	a91e                	sh	a5,16(a0)
    1d06:	2556                	lhu	a3,12(a0)
    1d08:	77fd                	lui	a5,0xfffff
    1d0a:	9f378793          	addi	a5,a5,-1549 # ffffe9f3 <__global_pointer$+0xdfffe1b3>
    1d0e:	8ff5                	and	a5,a5,a3
    1d10:	21d6                	lhu	a3,4(a1)
    1d12:	842a                	mv	s0,a0
    1d14:	c02e                	sw	a1,0(sp)
    1d16:	8fd5                	or	a5,a5,a3
    1d18:	2596                	lhu	a3,8(a1)
    1d1a:	8fd5                	or	a5,a5,a3
    1d1c:	25b6                	lhu	a3,10(a1)
    1d1e:	8fd5                	or	a5,a5,a3
    1d20:	a55e                	sh	a5,12(a0)
    1d22:	295e                	lhu	a5,20(a0)
    1d24:	07c2                	slli	a5,a5,0x10
    1d26:	83c1                	srli	a5,a5,0x10
    1d28:	cff7f793          	andi	a5,a5,-769
    1d2c:	8f5d                	or	a4,a4,a5
    1d2e:	a95a                	sh	a4,20(a0)
    1d30:	0048                	addi	a0,sp,4
    1d32:	d99ff0ef          	jal	ra,1aca <RCC_GetClocksFreq>
    1d36:	400147b7          	lui	a5,0x40014
    1d3a:	80078793          	addi	a5,a5,-2048 # 40013800 <__global_pointer$+0x20012fc0>
    1d3e:	4582                	lw	a1,0(sp)
    1d40:	06f41263          	bne	s0,a5,1da4 <USART_Init+0xb4>
    1d44:	47c2                	lw	a5,16(sp)
    1d46:	245a                	lhu	a4,12(s0)
    1d48:	00179513          	slli	a0,a5,0x1
    1d4c:	953e                	add	a0,a0,a5
    1d4e:	0742                	slli	a4,a4,0x10
    1d50:	050e                	slli	a0,a0,0x3
    1d52:	8741                	srai	a4,a4,0x10
    1d54:	953e                	add	a0,a0,a5
    1d56:	418c                	lw	a1,0(a1)
    1d58:	04075863          	bgez	a4,1da8 <USART_Init+0xb8>
    1d5c:	0586                	slli	a1,a1,0x1
    1d5e:	b76fe0ef          	jal	ra,d4 <__udivsi3>
    1d62:	06400593          	li	a1,100
    1d66:	c02a                	sw	a0,0(sp)
    1d68:	b6cfe0ef          	jal	ra,d4 <__udivsi3>
    1d6c:	4782                	lw	a5,0(sp)
    1d6e:	00451493          	slli	s1,a0,0x4
    1d72:	06400593          	li	a1,100
    1d76:	853e                	mv	a0,a5
    1d78:	b88fe0ef          	jal	ra,100 <__umodsi3>
    1d7c:	245e                	lhu	a5,12(s0)
    1d7e:	07c2                	slli	a5,a5,0x10
    1d80:	87c1                	srai	a5,a5,0x10
    1d82:	0207d563          	bgez	a5,1dac <USART_Init+0xbc>
    1d86:	050e                	slli	a0,a0,0x3
    1d88:	06400593          	li	a1,100
    1d8c:	03250513          	addi	a0,a0,50
    1d90:	b44fe0ef          	jal	ra,d4 <__udivsi3>
    1d94:	891d                	andi	a0,a0,7
    1d96:	8cc9                	or	s1,s1,a0
    1d98:	04c2                	slli	s1,s1,0x10
    1d9a:	80c1                	srli	s1,s1,0x10
    1d9c:	a406                	sh	s1,8(s0)
    1d9e:	0161                	addi	sp,sp,24
    1da0:	b0afe06f          	j	aa <__riscv_restore_0>
    1da4:	47b2                	lw	a5,12(sp)
    1da6:	b745                	j	1d46 <USART_Init+0x56>
    1da8:	058a                	slli	a1,a1,0x2
    1daa:	bf55                	j	1d5e <USART_Init+0x6e>
    1dac:	0512                	slli	a0,a0,0x4
    1dae:	06400593          	li	a1,100
    1db2:	03250513          	addi	a0,a0,50
    1db6:	b1efe0ef          	jal	ra,d4 <__udivsi3>
    1dba:	893d                	andi	a0,a0,15
    1dbc:	bfe9                	j	1d96 <USART_Init+0xa6>

00001dbe <USART_Cmd>:
    1dbe:	c591                	beqz	a1,1dca <USART_Cmd+0xc>
    1dc0:	255e                	lhu	a5,12(a0)
    1dc2:	6709                	lui	a4,0x2
    1dc4:	8fd9                	or	a5,a5,a4
    1dc6:	a55e                	sh	a5,12(a0)
    1dc8:	8082                	ret
    1dca:	255a                	lhu	a4,12(a0)
    1dcc:	77f9                	lui	a5,0xffffe
    1dce:	17fd                	addi	a5,a5,-1
    1dd0:	8ff9                	and	a5,a5,a4
    1dd2:	bfd5                	j	1dc6 <USART_Cmd+0x8>

00001dd4 <USART_SendData>:
    1dd4:	1ff5f593          	andi	a1,a1,511
    1dd8:	a14e                	sh	a1,4(a0)
    1dda:	8082                	ret

00001ddc <USART_GetFlagStatus>:
    1ddc:	210a                	lhu	a0,0(a0)
    1dde:	8d6d                	and	a0,a0,a1
    1de0:	00a03533          	snez	a0,a0
    1de4:	8082                	ret

00001de6 <Delay_Init>:
    1de6:	abafe2ef          	jal	t0,a0 <__riscv_save_0>
    1dea:	200007b7          	lui	a5,0x20000
    1dee:	0147a503          	lw	a0,20(a5) # 20000014 <SystemCoreClock>
    1df2:	007a15b7          	lui	a1,0x7a1
    1df6:	20058593          	addi	a1,a1,512 # 7a1200 <_data_lma+0x79e4a0>
    1dfa:	adafe0ef          	jal	ra,d4 <__udivsi3>
    1dfe:	0ff57513          	andi	a0,a0,255
    1e02:	9aa18523          	sb	a0,-1622(gp) # 200001ea <p_us>
    1e06:	00551793          	slli	a5,a0,0x5
    1e0a:	8f89                	sub	a5,a5,a0
    1e0c:	078a                	slli	a5,a5,0x2
    1e0e:	953e                	add	a0,a0,a5
    1e10:	050e                	slli	a0,a0,0x3
    1e12:	9aa19423          	sh	a0,-1624(gp) # 200001e8 <p_ms>
    1e16:	a94fe06f          	j	aa <__riscv_restore_0>

00001e1a <Delay_Ms>:
    1e1a:	a86fe2ef          	jal	t0,a0 <__riscv_save_0>
    1e1e:	e000f437          	lui	s0,0xe000f
    1e22:	405c                	lw	a5,4(s0)
    1e24:	85aa                	mv	a1,a0
    1e26:	9bf9                	andi	a5,a5,-2
    1e28:	c05c                	sw	a5,4(s0)
    1e2a:	9a81d503          	lhu	a0,-1624(gp) # 200001e8 <p_ms>
    1e2e:	a86fe0ef          	jal	ra,b4 <__mulsi3>
    1e32:	c808                	sw	a0,16(s0)
    1e34:	00042423          	sw	zero,8(s0) # e000f008 <__global_pointer$+0xc000e7c8>
    1e38:	401c                	lw	a5,0(s0)
    1e3a:	0017e793          	ori	a5,a5,1
    1e3e:	c01c                	sw	a5,0(s0)
    1e40:	e000f7b7          	lui	a5,0xe000f
    1e44:	43d8                	lw	a4,4(a5)
    1e46:	8b05                	andi	a4,a4,1
    1e48:	df75                	beqz	a4,1e44 <Delay_Ms+0x2a>
    1e4a:	4398                	lw	a4,0(a5)
    1e4c:	9b79                	andi	a4,a4,-2
    1e4e:	c398                	sw	a4,0(a5)
    1e50:	a5afe06f          	j	aa <__riscv_restore_0>

00001e54 <USART_Printf_Init>:
    1e54:	a4cfe2ef          	jal	t0,a0 <__riscv_save_0>
    1e58:	842a                	mv	s0,a0
    1e5a:	6511                	lui	a0,0x4
    1e5c:	1111                	addi	sp,sp,-28
    1e5e:	4585                	li	a1,1
    1e60:	02050513          	addi	a0,a0,32 # 4020 <_data_lma+0x12c0>
    1e64:	d09ff0ef          	jal	ra,1b6c <RCC_APB2PeriphClockCmd>
    1e68:	02000793          	li	a5,32
    1e6c:	807c                	sh	a5,0(sp)
    1e6e:	40011537          	lui	a0,0x40011
    1e72:	478d                	li	a5,3
    1e74:	c23e                	sw	a5,4(sp)
    1e76:	858a                	mv	a1,sp
    1e78:	47e1                	li	a5,24
    1e7a:	40050513          	addi	a0,a0,1024 # 40011400 <__global_pointer$+0x20010bc0>
    1e7e:	c43e                	sw	a5,8(sp)
    1e80:	b29ff0ef          	jal	ra,19a8 <GPIO_Init>
    1e84:	c622                	sw	s0,12(sp)
    1e86:	40014437          	lui	s0,0x40014
    1e8a:	000807b7          	lui	a5,0x80
    1e8e:	006c                	addi	a1,sp,12
    1e90:	80040513          	addi	a0,s0,-2048 # 40013800 <__global_pointer$+0x20012fc0>
    1e94:	ca3e                	sw	a5,20(sp)
    1e96:	c802                	sw	zero,16(sp)
    1e98:	00011c23          	sh	zero,24(sp)
    1e9c:	e55ff0ef          	jal	ra,1cf0 <USART_Init>
    1ea0:	4585                	li	a1,1
    1ea2:	80040513          	addi	a0,s0,-2048
    1ea6:	f19ff0ef          	jal	ra,1dbe <USART_Cmd>
    1eaa:	0171                	addi	sp,sp,28
    1eac:	9fefe06f          	j	aa <__riscv_restore_0>

00001eb0 <_write>:
    1eb0:	9f0fe2ef          	jal	t0,a0 <__riscv_save_0>
    1eb4:	1171                	addi	sp,sp,-4
    1eb6:	84ae                	mv	s1,a1
    1eb8:	4401                	li	s0,0
    1eba:	02c45f63          	bge	s0,a2,1ef8 <_write+0x48>
    1ebe:	400147b7          	lui	a5,0x40014
    1ec2:	80078713          	addi	a4,a5,-2048 # 40013800 <__global_pointer$+0x20012fc0>
    1ec6:	853a                	mv	a0,a4
    1ec8:	04000593          	li	a1,64
    1ecc:	c032                	sw	a2,0(sp)
    1ece:	f0fff0ef          	jal	ra,1ddc <USART_GetFlagStatus>
    1ed2:	400147b7          	lui	a5,0x40014
    1ed6:	80078713          	addi	a4,a5,-2048 # 40013800 <__global_pointer$+0x20012fc0>
    1eda:	4602                	lw	a2,0(sp)
    1edc:	d56d                	beqz	a0,1ec6 <_write+0x16>
    1ede:	00848733          	add	a4,s1,s0
    1ee2:	00070583          	lb	a1,0(a4) # 2000 <prints+0xb6>
    1ee6:	80078513          	addi	a0,a5,-2048
    1eea:	0405                	addi	s0,s0,1
    1eec:	05c2                	slli	a1,a1,0x10
    1eee:	81c1                	srli	a1,a1,0x10
    1ef0:	ee5ff0ef          	jal	ra,1dd4 <USART_SendData>
    1ef4:	4602                	lw	a2,0(sp)
    1ef6:	b7d1                	j	1eba <_write+0xa>
    1ef8:	8532                	mv	a0,a2
    1efa:	0111                	addi	sp,sp,4
    1efc:	9aefe06f          	j	aa <__riscv_restore_0>

00001f00 <printchar>:
    1f00:	1141                	addi	sp,sp,-16
    1f02:	c606                	sw	ra,12(sp)
    1f04:	c02e                	sw	a1,0(sp)
    1f06:	cd0d                	beqz	a0,1f40 <printchar+0x40>
    1f08:	4118                	lw	a4,0(a0)
    1f0a:	87aa                	mv	a5,a0
    1f0c:	c305                	beqz	a4,1f2c <printchar+0x2c>
    1f0e:	4158                	lw	a4,4(a0)
    1f10:	557d                	li	a0,-1
    1f12:	cb11                	beqz	a4,1f26 <printchar+0x26>
    1f14:	4685                	li	a3,1
    1f16:	00d71b63          	bne	a4,a3,1f2c <printchar+0x2c>
    1f1a:	4798                	lw	a4,8(a5)
    1f1c:	00070023          	sb	zero,0(a4)
    1f20:	0007a223          	sw	zero,4(a5)
    1f24:	4505                	li	a0,1
    1f26:	40b2                	lw	ra,12(sp)
    1f28:	0141                	addi	sp,sp,16
    1f2a:	8082                	ret
    1f2c:	4798                	lw	a4,8(a5)
    1f2e:	4682                	lw	a3,0(sp)
    1f30:	a314                	sb	a3,0(a4)
    1f32:	4798                	lw	a4,8(a5)
    1f34:	0705                	addi	a4,a4,1
    1f36:	c798                	sw	a4,8(a5)
    1f38:	43d8                	lw	a4,4(a5)
    1f3a:	177d                	addi	a4,a4,-1
    1f3c:	c3d8                	sw	a4,4(a5)
    1f3e:	b7dd                	j	1f24 <printchar+0x24>
    1f40:	4605                	li	a2,1
    1f42:	858a                	mv	a1,sp
    1f44:	f6dff0ef          	jal	ra,1eb0 <_write>
    1f48:	bff1                	j	1f24 <printchar+0x24>

00001f4a <prints>:
    1f4a:	1101                	addi	sp,sp,-32
    1f4c:	cc22                	sw	s0,24(sp)
    1f4e:	c22e                	sw	a1,4(sp)
    1f50:	ce06                	sw	ra,28(sp)
    1f52:	ca26                	sw	s1,20(sp)
    1f54:	842a                	mv	s0,a0
    1f56:	4781                	li	a5,0
    1f58:	02000593          	li	a1,32
    1f5c:	02064563          	bltz	a2,1f86 <prints+0x3c>
    1f60:	4592                	lw	a1,4(sp)
    1f62:	95be                	add	a1,a1,a5
    1f64:	00058583          	lb	a1,0(a1)
    1f68:	e58d                	bnez	a1,1f92 <prints+0x48>
    1f6a:	02c7d863          	bge	a5,a2,1f9a <prints+0x50>
    1f6e:	02e7d463          	bge	a5,a4,1f96 <prints+0x4c>
    1f72:	8e19                	sub	a2,a2,a4
    1f74:	02000513          	li	a0,32
    1f78:	0026f593          	andi	a1,a3,2
    1f7c:	c02a                	sw	a0,0(sp)
    1f7e:	c589                	beqz	a1,1f88 <prints+0x3e>
    1f80:	e701                	bnez	a4,1f88 <prints+0x3e>
    1f82:	03000593          	li	a1,48
    1f86:	c02e                	sw	a1,0(sp)
    1f88:	8a85                	andi	a3,a3,1
    1f8a:	4481                	li	s1,0
    1f8c:	ea9d                	bnez	a3,1fc2 <prints+0x78>
    1f8e:	84b2                	mv	s1,a2
    1f90:	a015                	j	1fb4 <prints+0x6a>
    1f92:	0785                	addi	a5,a5,1
    1f94:	b7f1                	j	1f60 <prints+0x16>
    1f96:	8e1d                	sub	a2,a2,a5
    1f98:	bff1                	j	1f74 <prints+0x2a>
    1f9a:	4601                	li	a2,0
    1f9c:	bfe1                	j	1f74 <prints+0x2a>
    1f9e:	4582                	lw	a1,0(sp)
    1fa0:	8522                	mv	a0,s0
    1fa2:	c83a                	sw	a4,16(sp)
    1fa4:	c632                	sw	a2,12(sp)
    1fa6:	c43e                	sw	a5,8(sp)
    1fa8:	f59ff0ef          	jal	ra,1f00 <printchar>
    1fac:	47a2                	lw	a5,8(sp)
    1fae:	4632                	lw	a2,12(sp)
    1fb0:	4742                	lw	a4,16(sp)
    1fb2:	14fd                	addi	s1,s1,-1
    1fb4:	fe9045e3          	bgtz	s1,1f9e <prints+0x54>
    1fb8:	84b2                	mv	s1,a2
    1fba:	00065363          	bgez	a2,1fc0 <prints+0x76>
    1fbe:	4481                	li	s1,0
    1fc0:	8e05                	sub	a2,a2,s1
    1fc2:	02e7c763          	blt	a5,a4,1ff0 <prints+0xa6>
    1fc6:	87a6                	mv	a5,s1
    1fc8:	4692                	lw	a3,4(sp)
    1fca:	40978733          	sub	a4,a5,s1
    1fce:	9736                	add	a4,a4,a3
    1fd0:	00070583          	lb	a1,0(a4)
    1fd4:	ed9d                	bnez	a1,2012 <prints+0xc8>
    1fd6:	84b2                	mv	s1,a2
    1fd8:	04904663          	bgtz	s1,2024 <prints+0xda>
    1fdc:	00065363          	bgez	a2,1fe2 <prints+0x98>
    1fe0:	4601                	li	a2,0
    1fe2:	40f2                	lw	ra,28(sp)
    1fe4:	4462                	lw	s0,24(sp)
    1fe6:	44d2                	lw	s1,20(sp)
    1fe8:	00f60533          	add	a0,a2,a5
    1fec:	6105                	addi	sp,sp,32
    1fee:	8082                	ret
    1ff0:	8f1d                	sub	a4,a4,a5
    1ff2:	87ba                	mv	a5,a4
    1ff4:	03000593          	li	a1,48
    1ff8:	8522                	mv	a0,s0
    1ffa:	c832                	sw	a2,16(sp)
    1ffc:	c63e                	sw	a5,12(sp)
    1ffe:	c43a                	sw	a4,8(sp)
    2000:	f01ff0ef          	jal	ra,1f00 <printchar>
    2004:	47b2                	lw	a5,12(sp)
    2006:	4722                	lw	a4,8(sp)
    2008:	4642                	lw	a2,16(sp)
    200a:	17fd                	addi	a5,a5,-1
    200c:	f7e5                	bnez	a5,1ff4 <prints+0xaa>
    200e:	94ba                	add	s1,s1,a4
    2010:	bf5d                	j	1fc6 <prints+0x7c>
    2012:	8522                	mv	a0,s0
    2014:	c632                	sw	a2,12(sp)
    2016:	c43e                	sw	a5,8(sp)
    2018:	ee9ff0ef          	jal	ra,1f00 <printchar>
    201c:	47a2                	lw	a5,8(sp)
    201e:	4632                	lw	a2,12(sp)
    2020:	0785                	addi	a5,a5,1
    2022:	b75d                	j	1fc8 <prints+0x7e>
    2024:	4582                	lw	a1,0(sp)
    2026:	8522                	mv	a0,s0
    2028:	c432                	sw	a2,8(sp)
    202a:	c23e                	sw	a5,4(sp)
    202c:	ed5ff0ef          	jal	ra,1f00 <printchar>
    2030:	14fd                	addi	s1,s1,-1
    2032:	4622                	lw	a2,8(sp)
    2034:	4792                	lw	a5,4(sp)
    2036:	b74d                	j	1fd8 <prints+0x8e>

00002038 <printInt>:
    2038:	7139                	addi	sp,sp,-64
    203a:	de06                	sw	ra,60(sp)
    203c:	dc22                	sw	s0,56(sp)
    203e:	da26                	sw	s1,52(sp)
    2040:	c23e                	sw	a5,4(sp)
    2042:	8332                	mv	t1,a2
    2044:	863a                	mv	a2,a4
    2046:	ed91                	bnez	a1,2062 <printInt+0x2a>
    2048:	4692                	lw	a3,4(sp)
    204a:	03000793          	li	a5,48
    204e:	4701                	li	a4,0
    2050:	086c                	addi	a1,sp,28
    2052:	86fc                	sh	a5,28(sp)
    2054:	ef7ff0ef          	jal	ra,1f4a <prints>
    2058:	50f2                	lw	ra,60(sp)
    205a:	5462                	lw	s0,56(sp)
    205c:	54d2                	lw	s1,52(sp)
    205e:	6121                	addi	sp,sp,64
    2060:	8082                	ret
    2062:	84aa                	mv	s1,a0
    2064:	8436                	mv	s0,a3
    2066:	87ae                	mv	a5,a1
    2068:	ca91                	beqz	a3,207c <printInt+0x44>
    206a:	4729                	li	a4,10
    206c:	4401                	li	s0,0
    206e:	00e31763          	bne	t1,a4,207c <printInt+0x44>
    2072:	0005d563          	bgez	a1,207c <printInt+0x44>
    2076:	40b007b3          	neg	a5,a1
    207a:	4405                	li	s0,1
    207c:	4686                	lw	a3,64(sp)
    207e:	020109a3          	sb	zero,51(sp)
    2082:	03310713          	addi	a4,sp,51
    2086:	fc668693          	addi	a3,a3,-58 # 40020fc6 <__global_pointer$+0x20020786>
    208a:	c436                	sw	a3,8(sp)
    208c:	859a                	mv	a1,t1
    208e:	853e                	mv	a0,a5
    2090:	ca32                	sw	a2,20(sp)
    2092:	c83a                	sw	a4,16(sp)
    2094:	c61a                	sw	t1,12(sp)
    2096:	c03e                	sw	a5,0(sp)
    2098:	868fe0ef          	jal	ra,100 <__umodsi3>
    209c:	46a5                	li	a3,9
    209e:	4782                	lw	a5,0(sp)
    20a0:	4332                	lw	t1,12(sp)
    20a2:	4742                	lw	a4,16(sp)
    20a4:	4652                	lw	a2,20(sp)
    20a6:	00a6d463          	bge	a3,a0,20ae <printInt+0x76>
    20aa:	46a2                	lw	a3,8(sp)
    20ac:	9536                	add	a0,a0,a3
    20ae:	03050513          	addi	a0,a0,48
    20b2:	fff70693          	addi	a3,a4,-1
    20b6:	fea70fa3          	sb	a0,-1(a4)
    20ba:	859a                	mv	a1,t1
    20bc:	853e                	mv	a0,a5
    20be:	cc32                	sw	a2,24(sp)
    20c0:	ca3a                	sw	a4,20(sp)
    20c2:	c81a                	sw	t1,16(sp)
    20c4:	c63e                	sw	a5,12(sp)
    20c6:	c036                	sw	a3,0(sp)
    20c8:	80cfe0ef          	jal	ra,d4 <__udivsi3>
    20cc:	47b2                	lw	a5,12(sp)
    20ce:	4342                	lw	t1,16(sp)
    20d0:	4752                	lw	a4,20(sp)
    20d2:	4662                	lw	a2,24(sp)
    20d4:	0467fd63          	bgeu	a5,t1,212e <printInt+0xf6>
    20d8:	cc09                	beqz	s0,20f2 <printInt+0xba>
    20da:	ce29                	beqz	a2,2134 <printInt+0xfc>
    20dc:	4792                	lw	a5,4(sp)
    20de:	8b89                	andi	a5,a5,2
    20e0:	cbb1                	beqz	a5,2134 <printInt+0xfc>
    20e2:	02d00593          	li	a1,45
    20e6:	8526                	mv	a0,s1
    20e8:	c432                	sw	a2,8(sp)
    20ea:	e17ff0ef          	jal	ra,1f00 <printchar>
    20ee:	4622                	lw	a2,8(sp)
    20f0:	167d                	addi	a2,a2,-1
    20f2:	4792                	lw	a5,4(sp)
    20f4:	8b91                	andi	a5,a5,4
    20f6:	c785                	beqz	a5,211e <printInt+0xe6>
    20f8:	4706                	lw	a4,64(sp)
    20fa:	06100793          	li	a5,97
    20fe:	c432                	sw	a2,8(sp)
    2100:	03000593          	li	a1,48
    2104:	8526                	mv	a0,s1
    2106:	04f71163          	bne	a4,a5,2148 <printInt+0x110>
    210a:	df7ff0ef          	jal	ra,1f00 <printchar>
    210e:	07800593          	li	a1,120
    2112:	8526                	mv	a0,s1
    2114:	dedff0ef          	jal	ra,1f00 <printchar>
    2118:	4622                	lw	a2,8(sp)
    211a:	0409                	addi	s0,s0,2
    211c:	1679                	addi	a2,a2,-2
    211e:	4716                	lw	a4,68(sp)
    2120:	4692                	lw	a3,4(sp)
    2122:	4582                	lw	a1,0(sp)
    2124:	8526                	mv	a0,s1
    2126:	e25ff0ef          	jal	ra,1f4a <prints>
    212a:	9522                	add	a0,a0,s0
    212c:	b735                	j	2058 <printInt+0x20>
    212e:	87aa                	mv	a5,a0
    2130:	4702                	lw	a4,0(sp)
    2132:	bfa9                	j	208c <printInt+0x54>
    2134:	4682                	lw	a3,0(sp)
    2136:	02d00793          	li	a5,45
    213a:	4401                	li	s0,0
    213c:	fef68fa3          	sb	a5,-1(a3)
    2140:	ffe70793          	addi	a5,a4,-2
    2144:	c03e                	sw	a5,0(sp)
    2146:	b775                	j	20f2 <printInt+0xba>
    2148:	db9ff0ef          	jal	ra,1f00 <printchar>
    214c:	05800593          	li	a1,88
    2150:	b7c9                	j	2112 <printInt+0xda>

00002152 <printLongLongInt>:
    2152:	4501                	li	a0,0
    2154:	8082                	ret

00002156 <printDouble>:
    2156:	4501                	li	a0,0
    2158:	8082                	ret

0000215a <print>:
    215a:	fd810113          	addi	sp,sp,-40
    215e:	d022                	sw	s0,32(sp)
    2160:	ce26                	sw	s1,28(sp)
    2162:	d206                	sw	ra,36(sp)
    2164:	c42a                	sw	a0,8(sp)
    2166:	82ae                	mv	t0,a1
    2168:	8432                	mv	s0,a2
    216a:	c602                	sw	zero,12(sp)
    216c:	4481                	li	s1,0
    216e:	00028583          	lb	a1,0(t0)
    2172:	ed91                	bnez	a1,218e <print+0x34>
    2174:	47a2                	lw	a5,8(sp)
    2176:	c789                	beqz	a5,2180 <print+0x26>
    2178:	4581                	li	a1,0
    217a:	853e                	mv	a0,a5
    217c:	d85ff0ef          	jal	ra,1f00 <printchar>
    2180:	5092                	lw	ra,36(sp)
    2182:	5402                	lw	s0,32(sp)
    2184:	8526                	mv	a0,s1
    2186:	44f2                	lw	s1,28(sp)
    2188:	02810113          	addi	sp,sp,40
    218c:	8082                	ret
    218e:	02500793          	li	a5,37
    2192:	00f58963          	beq	a1,a5,21a4 <print+0x4a>
    2196:	4522                	lw	a0,8(sp)
    2198:	c816                	sw	t0,16(sp)
    219a:	0485                	addi	s1,s1,1
    219c:	d65ff0ef          	jal	ra,1f00 <printchar>
    21a0:	42c2                	lw	t0,16(sp)
    21a2:	a005                	j	21c2 <print+0x68>
    21a4:	00128783          	lb	a5,1(t0)
    21a8:	00128713          	addi	a4,t0,1
    21ac:	00b79d63          	bne	a5,a1,21c6 <print+0x6c>
    21b0:	4522                	lw	a0,8(sp)
    21b2:	02500593          	li	a1,37
    21b6:	c83a                	sw	a4,16(sp)
    21b8:	d49ff0ef          	jal	ra,1f00 <printchar>
    21bc:	4742                	lw	a4,16(sp)
    21be:	0485                	addi	s1,s1,1
    21c0:	82ba                	mv	t0,a4
    21c2:	0285                	addi	t0,t0,1
    21c4:	b76d                	j	216e <print+0x14>
    21c6:	d7dd                	beqz	a5,2174 <print+0x1a>
    21c8:	02b00693          	li	a3,43
    21cc:	04d78963          	beq	a5,a3,221e <print+0xc4>
    21d0:	00f6c863          	blt	a3,a5,21e0 <print+0x86>
    21d4:	02300693          	li	a3,35
    21d8:	04d78663          	beq	a5,a3,2224 <print+0xca>
    21dc:	4781                	li	a5,0
    21de:	a005                	j	21fe <print+0xa4>
    21e0:	02d00693          	li	a3,45
    21e4:	00d78a63          	beq	a5,a3,21f8 <print+0x9e>
    21e8:	03000693          	li	a3,48
    21ec:	fed798e3          	bne	a5,a3,21dc <print+0x82>
    21f0:	00228713          	addi	a4,t0,2
    21f4:	4789                	li	a5,2
    21f6:	a021                	j	21fe <print+0xa4>
    21f8:	00228713          	addi	a4,t0,2
    21fc:	4785                	li	a5,1
    21fe:	00070683          	lb	a3,0(a4)
    2202:	02b00613          	li	a2,43
    2206:	04c68363          	beq	a3,a2,224c <print+0xf2>
    220a:	02d64163          	blt	a2,a3,222c <print+0xd2>
    220e:	02300613          	li	a2,35
    2212:	02c68b63          	beq	a3,a2,2248 <print+0xee>
    2216:	82ba                	mv	t0,a4
    2218:	4501                	li	a0,0
    221a:	46a5                	li	a3,9
    221c:	a081                	j	225c <print+0x102>
    221e:	00228713          	addi	a4,t0,2
    2222:	bf6d                	j	21dc <print+0x82>
    2224:	00228713          	addi	a4,t0,2
    2228:	4791                	li	a5,4
    222a:	bfd1                	j	21fe <print+0xa4>
    222c:	02d00613          	li	a2,45
    2230:	00c68963          	beq	a3,a2,2242 <print+0xe8>
    2234:	03000613          	li	a2,48
    2238:	fcc69fe3          	bne	a3,a2,2216 <print+0xbc>
    223c:	0027e793          	ori	a5,a5,2
    2240:	a031                	j	224c <print+0xf2>
    2242:	0705                	addi	a4,a4,1
    2244:	4785                	li	a5,1
    2246:	bfc1                	j	2216 <print+0xbc>
    2248:	0047e793          	ori	a5,a5,4
    224c:	0705                	addi	a4,a4,1
    224e:	b7e1                	j	2216 <print+0xbc>
    2250:	00251613          	slli	a2,a0,0x2
    2254:	9532                	add	a0,a0,a2
    2256:	0506                	slli	a0,a0,0x1
    2258:	953a                	add	a0,a0,a4
    225a:	0285                	addi	t0,t0,1
    225c:	00028603          	lb	a2,0(t0)
    2260:	fd060713          	addi	a4,a2,-48
    2264:	0ff77593          	andi	a1,a4,255
    2268:	feb6f4e3          	bgeu	a3,a1,2250 <print+0xf6>
    226c:	02e00713          	li	a4,46
    2270:	4699                	li	a3,6
    2272:	00e61e63          	bne	a2,a4,228e <print+0x134>
    2276:	0285                	addi	t0,t0,1
    2278:	4681                	li	a3,0
    227a:	45a5                	li	a1,9
    227c:	00028603          	lb	a2,0(t0)
    2280:	fd060613          	addi	a2,a2,-48
    2284:	0ff67713          	andi	a4,a2,255
    2288:	02e5f563          	bgeu	a1,a4,22b2 <print+0x158>
    228c:	c636                	sw	a3,12(sp)
    228e:	00028703          	lb	a4,0(t0)
    2292:	06a00613          	li	a2,106
    2296:	0ac70d63          	beq	a4,a2,2350 <print+0x1f6>
    229a:	02e64363          	blt	a2,a4,22c0 <print+0x166>
    229e:	04c00613          	li	a2,76
    22a2:	0ac70763          	beq	a4,a2,2350 <print+0x1f6>
    22a6:	06800613          	li	a2,104
    22aa:	08c70c63          	beq	a4,a2,2342 <print+0x1e8>
    22ae:	4581                	li	a1,0
    22b0:	a82d                	j	22ea <print+0x190>
    22b2:	00269713          	slli	a4,a3,0x2
    22b6:	96ba                	add	a3,a3,a4
    22b8:	0686                	slli	a3,a3,0x1
    22ba:	96b2                	add	a3,a3,a2
    22bc:	0285                	addi	t0,t0,1
    22be:	bf7d                	j	227c <print+0x122>
    22c0:	07400613          	li	a2,116
    22c4:	08c70663          	beq	a4,a2,2350 <print+0x1f6>
    22c8:	07a00613          	li	a2,122
    22cc:	08c70263          	beq	a4,a2,2350 <print+0x1f6>
    22d0:	06c00613          	li	a2,108
    22d4:	4581                	li	a1,0
    22d6:	00c71a63          	bne	a4,a2,22ea <print+0x190>
    22da:	00128603          	lb	a2,1(t0)
    22de:	458d                	li	a1,3
    22e0:	00e61463          	bne	a2,a4,22e8 <print+0x18e>
    22e4:	0285                	addi	t0,t0,1
    22e6:	4591                	li	a1,4
    22e8:	0285                	addi	t0,t0,1
    22ea:	00028603          	lb	a2,0(t0)
    22ee:	06000393          	li	t2,96
    22f2:	06100713          	li	a4,97
    22f6:	00c3c463          	blt	t2,a2,22fe <print+0x1a4>
    22fa:	04100713          	li	a4,65
    22fe:	06700393          	li	t2,103
    2302:	06c3c463          	blt	t2,a2,236a <print+0x210>
    2306:	06500393          	li	t2,101
    230a:	18765663          	bge	a2,t2,2496 <print+0x33c>
    230e:	04700393          	li	t2,71
    2312:	04c3c163          	blt	t2,a2,2354 <print+0x1fa>
    2316:	04500593          	li	a1,69
    231a:	16b65e63          	bge	a2,a1,2496 <print+0x33c>
    231e:	04300713          	li	a4,67
    2322:	eae610e3          	bne	a2,a4,21c2 <print+0x68>
    2326:	4018                	lw	a4,0(s0)
    2328:	00440393          	addi	t2,s0,4
    232c:	ca16                	sw	t0,20(sp)
    232e:	00e10c23          	sb	a4,24(sp)
    2332:	c81e                	sw	t2,16(sp)
    2334:	00010ca3          	sb	zero,25(sp)
    2338:	4701                	li	a4,0
    233a:	86be                	mv	a3,a5
    233c:	862a                	mv	a2,a0
    233e:	082c                	addi	a1,sp,24
    2340:	a849                	j	23d2 <print+0x278>
    2342:	00128603          	lb	a2,1(t0)
    2346:	4581                	li	a1,0
    2348:	fae611e3          	bne	a2,a4,22ea <print+0x190>
    234c:	0289                	addi	t0,t0,2
    234e:	bf71                	j	22ea <print+0x190>
    2350:	0285                	addi	t0,t0,1
    2352:	bfb1                	j	22ae <print+0x154>
    2354:	06300693          	li	a3,99
    2358:	fcd607e3          	beq	a2,a3,2326 <print+0x1cc>
    235c:	06c6cf63          	blt	a3,a2,23da <print+0x280>
    2360:	05800693          	li	a3,88
    2364:	02d60363          	beq	a2,a3,238a <print+0x230>
    2368:	bda9                	j	21c2 <print+0x68>
    236a:	07300693          	li	a3,115
    236e:	04d60463          	beq	a2,a3,23b6 <print+0x25c>
    2372:	02c6cb63          	blt	a3,a2,23a8 <print+0x24e>
    2376:	06f00693          	li	a3,111
    237a:	0ed60563          	beq	a2,a3,2464 <print+0x30a>
    237e:	07000693          	li	a3,112
    2382:	0047e793          	ori	a5,a5,4
    2386:	e2d61ee3          	bne	a2,a3,21c2 <print+0x68>
    238a:	4691                	li	a3,4
    238c:	0cd59263          	bne	a1,a3,2450 <print+0x2f6>
    2390:	00840393          	addi	t2,s0,8
    2394:	400c                	lw	a1,0(s0)
    2396:	4050                	lw	a2,4(s0)
    2398:	ca16                	sw	t0,20(sp)
    239a:	c23a                	sw	a4,4(sp)
    239c:	c03e                	sw	a5,0(sp)
    239e:	c81e                	sw	t2,16(sp)
    23a0:	87aa                	mv	a5,a0
    23a2:	4701                	li	a4,0
    23a4:	46c1                	li	a3,16
    23a6:	a881                	j	23f6 <print+0x29c>
    23a8:	07500693          	li	a3,117
    23ac:	06d60b63          	beq	a2,a3,2422 <print+0x2c8>
    23b0:	07800693          	li	a3,120
    23b4:	bf45                	j	2364 <print+0x20a>
    23b6:	4018                	lw	a4,0(s0)
    23b8:	000036b7          	lui	a3,0x3
    23bc:	00440393          	addi	t2,s0,4
    23c0:	d5868593          	addi	a1,a3,-680 # 2d58 <font+0x500>
    23c4:	c311                	beqz	a4,23c8 <print+0x26e>
    23c6:	85ba                	mv	a1,a4
    23c8:	4732                	lw	a4,12(sp)
    23ca:	ca16                	sw	t0,20(sp)
    23cc:	c81e                	sw	t2,16(sp)
    23ce:	86be                	mv	a3,a5
    23d0:	862a                	mv	a2,a0
    23d2:	4522                	lw	a0,8(sp)
    23d4:	b77ff0ef          	jal	ra,1f4a <prints>
    23d8:	a015                	j	23fc <print+0x2a2>
    23da:	4691                	li	a3,4
    23dc:	02d59563          	bne	a1,a3,2406 <print+0x2ac>
    23e0:	00840393          	addi	t2,s0,8
    23e4:	400c                	lw	a1,0(s0)
    23e6:	4050                	lw	a2,4(s0)
    23e8:	ca16                	sw	t0,20(sp)
    23ea:	c23a                	sw	a4,4(sp)
    23ec:	c03e                	sw	a5,0(sp)
    23ee:	c81e                	sw	t2,16(sp)
    23f0:	87aa                	mv	a5,a0
    23f2:	4705                	li	a4,1
    23f4:	46a9                	li	a3,10
    23f6:	4522                	lw	a0,8(sp)
    23f8:	d5bff0ef          	jal	ra,2152 <printLongLongInt>
    23fc:	43c2                	lw	t2,16(sp)
    23fe:	94aa                	add	s1,s1,a0
    2400:	841e                	mv	s0,t2
    2402:	42d2                	lw	t0,20(sp)
    2404:	bb7d                	j	21c2 <print+0x68>
    2406:	46b2                	lw	a3,12(sp)
    2408:	400c                	lw	a1,0(s0)
    240a:	c816                	sw	t0,16(sp)
    240c:	c236                	sw	a3,4(sp)
    240e:	c03a                	sw	a4,0(sp)
    2410:	0411                	addi	s0,s0,4
    2412:	872a                	mv	a4,a0
    2414:	4685                	li	a3,1
    2416:	4629                	li	a2,10
    2418:	4522                	lw	a0,8(sp)
    241a:	c1fff0ef          	jal	ra,2038 <printInt>
    241e:	94aa                	add	s1,s1,a0
    2420:	b341                	j	21a0 <print+0x46>
    2422:	4691                	li	a3,4
    2424:	00d59d63          	bne	a1,a3,243e <print+0x2e4>
    2428:	00840393          	addi	t2,s0,8
    242c:	400c                	lw	a1,0(s0)
    242e:	4050                	lw	a2,4(s0)
    2430:	ca16                	sw	t0,20(sp)
    2432:	c23a                	sw	a4,4(sp)
    2434:	c03e                	sw	a5,0(sp)
    2436:	c81e                	sw	t2,16(sp)
    2438:	87aa                	mv	a5,a0
    243a:	4701                	li	a4,0
    243c:	bf65                	j	23f4 <print+0x29a>
    243e:	46b2                	lw	a3,12(sp)
    2440:	400c                	lw	a1,0(s0)
    2442:	c816                	sw	t0,16(sp)
    2444:	c236                	sw	a3,4(sp)
    2446:	c03a                	sw	a4,0(sp)
    2448:	0411                	addi	s0,s0,4
    244a:	872a                	mv	a4,a0
    244c:	4681                	li	a3,0
    244e:	b7e1                	j	2416 <print+0x2bc>
    2450:	46b2                	lw	a3,12(sp)
    2452:	c816                	sw	t0,16(sp)
    2454:	400c                	lw	a1,0(s0)
    2456:	4641                	li	a2,16
    2458:	c236                	sw	a3,4(sp)
    245a:	c03a                	sw	a4,0(sp)
    245c:	0411                	addi	s0,s0,4
    245e:	872a                	mv	a4,a0
    2460:	4681                	li	a3,0
    2462:	bf5d                	j	2418 <print+0x2be>
    2464:	4691                	li	a3,4
    2466:	00d59e63          	bne	a1,a3,2482 <print+0x328>
    246a:	00840393          	addi	t2,s0,8
    246e:	400c                	lw	a1,0(s0)
    2470:	4050                	lw	a2,4(s0)
    2472:	ca16                	sw	t0,20(sp)
    2474:	c23a                	sw	a4,4(sp)
    2476:	c03e                	sw	a5,0(sp)
    2478:	c81e                	sw	t2,16(sp)
    247a:	87aa                	mv	a5,a0
    247c:	4701                	li	a4,0
    247e:	46a1                	li	a3,8
    2480:	bf9d                	j	23f6 <print+0x29c>
    2482:	46b2                	lw	a3,12(sp)
    2484:	400c                	lw	a1,0(s0)
    2486:	c816                	sw	t0,16(sp)
    2488:	c236                	sw	a3,4(sp)
    248a:	c03a                	sw	a4,0(sp)
    248c:	0411                	addi	s0,s0,4
    248e:	872a                	mv	a4,a0
    2490:	4681                	li	a3,0
    2492:	4621                	li	a2,8
    2494:	b751                	j	2418 <print+0x2be>
    2496:	400c                	lw	a1,0(s0)
    2498:	00840613          	addi	a2,s0,8
    249c:	4040                	lw	s0,4(s0)
    249e:	c23a                	sw	a4,4(sp)
    24a0:	872a                	mv	a4,a0
    24a2:	4522                	lw	a0,8(sp)
    24a4:	c832                	sw	a2,16(sp)
    24a6:	c03e                	sw	a5,0(sp)
    24a8:	8622                	mv	a2,s0
    24aa:	87b6                	mv	a5,a3
    24ac:	46a9                	li	a3,10
    24ae:	ca16                	sw	t0,20(sp)
    24b0:	ca7ff0ef          	jal	ra,2156 <printDouble>
    24b4:	94aa                	add	s1,s1,a0
    24b6:	4442                	lw	s0,16(sp)
    24b8:	b7a9                	j	2402 <print+0x2a8>

000024ba <printf>:
    24ba:	fdc10113          	addi	sp,sp,-36
    24be:	c82e                	sw	a1,16(sp)
    24c0:	ca32                	sw	a2,20(sp)
    24c2:	85aa                	mv	a1,a0
    24c4:	0810                	addi	a2,sp,16
    24c6:	4501                	li	a0,0
    24c8:	c606                	sw	ra,12(sp)
    24ca:	cc36                	sw	a3,24(sp)
    24cc:	ce3a                	sw	a4,28(sp)
    24ce:	d03e                	sw	a5,32(sp)
    24d0:	c032                	sw	a2,0(sp)
    24d2:	c89ff0ef          	jal	ra,215a <print>
    24d6:	40b2                	lw	ra,12(sp)
    24d8:	02410113          	addi	sp,sp,36
    24dc:	8082                	ret

000024de <snprintf>:
    24de:	fd810113          	addi	sp,sp,-40
    24e2:	8332                	mv	t1,a2
    24e4:	d23e                	sw	a5,36(sp)
    24e6:	c42e                	sw	a1,8(sp)
    24e8:	c62a                	sw	a0,12(sp)
    24ea:	0870                	addi	a2,sp,28
    24ec:	4785                	li	a5,1
    24ee:	0048                	addi	a0,sp,4
    24f0:	859a                	mv	a1,t1
    24f2:	cc06                	sw	ra,24(sp)
    24f4:	ce36                	sw	a3,28(sp)
    24f6:	d03a                	sw	a4,32(sp)
    24f8:	c23e                	sw	a5,4(sp)
    24fa:	c032                	sw	a2,0(sp)
    24fc:	c5fff0ef          	jal	ra,215a <print>
    2500:	40e2                	lw	ra,24(sp)
    2502:	02810113          	addi	sp,sp,40
    2506:	8082                	ret

00002508 <puts>:
    2508:	1141                	addi	sp,sp,-16
    250a:	c422                	sw	s0,8(sp)
    250c:	c226                	sw	s1,4(sp)
    250e:	c606                	sw	ra,12(sp)
    2510:	211c                	lbu	a5,0(a0)
    2512:	84aa                	mv	s1,a0
    2514:	4401                	li	s0,0
    2516:	81dc                	sb	a5,3(sp)
    2518:	00310783          	lb	a5,3(sp)
    251c:	0405                	addi	s0,s0,1
    251e:	ef99                	bnez	a5,253c <puts+0x34>
    2520:	47a9                	li	a5,10
    2522:	00310593          	addi	a1,sp,3
    2526:	4605                	li	a2,1
    2528:	4501                	li	a0,0
    252a:	81dc                	sb	a5,3(sp)
    252c:	985ff0ef          	jal	ra,1eb0 <_write>
    2530:	8522                	mv	a0,s0
    2532:	40b2                	lw	ra,12(sp)
    2534:	4422                	lw	s0,8(sp)
    2536:	4492                	lw	s1,4(sp)
    2538:	0141                	addi	sp,sp,16
    253a:	8082                	ret
    253c:	4605                	li	a2,1
    253e:	00310593          	addi	a1,sp,3
    2542:	4501                	li	a0,0
    2544:	96dff0ef          	jal	ra,1eb0 <_write>
    2548:	008487b3          	add	a5,s1,s0
    254c:	239c                	lbu	a5,0(a5)
    254e:	81dc                	sb	a5,3(sp)
    2550:	b7e1                	j	2518 <puts+0x10>

00002552 <memcpy>:
    2552:	00a5c7b3          	xor	a5,a1,a0
    2556:	8b8d                	andi	a5,a5,3
    2558:	00c50733          	add	a4,a0,a2
    255c:	e781                	bnez	a5,2564 <memcpy+0x12>
    255e:	478d                	li	a5,3
    2560:	02c7e763          	bltu	a5,a2,258e <memcpy+0x3c>
    2564:	87aa                	mv	a5,a0
    2566:	0ae57e63          	bgeu	a0,a4,2622 <memcpy+0xd0>
    256a:	2194                	lbu	a3,0(a1)
    256c:	0785                	addi	a5,a5,1
    256e:	0585                	addi	a1,a1,1
    2570:	fed78fa3          	sb	a3,-1(a5)
    2574:	fee7ebe3          	bltu	a5,a4,256a <memcpy+0x18>
    2578:	8082                	ret
    257a:	2194                	lbu	a3,0(a1)
    257c:	0785                	addi	a5,a5,1
    257e:	0585                	addi	a1,a1,1
    2580:	fed78fa3          	sb	a3,-1(a5)
    2584:	fee7ebe3          	bltu	a5,a4,257a <memcpy+0x28>
    2588:	4402                	lw	s0,0(sp)
    258a:	0111                	addi	sp,sp,4
    258c:	8082                	ret
    258e:	00357693          	andi	a3,a0,3
    2592:	87aa                	mv	a5,a0
    2594:	ca89                	beqz	a3,25a6 <memcpy+0x54>
    2596:	2194                	lbu	a3,0(a1)
    2598:	0785                	addi	a5,a5,1
    259a:	0585                	addi	a1,a1,1
    259c:	fed78fa3          	sb	a3,-1(a5)
    25a0:	0037f693          	andi	a3,a5,3
    25a4:	bfc5                	j	2594 <memcpy+0x42>
    25a6:	ffc77693          	andi	a3,a4,-4
    25aa:	fe068613          	addi	a2,a3,-32
    25ae:	06c7f563          	bgeu	a5,a2,2618 <memcpy+0xc6>
    25b2:	1171                	addi	sp,sp,-4
    25b4:	c022                	sw	s0,0(sp)
    25b6:	49c0                	lw	s0,20(a1)
    25b8:	0005a303          	lw	t1,0(a1)
    25bc:	0085a383          	lw	t2,8(a1)
    25c0:	cbc0                	sw	s0,20(a5)
    25c2:	4d80                	lw	s0,24(a1)
    25c4:	0067a023          	sw	t1,0(a5)
    25c8:	0045a303          	lw	t1,4(a1)
    25cc:	cf80                	sw	s0,24(a5)
    25ce:	4dc0                	lw	s0,28(a1)
    25d0:	0067a223          	sw	t1,4(a5)
    25d4:	00c5a283          	lw	t0,12(a1)
    25d8:	0105a303          	lw	t1,16(a1)
    25dc:	02458593          	addi	a1,a1,36
    25e0:	cfc0                	sw	s0,28(a5)
    25e2:	ffc5a403          	lw	s0,-4(a1)
    25e6:	0077a423          	sw	t2,8(a5)
    25ea:	0057a623          	sw	t0,12(a5)
    25ee:	0067a823          	sw	t1,16(a5)
    25f2:	02478793          	addi	a5,a5,36
    25f6:	fe87ae23          	sw	s0,-4(a5)
    25fa:	fac7eee3          	bltu	a5,a2,25b6 <memcpy+0x64>
    25fe:	f8d7f3e3          	bgeu	a5,a3,2584 <memcpy+0x32>
    2602:	4190                	lw	a2,0(a1)
    2604:	0791                	addi	a5,a5,4
    2606:	0591                	addi	a1,a1,4
    2608:	fec7ae23          	sw	a2,-4(a5)
    260c:	bfcd                	j	25fe <memcpy+0xac>
    260e:	4190                	lw	a2,0(a1)
    2610:	0791                	addi	a5,a5,4
    2612:	0591                	addi	a1,a1,4
    2614:	fec7ae23          	sw	a2,-4(a5)
    2618:	fed7ebe3          	bltu	a5,a3,260e <memcpy+0xbc>
    261c:	f4e7e7e3          	bltu	a5,a4,256a <memcpy+0x18>
    2620:	8082                	ret
    2622:	8082                	ret
    2624:	1609                	addi	a2,a2,-30
    2626:	2009                	jal	2628 <memcpy+0xd6>
    2628:	1b21                	addi	s6,s6,-24
    262a:	15171913          	0x15171913
    262e:	2b1e                	lhu	a5,16(a4)
    2630:	0504                	addi	s1,sp,640
    2632:	0e02                	c.slli64	t3
    2634:	1e08140b          	0x1e08140b
    2638:	1d22                	slli	s10,s10,0x28
    263a:	1e18                	addi	a4,sp,816
    263c:	2b241a1b          	0x2b241a1b
    2640:	0606                	slli	a2,a2,0x1
    2642:	0f02                	c.slli64	t5
    2644:	6425                	lui	s0,0x9
    2646:	2525                	jal	2c6e <font+0x416>
    2648:	0000                	unimp
    264a:	0000                	unimp
    264c:	00004843          	fmadd.s	fa6,ft0,ft0,ft0,rmm
    2650:	003a                	c.slli	zero,0xe
    2652:	0000                	unimp
    2654:	4441                	li	s0,16
    2656:	6f4d2043          	fmadd.q	ft0,fs10,fs4,fa3,rdn
    265a:	696e                	flw	fs2,216(sp)
    265c:	6f74                	flw	fa3,92(a4)
    265e:	0072                	c.slli	zero,0x1c
    2660:	64254843          	0x64254843
    2664:	0000                	unimp
    2666:	0000                	unimp
    2668:	2e2d                	jal	29a2 <font+0x14a>
    266a:	2d2d                	jal	2ca4 <font+0x44c>
    266c:	0056                	c.slli	zero,0x15
    266e:	0000                	unimp
    2670:	32334843          	fmadd.d	fa6,ft6,ft3,ft6,rmm
    2674:	0056                	c.slli	zero,0x15
    2676:	0000                	unimp
    2678:	4956                	lw	s2,84(sp)
    267a:	5445                	li	s0,-15
    267c:	414e                	lw	sp,208(sp)
    267e:	004d                	c.nop	19
    2680:	6425                	lui	s0,0x9
    2682:	252e                	lhu	a1,10(a0)
    2684:	3330                	lbu	a2,3(a4)
    2686:	5664                	lw	s1,108(a2)
    2688:	0000                	unimp
    268a:	0000                	unimp
    268c:	6425                	lui	s0,0x9
    268e:	252e                	lhu	a1,10(a0)
    2690:	3230                	lbu	a2,3(a2)
    2692:	5664                	lw	s1,108(a2)
    2694:	0000                	unimp
	...

00002698 <CSWTCH.3>:
    2698:	07ff 07e0 ffe0 f81f 4843 3233 3056 3330     ........CH32V003
    26a8:	4120 4344 0000 0000 6f4d 696e 6f74 2072      ADC....Monitor 
    26b8:	3176 302e 0000 0000 6f54 6375 3a68 5420     v1.0....Touch: T
    26c8:	7275 206e 6e4f 0000 6f48 646c 203a 6f54     urn On..Hold: To
    26d8:	6767 656c 0000 0000 6568 6c6c 006f 0000     ggle....hello...
    26e8:	4843 3233 3056 3330 0000 0000 4441 2043     CH32V003....ADC 
    26f8:	6e69 7469 6169 696c 657a 0d64 0000 0000     initialized.....
    2708:	5750 204d 6e69 7469 6169 696c 657a 0d64     PWM initialized.
    2718:	0000 0000 6f54 6375 2068 7562 7474 6e6f     ....Touch button
    2728:	6920 696e 6974 6c61 7a69 6465 000d 0000      initialized....
    2738:	6944 7073 616c 2079 6f63 746e 6f72 206c     Display control 
    2748:	6e69 7469 6169 696c 657a 0d64 0000 0000     initialized.....
    2758:	4441 2043 6964 7073 616c 2079 6e69 7469     ADC display init
    2768:	6169 696c 657a 0d64 0000 0000 7953 7473     ialized.....Syst
    2778:	6d65 6920 696e 6974 6c61 7a69 7461 6f69     em initializatio
    2788:	206e 6f63 706d 656c 6574 000d 0a0d 3d3d     n complete....==
    2798:	203d 4843 3233 3056 3330 4120 4344 4d20     = CH32V003 ADC M
    27a8:	6e6f 7469 726f 3d20 3d3d 000d 7953 7473     onitor ===..Syst
    27b8:	6d65 6c43 3a6b 2520 2064 7a48 0a0d 0000     emClk: %d Hz....
    27c8:	6843 7069 4449 203a 3025 7838 0a0d 0000     ChipID: %08x....
    27d8:	6f54 6375 3a68 5320 6f68 7472 7020 6572     Touch: Short pre
    27e8:	7373 2d20 7420 7275 696e 676e 6f20 206e     ss - turning on 
    27f8:	6964 7073 616c 0d79 0000 0000 6f54 6375     display.....Touc
    2808:	3a68 4c20 6e6f 2067 7270 7365 2073 202d     h: Long press - 
    2818:	6f74 6767 696c 676e 6420 7369 6c70 7961     toggling display
    2828:	6d20 646f 0d65 0000 6f54 6375 3a68 5420      mode...Touch: T
    2838:	6d69 6f65 7475 2d20 7420 7275 696e 676e     imeout - turning
    2848:	6f20 6666 6420 7369 6c70 7961 000d 0000      off display....

00002858 <font>:
    2858:	0000 0000 3e00 4f5b 3e5b 6b3e 6b4f 1c3e     .....>[O[>>kOk>.
    2868:	7c3e 1c3e 3c18 3c7e 1c18 7d57 1c57 5e1c     >|>..<~<..W}W..^
    2878:	5e7f 001c 3c18 0018 e7ff e7c3 00ff 2418     .^...<.........$
    2888:	0018 e7ff e7db 30ff 3a48 0e06 2926 2979     .......0H:..&)y)
    2898:	4026 057f 0705 7f40 2505 5a3f e73c 5a3c     &@....@..%?Z<.<Z
    28a8:	3e7f 1c1c 0808 1c1c 7f3e 2214 227f 5f14     .>......>.."."._
    28b8:	005f 5f5f 0906 017f 007f 8966 6a95 6060     _.__......f..j``
    28c8:	6060 9460 ffa2 94a2 0408 047e 1008 7e20     ```.......~... ~
    28d8:	1020 0808 1c2a 0808 2a1c 0808 101e 1010      ...*....*......
    28e8:	0c10 0c1e 0c1e 3830 383e 0630 3e0e 060e     ......08>80..>..
    28f8:	0000 0000 0000 5f00 0000 0700 0700 1400     ......._........
    2908:	147f 147f 2a24 2a7f 2312 0813 6264 4936     ....$*.*.#..db6I
    2918:	2056 0050 0708 0003 1c00 4122 0000 2241     V P......."A..A"
    2928:	001c 1c2a 1c7f 082a 3e08 0808 8000 3070     ..*...*..>....p0
    2938:	0800 0808 0808 0000 6060 2000 0810 0204     ........``. ....
    2948:	513e 4549 003e 7f42 0040 4972 4949 2146     >QIE>.B.@.rIIIF!
    2958:	4941 334d 1418 7f12 2710 4545 3945 4a3c     AIM3.....'EEE9<J
    2968:	4949 4131 1121 0709 4936 4949 4636 4949     II1A!...6III6FII
    2978:	1e29 0000 0014 0000 3440 0000 0800 2214     ).......@4....."
    2988:	1441 1414 1414 4100 1422 0208 5901 0609     A......A"....Y..
    2998:	413e 595d 7c4e 1112 7c12 497f 4949 3e36     >A]YN|...|.III6>
    29a8:	4141 2241 417f 4141 7f3e 4949 4149 097f     AAA".AAA>.IIIA..
    29b8:	0909 3e01 4141 7351 087f 0808 007f 7f41     ...>AAQs......A.
    29c8:	0041 4020 3f41 7f01 1408 4122 407f 4040     A. @A?...."A.@@@
    29d8:	7f40 1c02 7f02 047f 1008 3e7f 4141 3e41     @..........>AAA>
    29e8:	097f 0909 3e06 5141 5e21 097f 2919 2646     .....>AQ!^...)F&
    29f8:	4949 3249 0103 017f 3f03 4040 3f40 201f     III2.....?@@@?. 
    2a08:	2040 3f1f 3840 3f40 1463 1408 0363 7804     @ .?@8@?c...c..x
    2a18:	0304 5961 4d49 0043 417f 4141 0402 1008     ..aYIMC..AAA....
    2a28:	0020 4141 7f41 0204 0201 4004 4040 4040      .AAA......@@@@@
    2a38:	0300 0807 2000 5454 4078 287f 4444 3838     ..... TTx@.(DD88
    2a48:	4444 2844 4438 2844 387f 5454 1854 0800     DDD(8DD(.8TTT...
    2a58:	097e 1802 a4a4 789c 087f 0404 0078 7d44     ~......x....x.D}
    2a68:	0040 4020 3d40 7f00 2810 0044 4100 407f     @. @@=...(D..A.@
    2a78:	7c00 7804 7804 087c 0404 3878 4444 3844     .|.x.x|...x8DDD8
    2a88:	18fc 2424 1818 2424 fc18 087c 0404 4808     ..$$..$$..|....H
    2a98:	5454 2454 0404 443f 3c24 4040 7c20 201c     TTT$..?D$<@@ |. 
    2aa8:	2040 3c1c 3040 3c40 2844 2810 4c44 9090     @ .<@0@<D(.(DL..
    2ab8:	7c90 6444 4c54 0044 3608 0041 0000 0077     .|DdTLD..6A...w.
    2ac8:	0000 3641 0008 0102 0402 3c02 2326 3c26     ..A6.......<&#&<
    2ad8:	a11e 61a1 3a12 4040 7a20 5438 5554 2159     ...a.:@@ z8TTUY!
    2ae8:	5555 4179 5422 7854 2142 5455 4078 5420     UUyA"TTxB!UTx@ T
    2af8:	7955 0c40 521e 1272 5539 5555 3959 5454     <EMAIL>.9UUUY9TT
    2b08:	5954 5539 5454 0058 4500 417c 0200 7d45     TY9UTTX..E|A..E}
    2b18:	0042 4501 407c 127d 1211 f07d 2528 f028     B..E|@}...}.(%(.
    2b28:	547c 4555 2000 5454 547c 0a7c 7f09 3249     |TUE. TT|T|...I2
    2b38:	4949 3249 443a 4444 323a 484a 3048 413a     III2:DDD:2JHH0:A
    2b48:	2141 3a7a 4042 7820 9d00 a0a0 3d7d 4242     A!z:B@ x....}=BB
    2b58:	3d42 403d 4040 3c3d ff24 2424 7e48 4349     B==@@@=<$.$$H~IC
    2b68:	2b66 fc2f 2b2f 09ff f629 c020 7e88 0309     f+/./+..). ..~..
    2b78:	5420 7954 0041 4400 417d 4830 4a48 3832      TTyA..D}A0HHJ28
    2b88:	4040 7a22 7a00 0a0a 7d72 190d 7d31 2926     @@"z.z..r}..1}&)
    2b98:	2f29 2628 2929 2629 4830 404d 3820 0808     )/(&)))&0HM@ 8..
    2ba8:	0808 0808 0808 2f38 c810 baac 102f 3428     ......8/..../.(4
    2bb8:	00fa 7b00 0000 1408 142a 2222 2a14 0814     ...{....*."".*..
    2bc8:	0055 0055 aa55 aa55 aa55 55ff 55ff 00ff     U.U.U.U.U..U.U..
    2bd8:	0000 00ff 1010 ff10 1400 1414 00ff 1010     ................
    2be8:	00ff 10ff f010 f010 1414 fc14 1400 f714     ................
    2bf8:	ff00 0000 00ff 14ff f414 fc04 1414 1017     ................
    2c08:	101f 1f10 1f10 1414 1f14 1000 1010 00f0     ................
    2c18:	0000 1f00 1010 1010 101f 1010 f010 0010     ................
    2c28:	0000 10ff 1010 1010 1010 1010 10ff 0000     ................
    2c38:	ff00 0014 ff00 ff00 0000 101f 0017 fc00     ................
    2c48:	f404 1414 1017 1417 f414 f404 0000 00ff     ................
    2c58:	14f7 1414 1414 1414 00f7 14f7 1414 1417     ................
    2c68:	1010 101f 141f 1414 14f4 1010 10f0 00f0     ................
    2c78:	1f00 1f10 0000 1f00 0014 0000 14fc 0000     ................
    2c88:	10f0 10f0 ff10 ff10 1414 ff14 1014 1010     ................
    2c98:	001f 0000 f000 ff10 ffff ffff f0f0 f0f0     ................
    2ca8:	fff0 ffff 0000 0000 ff00 0fff 0f0f 0f0f     ................
    2cb8:	4438 3844 fc44 4a4a 344a 027e 0602 0206     8DD8D.JJJ4~.....
    2cc8:	027e 027e 5563 4149 3863 4444 043c 7e40     ~.~.cUIAc8DD<.@~
    2cd8:	1e20 0620 7e02 0202 a599 a5e7 1c99 492a      . ..~........*I
    2ce8:	1c2a 724c 7201 304c 4d4a 304d 4830 4878     *.Lr.rL0JMM00HxH
    2cf8:	bc30 5a62 3d46 493e 4949 7e00 0101 7e01     0.bZF=>III.~...~
    2d08:	2a2a 2a2a 442a 5f44 4444 5140 444a 4040     *****DD_DD@QJD@@
    2d18:	4a44 4051 0000 01ff e003 ff80 0000 0808     DJQ@............
    2d28:	6b6b 3608 3612 3624 0600 0909 0006 1800     kk.6.6$6........
    2d38:	0018 0000 1010 3000 ff40 0101 1f00 0101     .......0@.......
    2d48:	001e 1d19 1217 3c00 3c3c 003c 0000 0000     .......<<<<.....
    2d58:	6e28 6c75 296c 0000                         (null)..
