
CH32V003F4U6.elf:     file format elf32-littleriscv
CH32V003F4U6.elf
architecture: riscv:rv32, flags 0x00000112:
EXEC_P, HAS_SYMS, D_PAGED
start address 0x00000000

Program Header:
    LOAD off    0x00001000 vaddr 0x00000000 paddr 0x00000000 align 2**12
         filesz 0x000023e0 memsz 0x000023e0 flags r-x
    LOAD off    0x00004000 vaddr 0x20000000 paddr 0x000023e0 align 2**12
         filesz 0x00000040 memsz 0x000001d4 flags rw-
    LOAD off    0x00004700 vaddr 0x20000700 paddr 0x20000700 align 2**12
         filesz 0x00000000 memsz 0x00000100 flags rw-

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .init         000000a0  00000000  00000000  00001000  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  1 .highcodelalign 00000000  000000a0  000000a0  00004040  2**0
                  CONTENTS
  2 .highcode     00000000  20000000  20000000  00004040  2**0
                  CONTENTS
  3 .text         00002340  000000a0  000000a0  000010a0  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  4 .fini         00000000  000023e0  000023e0  00004040  2**0
                  CONTENTS, ALLOC, LOAD, CODE
  5 .dalign       00000000  20000000  20000000  00004040  2**0
                  CONTENTS
  6 .dlalign      00000000  000023e0  000023e0  00004040  2**0
                  CONTENTS
  7 .data         00000040  20000000  000023e0  00004000  2**2
                  CONTENTS, ALLOC, LOAD, DATA
  8 .bss          00000194  20000040  00002420  00004040  2**2
                  ALLOC
  9 .stack        00000100  20000700  20000700  00004700  2**0
                  ALLOC
 10 .debug_info   000121ed  00000000  00000000  00004040  2**0
                  CONTENTS, READONLY, DEBUGGING
 11 .debug_abbrev 00003391  00000000  00000000  0001622d  2**0
                  CONTENTS, READONLY, DEBUGGING
 12 .debug_loc    00004894  00000000  00000000  000195be  2**0
                  CONTENTS, READONLY, DEBUGGING
 13 .debug_aranges 000009c8  00000000  00000000  0001de58  2**3
                  CONTENTS, READONLY, DEBUGGING
 14 .debug_ranges 00000b10  00000000  00000000  0001e820  2**3
                  CONTENTS, READONLY, DEBUGGING
 15 .debug_line   0000bd53  00000000  00000000  0001f330  2**0
                  CONTENTS, READONLY, DEBUGGING
 16 .debug_str    00002f80  00000000  00000000  0002b083  2**0
                  CONTENTS, READONLY, DEBUGGING
 17 .comment      00000033  00000000  00000000  0002e003  2**0
                  CONTENTS, READONLY
 18 .debug_frame  00001678  00000000  00000000  0002e038  2**2
                  CONTENTS, READONLY, DEBUGGING
SYMBOL TABLE:
00000000 l    d  .init	00000000 .init
000000a0 l    d  .highcodelalign	00000000 .highcodelalign
20000000 l    d  .highcode	00000000 .highcode
000000a0 l    d  .text	00000000 .text
000023e0 l    d  .fini	00000000 .fini
20000000 l    d  .dalign	00000000 .dalign
000023e0 l    d  .dlalign	00000000 .dlalign
20000000 l    d  .data	00000000 .data
20000040 l    d  .bss	00000000 .bss
20000700 l    d  .stack	00000000 .stack
00000000 l    d  .debug_info	00000000 .debug_info
00000000 l    d  .debug_abbrev	00000000 .debug_abbrev
00000000 l    d  .debug_loc	00000000 .debug_loc
00000000 l    d  .debug_aranges	00000000 .debug_aranges
00000000 l    d  .debug_ranges	00000000 .debug_ranges
00000000 l    d  .debug_line	00000000 .debug_line
00000000 l    d  .debug_str	00000000 .debug_str
00000000 l    d  .comment	00000000 .comment
00000000 l    d  .debug_frame	00000000 .debug_frame
00000000 l    df *ABS*	00000000 adc_display.c
00001d0c l     O .text	00000008 CSWTCH.2
00000000 l    df *ABS*	00000000 ch32v00x_it.c
00000000 l    df *ABS*	00000000 display_control.c
00000000 l    df *ABS*	00000000 display_text.c
00000000 l    df *ABS*	00000000 main.c
00000000 l    df *ABS*	00000000 pwm_config.c
00000000 l    df *ABS*	00000000 st7735.c
00000718 l     F .text	0000003e SPI_send_DMA
00000756 l     F .text	00000012 SPI_send
00000768 l     F .text	00000016 write_command_8
0000077e l     F .text	00000020 write_data_16
0000079e l     F .text	0000003c tft_set_window
20000062 l     O .bss	00000002 _bg_color
20000064 l     O .bss	00000140 _buffer
200001a4 l     O .bss	00000002 _cursor_x
200001a6 l     O .bss	00000002 _cursor_y
200001a8 l     O .bss	0000000c str.4169
20000000 l     O .data	00000002 _color
00001ed8 l     O .text	00000500 font
00000000 l    df *ABS*	00000000 system_ch32v00x.c
00000000 l    df *ABS*	00000000 touch_button.c
00000000 l    df *ABS*	00000000 ch32v00x_dbgmcu.c
00000000 l    df *ABS*	00000000 ch32v00x_exti.c
00000000 l    df *ABS*	00000000 ch32v00x_gpio.c
00000000 l    df *ABS*	00000000 ch32v00x_misc.c
00000000 l    df *ABS*	00000000 ch32v00x_rcc.c
20000018 l     O .data	00000014 ADCPrescTable
2000002c l     O .data	00000010 APBAHBPrescTable
00000000 l    df *ABS*	00000000 ch32v00x_tim.c
00000000 l    df *ABS*	00000000 ch32v00x_usart.c
00000000 l    df *ABS*	00000000 debug.c
200001d0 l     O .bss	00000002 p_ms
200001d2 l     O .bss	00000001 p_us
00000000 l    df *ABS*	00000000 wchprintf.c
00000000 l    df *ABS*	00000000 memcpy.c
00001832  w    F .text	00000004 printDouble
000001ce g     F .text	00000026 ADC_Display_Init
20000040 g     O .bss	0000000c adc_display_config
00001836  w    F .text	00000360 print
00001b96  w    F .text	00000024 printf
20000840 g       .data	00000000 __global_pointer$
00000306 g     F .text	00000024 Display_Control_Init
00000f56  w      .text	00000000 TIM1_CC_IRQHandler
000001f6 g     F .text	00000010 HardFault_Handler
00001720  w    F .text	0000010e printInt
000013b6 g     F .text	0000000e TIM_OC1PreloadConfig
00000e2a g     F .text	0000002a Touch_Button_Init
00000f56  w      .text	00000000 SysTick_Handler
00001152 g     F .text	00000062 NVIC_Init
00000f56  w      .text	00000000 PVD_IRQHandler
000001f4 g     F .text	00000002 NMI_Handler
00001008 g     F .text	0000000a DBGMCU_GetCHIPID
200001b4 g     O .bss	00000004 system_tick_ms
0000096a g     F .text	0000000e tft_set_cursor
000014d6 g     F .text	0000000a USART_GetFlagStatus
20000040 g       .bss	00000000 _sbss
00000100 g       *ABS*	00000000 __stack_size
0000154e g     F .text	00000058 USART_Printf_Init
0000041e g     F .text	00000018 Display_Text_Clear_Screen
000000aa g     F .text	0000000a .hidden __riscv_restore_2
00001374 g     F .text	00000016 TIM_CtrlPWMOutputs
00001c04 g     F .text	000000d2 memcpy
0000182e  w    F .text	00000004 printLongLongInt
00000172 g     F .text	0000005c ADC_Display_Draw_Channel_Labels
0000135c g     F .text	00000018 TIM_Cmd
00001bba g     F .text	0000004a puts
0000014a g     F .text	00000028 ADC_Display_Draw_Header
20000014 g     O .data	00000004 SystemCoreClock
2000004c g     O .bss	0000000c display_control
00000232 g     F .text	00000016 Display_Control_Turn_On
000000d4 g     F .text	0000002c .hidden __udivsi3
00000ed2 g     F .text	0000003e Touch_Button_Display_Hello
000000a0 g       .init	00000000 _einit
000013e0 g     F .text	0000000c TIM_ClearITPendingBit
00001268 g     F .text	0000001e RCC_APB2PeriphClockCmd
000010a4 g     F .text	0000007c GPIO_Init
200001cc g     O .bss	00000004 NVIC_Priority_Group
00000f56  w      .text	00000000 SPI1_IRQHandler
000014b8 g     F .text	00000016 USART_Cmd
000000a0 g     F .text	0000000a .hidden __riscv_save_1
00000a6c g     F .text	00000020 tft_print
000000aa g     F .text	0000000a .hidden __riscv_restore_0
00000f56  w      .text	00000000 AWU_IRQHandler
00000206 g     F .text	00000008 EXTI7_0_IRQHandler
00000436 g     F .text	0000004c Display_Text_Welcome
00001286 g     F .text	0000001e RCC_APB1PeriphClockCmd
20000700 g       .stack	00000000 _heap_end
0000032a g     F .text	0000002e Display_Control_Show_Off_Message
00000358 g     F .text	0000003e Display_Control_Update
00000f56  w      .text	00000000 DMA1_Channel4_IRQHandler
00000f56  w      .text	00000000 ADC1_IRQHandler
00000e54 g     F .text	00000072 Touch_Button_Update
0000109a g     F .text	0000000a EXTI_ClearITPendingBit
200001d4 g       .bss	00000000 _ebss
00000f56  w      .text	00000000 DMA1_Channel7_IRQHandler
000012f2 g     F .text	0000006a TIM_OC1Init
000014e0 g     F .text	00000034 Delay_Init
00000de8 g     F .text	00000042 Touch_Button_EXTI_Config
0000139c g     F .text	0000001a TIM_ARRPreloadConfig
00000982 g     F .text	00000006 tft_set_background_color
00000100 g     F .text	00000008 .hidden __umodsi3
000005bc g     F .text	00000098 PWM_Timer_Config
00000f56  w      .text	00000000 I2C1_EV_IRQHandler
000013c8 g     F .text	00000018 TIM_GetITStatus
000011c6 g     F .text	000000a2 RCC_GetClocksFreq
00000d62 g     F .text	00000030 Touch_Button_GPIO_Config
00000f56  w      .text	00000000 DMA1_Channel6_IRQHandler
000013ec g     F .text	000000cc USART_Init
00000f56  w      .text	00000000 RCC_IRQHandler
00000f56  w      .text	00000000 TIM1_TRG_COM_IRQHandler
00000f56  w      .text	00000000 DMA1_Channel1_IRQHandler
00000000 g       .init	00000000 _start
20000004 g     O .data	00000010 AHBPrescTable
00000704 g     F .text	0000000e PWM_Turn_Off
0000027a g     F .text	00000018 Display_Control_Clear_Screen
0000163a  w    F .text	000000e6 prints
0000138a g     F .text	00000012 TIM_ITConfig
20000000 g       .highcode	00000000 _highcode_vma_start
00001012 g     F .text	0000006a EXTI_Init
000000b4 g     F .text	00000014 .hidden __mulsi3
00000262 g     F .text	00000018 Display_Control_Toggle
20000000 g       .dalign	00000000 _data_vma
00000680 g     F .text	00000022 PWM_Config_Init
00000ec6 g     F .text	0000000c Touch_Button_Get_Event
000003da g     F .text	00000044 Display_Text_Centered
0000112a g     F .text	00000022 GPIO_EXTILineConfig
00000248 g     F .text	0000001a Display_Control_Turn_Off
0000107c g     F .text	0000001e EXTI_GetITStatus
000011b4 g     F .text	00000012 RCC_AdjustHSICalibrationValue
000004e0 g     F .text	000000a8 main
00000482 g     F .text	0000005e System_Init
00000f56  w      .text	00000000 DMA1_Channel5_IRQHandler
00000396 g     F .text	00000044 Display_Text_Custom
000000cc g     F .text	00000058 .hidden __divsi3
00001514 g     F .text	0000003a Delay_Ms
00000a8c g     F .text	000000aa tft_print_number
000007da g     F .text	00000190 tft_init
000000a0 g       .highcodelalign	00000000 _highcode_lma
00000bc2 g     F .text	00000134 SystemInit
00000712 g     F .text	00000006 PWM_Get_Brightness
000015f2  w    F .text	00000048 printchar
00000000 g       .init	00000000 _sinit
00000b36 g     F .text	0000008c tft_fill_rect
00000f56  w      .text	00000000 DMA1_Channel3_IRQHandler
000006f4 g     F .text	00000010 PWM_Turn_On
00000f56  w      .text	00000000 TIM1_UP_IRQHandler
20000058 g     O .bss	00000001 system_initialized
00000f56  w      .text	00000000 WWDG_IRQHandler
00000588 g     F .text	00000034 PWM_GPIO_Config
0000020e g     F .text	00000024 TIM2_IRQHandler
20000800 g       .stack	00000000 _eusrstack
000000a0 g     F .text	0000000a .hidden __riscv_save_2
00000f56  w      .text	00000000 SW_Handler
2000005c g     O .bss	00000006 pwm_control
00000f56  w      .text	00000000 TIM1_BRK_IRQHandler
000014ce g     F .text	00000008 USART_SendData
000015a6 g     F .text	0000004c _write
20000040 g       .data	00000000 _edata
200001d4 g       .bss	00000000 _end
20000000 g       .highcode	00000000 _highcode_vma_end
000012a4 g     F .text	0000004e TIM_TimeBaseInit
000023e0 g       .dlalign	00000000 _data_lma
00000cf6 g     F .text	0000006c SystemCoreClockUpdate
00000f10 g     F .text	00000046 Touch_Button_IRQ_Handler
00000124 g     F .text	00000024 .hidden __modsi3
00000f56  w      .text	00000000 DMA1_Channel2_IRQHandler
00000f58  w      .text	00000000 handle_reset
00000f56  w      .text	00000000 FLASH_IRQHandler
000000a0 g     F .text	0000000a .hidden __riscv_save_0
00000f56  w      .text	00000000 USART1_IRQHandler
00000d92 g     F .text	00000056 Touch_Button_Timer_Init
00000978 g     F .text	0000000a tft_set_color
00000654 g     F .text	0000002c PWM_Set_Brightness
00000f56  w      .text	00000000 I2C1_ER_IRQHandler
0000114c g     F .text	00000006 NVIC_PriorityGroupConfig
00000988 g     F .text	000000e4 tft_print_char
000006a2 g     F .text	00000052 PWM_Update_Fade
00000292 g     F .text	00000074 Display_Control_Show_Startup_Message
000000aa g     F .text	0000000a .hidden __riscv_restore_1
000013c4 g     F .text	00000004 TIM_SetCompare1
00001120 g     F .text	0000000a GPIO_ReadInputDataBit
200001b8 g     O .bss	00000014 touch_button



Disassembly of section .init:

00000000 <_sinit>:
   0:	7590006f          	j	f58 <handle_reset>
   4:	0000                	unimp
   6:	0000                	unimp
   8:	01f4                	addi	a3,sp,204
   a:	0000                	unimp
   c:	01f6                	slli	gp,gp,0x1d
	...
  2e:	0000                	unimp
  30:	0f56                	slli	t5,t5,0x15
  32:	0000                	unimp
  34:	0000                	unimp
  36:	0000                	unimp
  38:	0f56                	slli	t5,t5,0x15
  3a:	0000                	unimp
  3c:	0000                	unimp
  3e:	0000                	unimp
  40:	0f56                	slli	t5,t5,0x15
  42:	0000                	unimp
  44:	0f56                	slli	t5,t5,0x15
  46:	0000                	unimp
  48:	0f56                	slli	t5,t5,0x15
  4a:	0000                	unimp
  4c:	0f56                	slli	t5,t5,0x15
  4e:	0000                	unimp
  50:	0206                	slli	tp,tp,0x1
  52:	0000                	unimp
  54:	0f56                	slli	t5,t5,0x15
  56:	0000                	unimp
  58:	0f56                	slli	t5,t5,0x15
  5a:	0000                	unimp
  5c:	0f56                	slli	t5,t5,0x15
  5e:	0000                	unimp
  60:	0f56                	slli	t5,t5,0x15
  62:	0000                	unimp
  64:	0f56                	slli	t5,t5,0x15
  66:	0000                	unimp
  68:	0f56                	slli	t5,t5,0x15
  6a:	0000                	unimp
  6c:	0f56                	slli	t5,t5,0x15
  6e:	0000                	unimp
  70:	0f56                	slli	t5,t5,0x15
  72:	0000                	unimp
  74:	0f56                	slli	t5,t5,0x15
  76:	0000                	unimp
  78:	0f56                	slli	t5,t5,0x15
  7a:	0000                	unimp
  7c:	0f56                	slli	t5,t5,0x15
  7e:	0000                	unimp
  80:	0f56                	slli	t5,t5,0x15
  82:	0000                	unimp
  84:	0f56                	slli	t5,t5,0x15
  86:	0000                	unimp
  88:	0f56                	slli	t5,t5,0x15
  8a:	0000                	unimp
  8c:	0f56                	slli	t5,t5,0x15
  8e:	0000                	unimp
  90:	0f56                	slli	t5,t5,0x15
  92:	0000                	unimp
  94:	0f56                	slli	t5,t5,0x15
  96:	0000                	unimp
  98:	020e                	slli	tp,tp,0x3
  9a:	0000                	unimp
  9c:	0000                	unimp
	...

Disassembly of section .text:

000000a0 <__riscv_save_0>:
      a0:	1151                	addi	sp,sp,-12
      a2:	c026                	sw	s1,0(sp)
      a4:	c222                	sw	s0,4(sp)
      a6:	c406                	sw	ra,8(sp)
      a8:	8282                	jr	t0

000000aa <__riscv_restore_0>:
      aa:	4482                	lw	s1,0(sp)
      ac:	4412                	lw	s0,4(sp)
      ae:	40a2                	lw	ra,8(sp)
      b0:	0131                	addi	sp,sp,12
      b2:	8082                	ret

000000b4 <__mulsi3>:
      b4:	862a                	mv	a2,a0
      b6:	4501                	li	a0,0
      b8:	0015f693          	andi	a3,a1,1
      bc:	c291                	beqz	a3,c0 <__mulsi3+0xc>
      be:	9532                	add	a0,a0,a2
      c0:	8185                	srli	a1,a1,0x1
      c2:	0606                	slli	a2,a2,0x1
      c4:	f9f5                	bnez	a1,b8 <__mulsi3+0x4>
      c6:	8082                	ret
      c8:	0000                	unimp
	...

000000cc <__divsi3>:
      cc:	02054e63          	bltz	a0,108 <__umodsi3+0x8>
      d0:	0405c363          	bltz	a1,116 <__umodsi3+0x16>

000000d4 <__udivsi3>:
      d4:	862e                	mv	a2,a1
      d6:	85aa                	mv	a1,a0
      d8:	557d                	li	a0,-1
      da:	c215                	beqz	a2,fe <__udivsi3+0x2a>
      dc:	4685                	li	a3,1
      de:	00b67863          	bgeu	a2,a1,ee <__udivsi3+0x1a>
      e2:	00c05663          	blez	a2,ee <__udivsi3+0x1a>
      e6:	0606                	slli	a2,a2,0x1
      e8:	0686                	slli	a3,a3,0x1
      ea:	feb66ce3          	bltu	a2,a1,e2 <__udivsi3+0xe>
      ee:	4501                	li	a0,0
      f0:	00c5e463          	bltu	a1,a2,f8 <__udivsi3+0x24>
      f4:	8d91                	sub	a1,a1,a2
      f6:	8d55                	or	a0,a0,a3
      f8:	8285                	srli	a3,a3,0x1
      fa:	8205                	srli	a2,a2,0x1
      fc:	faf5                	bnez	a3,f0 <__udivsi3+0x1c>
      fe:	8082                	ret

00000100 <__umodsi3>:
     100:	8286                	mv	t0,ra
     102:	3fc9                	jal	d4 <__udivsi3>
     104:	852e                	mv	a0,a1
     106:	8282                	jr	t0
     108:	40a00533          	neg	a0,a0
     10c:	0005d763          	bgez	a1,11a <__umodsi3+0x1a>
     110:	40b005b3          	neg	a1,a1
     114:	b7c1                	j	d4 <__udivsi3>
     116:	40b005b3          	neg	a1,a1
     11a:	8286                	mv	t0,ra
     11c:	3f65                	jal	d4 <__udivsi3>
     11e:	40a00533          	neg	a0,a0
     122:	8282                	jr	t0

00000124 <__modsi3>:
     124:	8286                	mv	t0,ra
     126:	0005c763          	bltz	a1,134 <__modsi3+0x10>
     12a:	00054963          	bltz	a0,13c <__modsi3+0x18>
     12e:	375d                	jal	d4 <__udivsi3>
     130:	852e                	mv	a0,a1
     132:	8282                	jr	t0
     134:	40b005b3          	neg	a1,a1
     138:	fe055be3          	bgez	a0,12e <__modsi3+0xa>
     13c:	40a00533          	neg	a0,a0
     140:	3f51                	jal	d4 <__udivsi3>
     142:	40b00533          	neg	a0,a1
     146:	8282                	jr	t0
	...

0000014a <ADC_Display_Draw_Header>:
     14a:	f57ff2ef          	jal	t0,a0 <__riscv_save_0>
     14e:	6541                	lui	a0,0x10
     150:	157d                	addi	a0,a0,-1
     152:	027000ef          	jal	ra,978 <tft_set_color>
     156:	4501                	li	a0,0
     158:	02b000ef          	jal	ra,982 <tft_set_background_color>
     15c:	4581                	li	a1,0
     15e:	4515                	li	a0,5
     160:	00b000ef          	jal	ra,96a <tft_set_cursor>
     164:	00002537          	lui	a0,0x2
     168:	d0050513          	addi	a0,a0,-768 # 1d00 <memcpy+0xfc>
     16c:	101000ef          	jal	ra,a6c <tft_print>
     170:	bf2d                	j	aa <__riscv_restore_0>

00000172 <ADC_Display_Draw_Channel_Labels>:
     172:	f2fff2ef          	jal	t0,a0 <__riscv_save_0>
     176:	1171                	addi	sp,sp,-4
     178:	4501                	li	a0,0
     17a:	009000ef          	jal	ra,982 <tft_set_background_color>
     17e:	6789                	lui	a5,0x2
     180:	d0c78793          	addi	a5,a5,-756 # 1d0c <CSWTCH.2>
     184:	4451                	li	s0,20
     186:	4485                	li	s1,1
     188:	238a                	lhu	a0,0(a5)
     18a:	c03e                	sw	a5,0(sp)
     18c:	7ec000ef          	jal	ra,978 <tft_set_color>
     190:	85a2                	mv	a1,s0
     192:	4515                	li	a0,5
     194:	7d6000ef          	jal	ra,96a <tft_set_cursor>
     198:	000027b7          	lui	a5,0x2
     19c:	cf878513          	addi	a0,a5,-776 # 1cf8 <memcpy+0xf4>
     1a0:	0cd000ef          	jal	ra,a6c <tft_print>
     1a4:	8526                	mv	a0,s1
     1a6:	4585                	li	a1,1
     1a8:	0e5000ef          	jal	ra,a8c <tft_print_number>
     1ac:	00002537          	lui	a0,0x2
     1b0:	cfc50513          	addi	a0,a0,-772 # 1cfc <memcpy+0xf8>
     1b4:	0b9000ef          	jal	ra,a6c <tft_print>
     1b8:	4782                	lw	a5,0(sp)
     1ba:	0449                	addi	s0,s0,18
     1bc:	0442                	slli	s0,s0,0x10
     1be:	0485                	addi	s1,s1,1
     1c0:	4695                	li	a3,5
     1c2:	0789                	addi	a5,a5,2
     1c4:	8041                	srli	s0,s0,0x10
     1c6:	fcd491e3          	bne	s1,a3,188 <ADC_Display_Draw_Channel_Labels+0x16>
     1ca:	0111                	addi	sp,sp,4
     1cc:	bdf9                	j	aa <__riscv_restore_0>

000001ce <ADC_Display_Init>:
     1ce:	ed3ff2ef          	jal	t0,a0 <__riscv_save_0>
     1d2:	200007b7          	lui	a5,0x20000
     1d6:	02010737          	lui	a4,0x2010
     1da:	04078793          	addi	a5,a5,64 # 20000040 <_edata>
     1de:	10070713          	addi	a4,a4,256 # 2010100 <_data_lma+0x200dd20>
     1e2:	c398                	sw	a4,0(a5)
     1e4:	06400713          	li	a4,100
     1e8:	a3da                	sh	a4,4(a5)
     1ea:	0007a423          	sw	zero,8(a5)
     1ee:	3fb1                	jal	14a <ADC_Display_Draw_Header>
     1f0:	3749                	jal	172 <ADC_Display_Draw_Channel_Labels>
     1f2:	bd65                	j	aa <__riscv_restore_0>

000001f4 <NMI_Handler>:
     1f4:	a001                	j	1f4 <NMI_Handler>

000001f6 <HardFault_Handler>:
     1f6:	beef07b7          	lui	a5,0xbeef0
     1fa:	e000e737          	lui	a4,0xe000e
     1fe:	08078793          	addi	a5,a5,128 # beef0080 <__global_pointer$+0x9eeef840>
     202:	c73c                	sw	a5,72(a4)
     204:	a001                	j	204 <HardFault_Handler+0xe>

00000206 <EXTI7_0_IRQHandler>:
     206:	50b000ef          	jal	ra,f10 <Touch_Button_IRQ_Handler>
     20a:	30200073          	mret

0000020e <TIM2_IRQHandler>:
     20e:	4585                	li	a1,1
     210:	40000537          	lui	a0,0x40000
     214:	1b4010ef          	jal	ra,13c8 <TIM_GetITStatus>
     218:	c919                	beqz	a0,22e <TIM2_IRQHandler+0x20>
     21a:	9741a783          	lw	a5,-1676(gp) # 200001b4 <system_tick_ms>
     21e:	4585                	li	a1,1
     220:	40000537          	lui	a0,0x40000
     224:	0785                	addi	a5,a5,1
     226:	96f1aa23          	sw	a5,-1676(gp) # 200001b4 <system_tick_ms>
     22a:	1b6010ef          	jal	ra,13e0 <TIM_ClearITPendingBit>
     22e:	30200073          	mret

00000232 <Display_Control_Turn_On>:
     232:	80c18793          	addi	a5,gp,-2036 # 2000004c <display_control>
     236:	4398                	lw	a4,0(a5)
     238:	e719                	bnez	a4,246 <Display_Control_Turn_On+0x14>
     23a:	e67ff2ef          	jal	t0,a0 <__riscv_save_0>
     23e:	4705                	li	a4,1
     240:	c398                	sw	a4,0(a5)
     242:	294d                	jal	6f4 <PWM_Turn_On>
     244:	b59d                	j	aa <__riscv_restore_0>
     246:	8082                	ret

00000248 <Display_Control_Turn_Off>:
     248:	80c18793          	addi	a5,gp,-2036 # 2000004c <display_control>
     24c:	4394                	lw	a3,0(a5)
     24e:	4709                	li	a4,2
     250:	00e69863          	bne	a3,a4,260 <Display_Control_Turn_Off+0x18>
     254:	e4dff2ef          	jal	t0,a0 <__riscv_save_0>
     258:	470d                	li	a4,3
     25a:	c398                	sw	a4,0(a5)
     25c:	2165                	jal	704 <PWM_Turn_Off>
     25e:	b5b1                	j	aa <__riscv_restore_0>
     260:	8082                	ret

00000262 <Display_Control_Toggle>:
     262:	e3fff2ef          	jal	t0,a0 <__riscv_save_0>
     266:	80c1a783          	lw	a5,-2036(gp) # 2000004c <display_control>
     26a:	e399                	bnez	a5,270 <Display_Control_Toggle+0xe>
     26c:	37d9                	jal	232 <Display_Control_Turn_On>
     26e:	bd35                	j	aa <__riscv_restore_0>
     270:	4709                	li	a4,2
     272:	fee79ee3          	bne	a5,a4,26e <Display_Control_Toggle+0xc>
     276:	3fc9                	jal	248 <Display_Control_Turn_Off>
     278:	bfdd                	j	26e <Display_Control_Toggle+0xc>

0000027a <Display_Control_Clear_Screen>:
     27a:	e27ff2ef          	jal	t0,a0 <__riscv_save_0>
     27e:	4701                	li	a4,0
     280:	05000693          	li	a3,80
     284:	0a000613          	li	a2,160
     288:	4581                	li	a1,0
     28a:	4501                	li	a0,0
     28c:	0ab000ef          	jal	ra,b36 <tft_fill_rect>
     290:	bd29                	j	aa <__riscv_restore_0>

00000292 <Display_Control_Show_Startup_Message>:
     292:	e0fff2ef          	jal	t0,a0 <__riscv_save_0>
     296:	37d5                	jal	27a <Display_Control_Clear_Screen>
     298:	6441                	lui	s0,0x10
     29a:	fff40513          	addi	a0,s0,-1 # ffff <_data_lma+0xdc1f>
     29e:	6da000ef          	jal	ra,978 <tft_set_color>
     2a2:	4501                	li	a0,0
     2a4:	6de000ef          	jal	ra,982 <tft_set_background_color>
     2a8:	45a9                	li	a1,10
     2aa:	4529                	li	a0,10
     2ac:	6be000ef          	jal	ra,96a <tft_set_cursor>
     2b0:	00002537          	lui	a0,0x2
     2b4:	d2050513          	addi	a0,a0,-736 # 1d20 <CSWTCH.2+0x14>
     2b8:	7b4000ef          	jal	ra,a6c <tft_print>
     2bc:	45e5                	li	a1,25
     2be:	4529                	li	a0,10
     2c0:	6aa000ef          	jal	ra,96a <tft_set_cursor>
     2c4:	00002537          	lui	a0,0x2
     2c8:	d3050513          	addi	a0,a0,-720 # 1d30 <CSWTCH.2+0x24>
     2cc:	7a0000ef          	jal	ra,a6c <tft_print>
     2d0:	02d00593          	li	a1,45
     2d4:	4515                	li	a0,5
     2d6:	694000ef          	jal	ra,96a <tft_set_cursor>
     2da:	fe040513          	addi	a0,s0,-32
     2de:	69a000ef          	jal	ra,978 <tft_set_color>
     2e2:	00002537          	lui	a0,0x2
     2e6:	d4050513          	addi	a0,a0,-704 # 1d40 <CSWTCH.2+0x34>
     2ea:	782000ef          	jal	ra,a6c <tft_print>
     2ee:	03c00593          	li	a1,60
     2f2:	4515                	li	a0,5
     2f4:	676000ef          	jal	ra,96a <tft_set_cursor>
     2f8:	00002537          	lui	a0,0x2
     2fc:	d5050513          	addi	a0,a0,-688 # 1d50 <CSWTCH.2+0x44>
     300:	76c000ef          	jal	ra,a6c <tft_print>
     304:	b35d                	j	aa <__riscv_restore_0>

00000306 <Display_Control_Init>:
     306:	d9bff2ef          	jal	t0,a0 <__riscv_save_0>
     30a:	80c18413          	addi	s0,gp,-2036 # 2000004c <display_control>
     30e:	10000793          	li	a5,256
     312:	a05e                	sh	a5,4(s0)
     314:	00042023          	sw	zero,0(s0)
     318:	00042423          	sw	zero,8(s0)
     31c:	297d                	jal	7da <tft_init>
     31e:	3fb1                	jal	27a <Display_Control_Clear_Screen>
     320:	3f8d                	jal	292 <Display_Control_Show_Startup_Message>
     322:	4785                	li	a5,1
     324:	a05c                	sb	a5,4(s0)
     326:	2ef9                	jal	704 <PWM_Turn_Off>
     328:	b349                	j	aa <__riscv_restore_0>

0000032a <Display_Control_Show_Off_Message>:
     32a:	d77ff2ef          	jal	t0,a0 <__riscv_save_0>
     32e:	37b1                	jal	27a <Display_Control_Clear_Screen>
     330:	6521                	lui	a0,0x8
     332:	bef50513          	addi	a0,a0,-1041 # 7bef <_data_lma+0x580f>
     336:	642000ef          	jal	ra,978 <tft_set_color>
     33a:	4501                	li	a0,0
     33c:	646000ef          	jal	ra,982 <tft_set_background_color>
     340:	02300593          	li	a1,35
     344:	4551                	li	a0,20
     346:	624000ef          	jal	ra,96a <tft_set_cursor>
     34a:	00002537          	lui	a0,0x2
     34e:	d1450513          	addi	a0,a0,-748 # 1d14 <CSWTCH.2+0x8>
     352:	71a000ef          	jal	ra,a6c <tft_print>
     356:	bb91                	j	aa <__riscv_restore_0>

00000358 <Display_Control_Update>:
     358:	d49ff2ef          	jal	t0,a0 <__riscv_save_0>
     35c:	80c18413          	addi	s0,gp,-2036 # 2000004c <display_control>
     360:	205c                	lbu	a5,4(s0)
     362:	cb89                	beqz	a5,374 <Display_Control_Update+0x1c>
     364:	2e3d                	jal	6a2 <PWM_Update_Fade>
     366:	401c                	lw	a5,0(s0)
     368:	4705                	li	a4,1
     36a:	00e78663          	beq	a5,a4,376 <Display_Control_Update+0x1e>
     36e:	470d                	li	a4,3
     370:	00e78a63          	beq	a5,a4,384 <Display_Control_Update+0x2c>
     374:	bb1d                	j	aa <__riscv_restore_0>
     376:	8201c703          	lbu	a4,-2016(gp) # 20000060 <pwm_control+0x4>
     37a:	ff6d                	bnez	a4,374 <Display_Control_Update+0x1c>
     37c:	4709                	li	a4,2
     37e:	c018                	sw	a4,0(s0)
     380:	b05c                	sb	a5,5(s0)
     382:	bfcd                	j	374 <Display_Control_Update+0x1c>
     384:	8201c783          	lbu	a5,-2016(gp) # 20000060 <pwm_control+0x4>
     388:	f7f5                	bnez	a5,374 <Display_Control_Update+0x1c>
     38a:	2661                	jal	712 <PWM_Get_Brightness>
     38c:	f565                	bnez	a0,374 <Display_Control_Update+0x1c>
     38e:	00042023          	sw	zero,0(s0)
     392:	3f61                	jal	32a <Display_Control_Show_Off_Message>
     394:	b7c5                	j	374 <Display_Control_Update+0x1c>

00000396 <Display_Text_Custom>:
     396:	d0bff2ef          	jal	t0,a0 <__riscv_save_0>
     39a:	1151                	addi	sp,sp,-12
     39c:	842a                	mv	s0,a0
     39e:	8536                	mv	a0,a3
     3a0:	c43e                	sw	a5,8(sp)
     3a2:	c02e                	sw	a1,0(sp)
     3a4:	84b2                	mv	s1,a2
     3a6:	c23a                	sw	a4,4(sp)
     3a8:	5d0000ef          	jal	ra,978 <tft_set_color>
     3ac:	4712                	lw	a4,4(sp)
     3ae:	853a                	mv	a0,a4
     3b0:	5d2000ef          	jal	ra,982 <tft_set_background_color>
     3b4:	47a2                	lw	a5,8(sp)
     3b6:	4712                	lw	a4,4(sp)
     3b8:	cb89                	beqz	a5,3ca <Display_Text_Custom+0x34>
     3ba:	05000693          	li	a3,80
     3be:	0a000613          	li	a2,160
     3c2:	4581                	li	a1,0
     3c4:	4501                	li	a0,0
     3c6:	770000ef          	jal	ra,b36 <tft_fill_rect>
     3ca:	4502                	lw	a0,0(sp)
     3cc:	85a6                	mv	a1,s1
     3ce:	2b71                	jal	96a <tft_set_cursor>
     3d0:	8522                	mv	a0,s0
     3d2:	69a000ef          	jal	ra,a6c <tft_print>
     3d6:	0131                	addi	sp,sp,12
     3d8:	b9c9                	j	aa <__riscv_restore_0>

000003da <Display_Text_Centered>:
     3da:	cc7ff2ef          	jal	t0,a0 <__riscv_save_0>
     3de:	87ba                	mv	a5,a4
     3e0:	4301                	li	t1,0
     3e2:	00130293          	addi	t0,t1,1
     3e6:	00550733          	add	a4,a0,t0
     3ea:	fff70703          	lb	a4,-1(a4) # e000dfff <__global_pointer$+0xc000d7bf>
     3ee:	e715                	bnez	a4,41a <Display_Text_Centered+0x40>
     3f0:	0342                	slli	t1,t1,0x10
     3f2:	01035313          	srli	t1,t1,0x10
     3f6:	00231713          	slli	a4,t1,0x2
     3fa:	40e30333          	sub	t1,t1,a4
     3fe:	05030313          	addi	t1,t1,80
     402:	0342                	slli	t1,t1,0x10
     404:	01035313          	srli	t1,t1,0x10
     408:	e199                	bnez	a1,40e <Display_Text_Centered+0x34>
     40a:	02400593          	li	a1,36
     40e:	8736                	mv	a4,a3
     410:	86b2                	mv	a3,a2
     412:	862e                	mv	a2,a1
     414:	859a                	mv	a1,t1
     416:	3741                	jal	396 <Display_Text_Custom>
     418:	b949                	j	aa <__riscv_restore_0>
     41a:	8316                	mv	t1,t0
     41c:	b7d9                	j	3e2 <Display_Text_Centered+0x8>

0000041e <Display_Text_Clear_Screen>:
     41e:	c83ff2ef          	jal	t0,a0 <__riscv_save_0>
     422:	872a                	mv	a4,a0
     424:	05000693          	li	a3,80
     428:	0a000613          	li	a2,160
     42c:	4581                	li	a1,0
     42e:	4501                	li	a0,0
     430:	706000ef          	jal	ra,b36 <tft_fill_rect>
     434:	b99d                	j	aa <__riscv_restore_0>

00000436 <Display_Text_Welcome>:
     436:	c6bff2ef          	jal	t0,a0 <__riscv_save_0>
     43a:	4501                	li	a0,0
     43c:	37cd                	jal	41e <Display_Text_Clear_Screen>
     43e:	00002537          	lui	a0,0x2
     442:	4701                	li	a4,0
     444:	4681                	li	a3,0
     446:	7ff00613          	li	a2,2047
     44a:	45d1                	li	a1,20
     44c:	d6850513          	addi	a0,a0,-664 # 1d68 <CSWTCH.2+0x5c>
     450:	3769                	jal	3da <Display_Text_Centered>
     452:	6441                	lui	s0,0x10
     454:	00002537          	lui	a0,0x2
     458:	fff40613          	addi	a2,s0,-1 # ffff <_data_lma+0xdc1f>
     45c:	4701                	li	a4,0
     45e:	4681                	li	a3,0
     460:	02300593          	li	a1,35
     464:	d0050513          	addi	a0,a0,-768 # 1d00 <memcpy+0xfc>
     468:	3f8d                	jal	3da <Display_Text_Centered>
     46a:	00002537          	lui	a0,0x2
     46e:	4701                	li	a4,0
     470:	4681                	li	a3,0
     472:	fe040613          	addi	a2,s0,-32
     476:	03200593          	li	a1,50
     47a:	d6050513          	addi	a0,a0,-672 # 1d60 <CSWTCH.2+0x54>
     47e:	3fb1                	jal	3da <Display_Text_Centered>
     480:	b12d                	j	aa <__riscv_restore_0>

00000482 <System_Init>:
     482:	c1fff2ef          	jal	t0,a0 <__riscv_save_0>
     486:	00002537          	lui	a0,0x2
     48a:	d7450513          	addi	a0,a0,-652 # 1d74 <CSWTCH.2+0x68>
     48e:	72c010ef          	jal	ra,1bba <puts>
     492:	22fd                	jal	680 <PWM_Config_Init>
     494:	00002537          	lui	a0,0x2
     498:	d8850513          	addi	a0,a0,-632 # 1d88 <CSWTCH.2+0x7c>
     49c:	71e010ef          	jal	ra,1bba <puts>
     4a0:	18b000ef          	jal	ra,e2a <Touch_Button_Init>
     4a4:	00002537          	lui	a0,0x2
     4a8:	d9c50513          	addi	a0,a0,-612 # 1d9c <CSWTCH.2+0x90>
     4ac:	70e010ef          	jal	ra,1bba <puts>
     4b0:	3d99                	jal	306 <Display_Control_Init>
     4b2:	00002537          	lui	a0,0x2
     4b6:	db850513          	addi	a0,a0,-584 # 1db8 <CSWTCH.2+0xac>
     4ba:	700010ef          	jal	ra,1bba <puts>
     4be:	3b01                	jal	1ce <ADC_Display_Init>
     4c0:	00002537          	lui	a0,0x2
     4c4:	dd850513          	addi	a0,a0,-552 # 1dd8 <CSWTCH.2+0xcc>
     4c8:	6f2010ef          	jal	ra,1bba <puts>
     4cc:	00002537          	lui	a0,0x2
     4d0:	4705                	li	a4,1
     4d2:	df450513          	addi	a0,a0,-524 # 1df4 <CSWTCH.2+0xe8>
     4d6:	80e18c23          	sb	a4,-2024(gp) # 20000058 <system_initialized>
     4da:	6e0010ef          	jal	ra,1bba <puts>
     4de:	b6f1                	j	aa <__riscv_restore_0>

000004e0 <main>:
     4e0:	bc1ff2ef          	jal	t0,a0 <__riscv_save_0>
     4e4:	4505                	li	a0,1
     4e6:	467000ef          	jal	ra,114c <NVIC_PriorityGroupConfig>
     4ea:	00d000ef          	jal	ra,cf6 <SystemCoreClockUpdate>
     4ee:	7f3000ef          	jal	ra,14e0 <Delay_Init>
     4f2:	6571                	lui	a0,0x1c
     4f4:	20050513          	addi	a0,a0,512 # 1c200 <_data_lma+0x19e20>
     4f8:	056010ef          	jal	ra,154e <USART_Printf_Init>
     4fc:	00002537          	lui	a0,0x2
     500:	e1450513          	addi	a0,a0,-492 # 1e14 <CSWTCH.2+0x108>
     504:	6b6010ef          	jal	ra,1bba <puts>
     508:	200007b7          	lui	a5,0x20000
     50c:	0147a583          	lw	a1,20(a5) # 20000014 <SystemCoreClock>
     510:	00002537          	lui	a0,0x2
     514:	e3450513          	addi	a0,a0,-460 # 1e34 <CSWTCH.2+0x128>
     518:	67e010ef          	jal	ra,1b96 <printf>
     51c:	2ed000ef          	jal	ra,1008 <DBGMCU_GetCHIPID>
     520:	85aa                	mv	a1,a0
     522:	00002537          	lui	a0,0x2
     526:	e4850513          	addi	a0,a0,-440 # 1e48 <CSWTCH.2+0x13c>
     52a:	66c010ef          	jal	ra,1b96 <printf>
     52e:	3f91                	jal	482 <System_Init>
     530:	00002437          	lui	s0,0x2
     534:	3709                	jal	436 <Display_Text_Welcome>
     536:	11f000ef          	jal	ra,e54 <Touch_Button_Update>
     53a:	18d000ef          	jal	ra,ec6 <Touch_Button_Get_Event>
     53e:	4789                	li	a5,2
     540:	02f50663          	beq	a0,a5,56c <main+0x8c>
     544:	478d                	li	a5,3
     546:	02f50963          	beq	a0,a5,578 <main+0x98>
     54a:	4785                	li	a5,1
     54c:	00f51b63          	bne	a0,a5,562 <main+0x82>
     550:	00002537          	lui	a0,0x2
     554:	e5850513          	addi	a0,a0,-424 # 1e58 <CSWTCH.2+0x14c>
     558:	662010ef          	jal	ra,1bba <puts>
     55c:	39d9                	jal	232 <Display_Control_Turn_On>
     55e:	175000ef          	jal	ra,ed2 <Touch_Button_Display_Hello>
     562:	3bdd                	jal	358 <Display_Control_Update>
     564:	4505                	li	a0,1
     566:	7af000ef          	jal	ra,1514 <Delay_Ms>
     56a:	b7f1                	j	536 <main+0x56>
     56c:	e8440513          	addi	a0,s0,-380 # 1e84 <CSWTCH.2+0x178>
     570:	64a010ef          	jal	ra,1bba <puts>
     574:	31fd                	jal	262 <Display_Control_Toggle>
     576:	b7f5                	j	562 <main+0x82>
     578:	00002537          	lui	a0,0x2
     57c:	eb050513          	addi	a0,a0,-336 # 1eb0 <CSWTCH.2+0x1a4>
     580:	63a010ef          	jal	ra,1bba <puts>
     584:	31d1                	jal	248 <Display_Control_Turn_Off>
     586:	bff1                	j	562 <main+0x82>

00000588 <PWM_GPIO_Config>:
     588:	b19ff2ef          	jal	t0,a0 <__riscv_save_0>
     58c:	1151                	addi	sp,sp,-12
     58e:	4585                	li	a1,1
     590:	02000513          	li	a0,32
     594:	c002                	sw	zero,0(sp)
     596:	c202                	sw	zero,4(sp)
     598:	c402                	sw	zero,8(sp)
     59a:	4cf000ef          	jal	ra,1268 <RCC_APB2PeriphClockCmd>
     59e:	4791                	li	a5,4
     5a0:	807c                	sh	a5,0(sp)
     5a2:	40011537          	lui	a0,0x40011
     5a6:	47e1                	li	a5,24
     5a8:	c43e                	sw	a5,8(sp)
     5aa:	858a                	mv	a1,sp
     5ac:	478d                	li	a5,3
     5ae:	40050513          	addi	a0,a0,1024 # 40011400 <__global_pointer$+0x20010bc0>
     5b2:	c23e                	sw	a5,4(sp)
     5b4:	2f1000ef          	jal	ra,10a4 <GPIO_Init>
     5b8:	0131                	addi	sp,sp,12
     5ba:	bcc5                	j	aa <__riscv_restore_0>

000005bc <PWM_Timer_Config>:
     5bc:	ae5ff2ef          	jal	t0,a0 <__riscv_save_0>
     5c0:	6505                	lui	a0,0x1
     5c2:	1111                	addi	sp,sp,-28
     5c4:	4585                	li	a1,1
     5c6:	80050513          	addi	a0,a0,-2048 # 800 <tft_init+0x26>
     5ca:	c002                	sw	zero,0(sp)
     5cc:	c202                	sw	zero,4(sp)
     5ce:	00011423          	sh	zero,8(sp)
     5d2:	c602                	sw	zero,12(sp)
     5d4:	c802                	sw	zero,16(sp)
     5d6:	ca02                	sw	zero,20(sp)
     5d8:	cc02                	sw	zero,24(sp)
     5da:	48f000ef          	jal	ra,1268 <RCC_APB2PeriphClockCmd>
     5de:	200007b7          	lui	a5,0x20000
     5e2:	0147a503          	lw	a0,20(a5) # 20000014 <SystemCoreClock>
     5e6:	000f45b7          	lui	a1,0xf4
     5ea:	24058593          	addi	a1,a1,576 # f4240 <_data_lma+0xf1e60>
     5ee:	34dd                	jal	d4 <__udivsi3>
     5f0:	40013437          	lui	s0,0x40013
     5f4:	157d                	addi	a0,a0,-1
     5f6:	8068                	sh	a0,0(sp)
     5f8:	3e700793          	li	a5,999
     5fc:	858a                	mv	a1,sp
     5fe:	c0040513          	addi	a0,s0,-1024 # 40012c00 <__global_pointer$+0x200123c0>
     602:	c23e                	sw	a5,4(sp)
     604:	00011123          	sh	zero,2(sp)
     608:	49d000ef          	jal	ra,12a4 <TIM_TimeBaseInit>
     60c:	67c1                	lui	a5,0x10
     60e:	06078793          	addi	a5,a5,96 # 10060 <_data_lma+0xdc80>
     612:	006c                	addi	a1,sp,12
     614:	c0040513          	addi	a0,s0,-1024
     618:	c63e                	sw	a5,12(sp)
     61a:	00011923          	sh	zero,18(sp)
     61e:	00011a23          	sh	zero,20(sp)
     622:	4d1000ef          	jal	ra,12f2 <TIM_OC1Init>
     626:	c0040513          	addi	a0,s0,-1024
     62a:	45a1                	li	a1,8
     62c:	58b000ef          	jal	ra,13b6 <TIM_OC1PreloadConfig>
     630:	c0040513          	addi	a0,s0,-1024
     634:	4585                	li	a1,1
     636:	567000ef          	jal	ra,139c <TIM_ARRPreloadConfig>
     63a:	c0040513          	addi	a0,s0,-1024
     63e:	4585                	li	a1,1
     640:	51d000ef          	jal	ra,135c <TIM_Cmd>
     644:	4585                	li	a1,1
     646:	c0040513          	addi	a0,s0,-1024
     64a:	52b000ef          	jal	ra,1374 <TIM_CtrlPWMOutputs>
     64e:	0171                	addi	sp,sp,28
     650:	a5bff06f          	j	aa <__riscv_restore_0>

00000654 <PWM_Set_Brightness>:
     654:	a4dff2ef          	jal	t0,a0 <__riscv_save_0>
     658:	3e800793          	li	a5,1000
     65c:	3e800413          	li	s0,1000
     660:	00a7e363          	bltu	a5,a0,666 <PWM_Set_Brightness+0x12>
     664:	842a                	mv	s0,a0
     666:	01041593          	slli	a1,s0,0x10
     66a:	40013537          	lui	a0,0x40013
     66e:	81c1                	srli	a1,a1,0x10
     670:	c0050513          	addi	a0,a0,-1024 # 40012c00 <__global_pointer$+0x200123c0>
     674:	551000ef          	jal	ra,13c4 <TIM_SetCompare1>
     678:	80819e23          	sh	s0,-2020(gp) # 2000005c <pwm_control>
     67c:	a2fff06f          	j	aa <__riscv_restore_0>

00000680 <PWM_Config_Init>:
     680:	a21ff2ef          	jal	t0,a0 <__riscv_save_0>
     684:	81c18793          	addi	a5,gp,-2020 # 2000005c <pwm_control>
     688:	01f40737          	lui	a4,0x1f40
     68c:	c398                	sw	a4,0(a5)
     68e:	6705                	lui	a4,0x1
     690:	a0070713          	addi	a4,a4,-1536 # a00 <tft_print_char+0x78>
     694:	a3da                	sh	a4,4(a5)
     696:	3dcd                	jal	588 <PWM_GPIO_Config>
     698:	3715                	jal	5bc <PWM_Timer_Config>
     69a:	4501                	li	a0,0
     69c:	3f65                	jal	654 <PWM_Set_Brightness>
     69e:	a0dff06f          	j	aa <__riscv_restore_0>

000006a2 <PWM_Update_Fade>:
     6a2:	81c18793          	addi	a5,gp,-2020 # 2000005c <pwm_control>
     6a6:	23d8                	lbu	a4,4(a5)
     6a8:	c729                	beqz	a4,6f2 <PWM_Update_Fade+0x50>
     6aa:	9f7ff2ef          	jal	t0,a0 <__riscv_save_0>
     6ae:	238a                	lhu	a0,0(a5)
     6b0:	23ba                	lhu	a4,2(a5)
     6b2:	00e57e63          	bgeu	a0,a4,6ce <PWM_Update_Fade+0x2c>
     6b6:	33d4                	lbu	a3,5(a5)
     6b8:	9536                	add	a0,a0,a3
     6ba:	0542                	slli	a0,a0,0x10
     6bc:	8141                	srli	a0,a0,0x10
     6be:	00e56563          	bltu	a0,a4,6c8 <PWM_Update_Fade+0x26>
     6c2:	00078223          	sb	zero,4(a5)
     6c6:	853a                	mv	a0,a4
     6c8:	3771                	jal	654 <PWM_Set_Brightness>
     6ca:	9e1ff06f          	j	aa <__riscv_restore_0>
     6ce:	00a77f63          	bgeu	a4,a0,6ec <PWM_Update_Fade+0x4a>
     6d2:	81c18693          	addi	a3,gp,-2020 # 2000005c <pwm_control>
     6d6:	32dc                	lbu	a5,5(a3)
     6d8:	00f56763          	bltu	a0,a5,6e6 <PWM_Update_Fade+0x44>
     6dc:	8d1d                	sub	a0,a0,a5
     6de:	0542                	slli	a0,a0,0x10
     6e0:	8141                	srli	a0,a0,0x10
     6e2:	fea763e3          	bltu	a4,a0,6c8 <PWM_Update_Fade+0x26>
     6e6:	00068223          	sb	zero,4(a3)
     6ea:	bff1                	j	6c6 <PWM_Update_Fade+0x24>
     6ec:	00078223          	sb	zero,4(a5)
     6f0:	bfe9                	j	6ca <PWM_Update_Fade+0x28>
     6f2:	8082                	ret

000006f4 <PWM_Turn_On>:
     6f4:	81c18793          	addi	a5,gp,-2020 # 2000005c <pwm_control>
     6f8:	1f400713          	li	a4,500
     6fc:	a3ba                	sh	a4,2(a5)
     6fe:	4705                	li	a4,1
     700:	a3d8                	sb	a4,4(a5)
     702:	8082                	ret

00000704 <PWM_Turn_Off>:
     704:	81c18793          	addi	a5,gp,-2020 # 2000005c <pwm_control>
     708:	4705                	li	a4,1
     70a:	00079123          	sh	zero,2(a5)
     70e:	a3d8                	sb	a4,4(a5)
     710:	8082                	ret

00000712 <PWM_Get_Brightness>:
     712:	81c1d503          	lhu	a0,-2020(gp) # 2000005c <pwm_control>
     716:	8082                	ret

00000718 <SPI_send_DMA>:
     718:	400207b7          	lui	a5,0x40020
     71c:	dfc8                	sw	a0,60(a5)
     71e:	dbcc                	sw	a1,52(a5)
     720:	5b98                	lw	a4,48(a5)
     722:	400206b7          	lui	a3,0x40020
     726:	20000593          	li	a1,512
     72a:	00176713          	ori	a4,a4,1
     72e:	db98                	sw	a4,48(a5)
     730:	67c1                	lui	a5,0x10
     732:	17fd                	addi	a5,a5,-1
     734:	167d                	addi	a2,a2,-1
     736:	0642                	slli	a2,a2,0x10
     738:	8241                	srli	a2,a2,0x10
     73a:	00f61863          	bne	a2,a5,74a <SPI_send_DMA+0x32>
     73e:	40020737          	lui	a4,0x40020
     742:	5b1c                	lw	a5,48(a4)
     744:	9bf9                	andi	a5,a5,-2
     746:	db1c                	sw	a5,48(a4)
     748:	8082                	ret
     74a:	c2cc                	sw	a1,4(a3)
     74c:	4298                	lw	a4,0(a3)
     74e:	20077713          	andi	a4,a4,512
     752:	df6d                	beqz	a4,74c <SPI_send_DMA+0x34>
     754:	b7c5                	j	734 <SPI_send_DMA+0x1c>

00000756 <SPI_send>:
     756:	400137b7          	lui	a5,0x40013
     75a:	a7ca                	sh	a0,12(a5)
     75c:	40013737          	lui	a4,0x40013
     760:	271e                	lhu	a5,8(a4)
     762:	8b89                	andi	a5,a5,2
     764:	dff5                	beqz	a5,760 <SPI_send+0xa>
     766:	8082                	ret

00000768 <write_command_8>:
     768:	939ff2ef          	jal	t0,a0 <__riscv_save_0>
     76c:	40011737          	lui	a4,0x40011
     770:	4b5c                	lw	a5,20(a4)
     772:	0087e793          	ori	a5,a5,8
     776:	cb5c                	sw	a5,20(a4)
     778:	3ff9                	jal	756 <SPI_send>
     77a:	931ff06f          	j	aa <__riscv_restore_0>

0000077e <write_data_16>:
     77e:	923ff2ef          	jal	t0,a0 <__riscv_save_0>
     782:	40011737          	lui	a4,0x40011
     786:	4b1c                	lw	a5,16(a4)
     788:	842a                	mv	s0,a0
     78a:	8121                	srli	a0,a0,0x8
     78c:	0087e793          	ori	a5,a5,8
     790:	cb1c                	sw	a5,16(a4)
     792:	37d1                	jal	756 <SPI_send>
     794:	0ff47513          	andi	a0,s0,255
     798:	3f7d                	jal	756 <SPI_send>
     79a:	911ff06f          	j	aa <__riscv_restore_0>

0000079e <tft_set_window>:
     79e:	903ff2ef          	jal	t0,a0 <__riscv_save_0>
     7a2:	1151                	addi	sp,sp,-12
     7a4:	842a                	mv	s0,a0
     7a6:	02a00513          	li	a0,42
     7aa:	c036                	sw	a3,0(sp)
     7ac:	c42e                	sw	a1,8(sp)
     7ae:	c232                	sw	a2,4(sp)
     7b0:	3f65                	jal	768 <write_command_8>
     7b2:	8522                	mv	a0,s0
     7b4:	37e9                	jal	77e <write_data_16>
     7b6:	4612                	lw	a2,4(sp)
     7b8:	8532                	mv	a0,a2
     7ba:	37d1                	jal	77e <write_data_16>
     7bc:	02b00513          	li	a0,43
     7c0:	3765                	jal	768 <write_command_8>
     7c2:	45a2                	lw	a1,8(sp)
     7c4:	852e                	mv	a0,a1
     7c6:	3f65                	jal	77e <write_data_16>
     7c8:	4682                	lw	a3,0(sp)
     7ca:	8536                	mv	a0,a3
     7cc:	3f4d                	jal	77e <write_data_16>
     7ce:	02c00513          	li	a0,44
     7d2:	3f59                	jal	768 <write_command_8>
     7d4:	0131                	addi	sp,sp,12
     7d6:	8d5ff06f          	j	aa <__riscv_restore_0>

000007da <tft_init>:
     7da:	8c7ff2ef          	jal	t0,a0 <__riscv_save_0>
     7de:	400216b7          	lui	a3,0x40021
     7e2:	4e9c                	lw	a5,24(a3)
     7e4:	6705                	lui	a4,0x1
     7e6:	0741                	addi	a4,a4,16
     7e8:	8fd9                	or	a5,a5,a4
     7ea:	40011437          	lui	s0,0x40011
     7ee:	ce9c                	sw	a5,24(a3)
     7f0:	401c                	lw	a5,0(s0)
     7f2:	777d                	lui	a4,0xfffff
     7f4:	0ff70713          	addi	a4,a4,255 # fffff0ff <__global_pointer$+0xdfffe8bf>
     7f8:	8ff9                	and	a5,a5,a4
     7fa:	c01c                	sw	a5,0(s0)
     7fc:	401c                	lw	a5,0(s0)
     7fe:	7745                	lui	a4,0xffff1
     800:	177d                	addi	a4,a4,-1
     802:	3007e793          	ori	a5,a5,768
     806:	c01c                	sw	a5,0(s0)
     808:	401c                	lw	a5,0(s0)
     80a:	fff10637          	lui	a2,0xfff10
     80e:	167d                	addi	a2,a2,-1
     810:	8ff9                	and	a5,a5,a4
     812:	c01c                	sw	a5,0(s0)
     814:	401c                	lw	a5,0(s0)
     816:	670d                	lui	a4,0x3
     818:	1101                	addi	sp,sp,-32
     81a:	8fd9                	or	a5,a5,a4
     81c:	c01c                	sw	a5,0(s0)
     81e:	401c                	lw	a5,0(s0)
     820:	0b070713          	addi	a4,a4,176 # 30b0 <_data_lma+0xcd0>
     824:	03200513          	li	a0,50
     828:	8ff1                	and	a5,a5,a2
     82a:	c01c                	sw	a5,0(s0)
     82c:	401c                	lw	a5,0(s0)
     82e:	00030637          	lui	a2,0x30
     832:	8fd1                	or	a5,a5,a2
     834:	c01c                	sw	a5,0(s0)
     836:	401c                	lw	a5,0(s0)
     838:	ff100637          	lui	a2,0xff100
     83c:	167d                	addi	a2,a2,-1
     83e:	8ff1                	and	a5,a5,a2
     840:	c01c                	sw	a5,0(s0)
     842:	401c                	lw	a5,0(s0)
     844:	00b00637          	lui	a2,0xb00
     848:	8fd1                	or	a5,a5,a2
     84a:	c01c                	sw	a5,0(s0)
     84c:	401c                	lw	a5,0(s0)
     84e:	f1000637          	lui	a2,0xf1000
     852:	167d                	addi	a2,a2,-1
     854:	8ff1                	and	a5,a5,a2
     856:	c01c                	sw	a5,0(s0)
     858:	401c                	lw	a5,0(s0)
     85a:	0b000637          	lui	a2,0xb000
     85e:	8fd1                	or	a5,a5,a2
     860:	7671                	lui	a2,0xffffc
     862:	c01c                	sw	a5,0(s0)
     864:	30460613          	addi	a2,a2,772 # ffffc304 <__global_pointer$+0xdfffbac4>
     868:	400137b7          	lui	a5,0x40013
     86c:	a392                	sh	a2,0(a5)
     86e:	461d                	li	a2,7
     870:	ab92                	sh	a2,16(a5)
     872:	23d2                	lhu	a2,4(a5)
     874:	07b1                	addi	a5,a5,12
     876:	00266613          	ori	a2,a2,2
     87a:	fec79c23          	sh	a2,-8(a5) # 40012ff8 <__global_pointer$+0x200127b8>
     87e:	ff47d603          	lhu	a2,-12(a5)
     882:	04066613          	ori	a2,a2,64
     886:	fec79a23          	sh	a2,-12(a5)
     88a:	4ad0                	lw	a2,20(a3)
     88c:	00166613          	ori	a2,a2,1
     890:	cad0                	sw	a2,20(a3)
     892:	400206b7          	lui	a3,0x40020
     896:	da98                	sw	a4,48(a3)
     898:	de9c                	sw	a5,56(a3)
     89a:	485c                	lw	a5,20(s0)
     89c:	0047e793          	ori	a5,a5,4
     8a0:	c85c                	sw	a5,20(s0)
     8a2:	473000ef          	jal	ra,1514 <Delay_Ms>
     8a6:	481c                	lw	a5,16(s0)
     8a8:	03200513          	li	a0,50
     8ac:	0047e793          	ori	a5,a5,4
     8b0:	c81c                	sw	a5,16(s0)
     8b2:	463000ef          	jal	ra,1514 <Delay_Ms>
     8b6:	485c                	lw	a5,20(s0)
     8b8:	4545                	li	a0,17
     8ba:	0107e793          	ori	a5,a5,16
     8be:	c85c                	sw	a5,20(s0)
     8c0:	3565                	jal	768 <write_command_8>
     8c2:	07800513          	li	a0,120
     8c6:	44f000ef          	jal	ra,1514 <Delay_Ms>
     8ca:	03600513          	li	a0,54
     8ce:	3d69                	jal	768 <write_command_8>
     8d0:	481c                	lw	a5,16(s0)
     8d2:	0a800513          	li	a0,168
     8d6:	0087e793          	ori	a5,a5,8
     8da:	c81c                	sw	a5,16(s0)
     8dc:	3dad                	jal	756 <SPI_send>
     8de:	03a00513          	li	a0,58
     8e2:	3559                	jal	768 <write_command_8>
     8e4:	481c                	lw	a5,16(s0)
     8e6:	4515                	li	a0,5
     8e8:	0087e793          	ori	a5,a5,8
     8ec:	c81c                	sw	a5,16(s0)
     8ee:	35a5                	jal	756 <SPI_send>
     8f0:	6589                	lui	a1,0x2
     8f2:	cd858493          	addi	s1,a1,-808 # 1cd8 <memcpy+0xd4>
     8f6:	4641                	li	a2,16
     8f8:	cd858593          	addi	a1,a1,-808
     8fc:	850a                	mv	a0,sp
     8fe:	306010ef          	jal	ra,1c04 <memcpy>
     902:	0e000513          	li	a0,224
     906:	358d                	jal	768 <write_command_8>
     908:	481c                	lw	a5,16(s0)
     90a:	850a                	mv	a0,sp
     90c:	4605                	li	a2,1
     90e:	0087e793          	ori	a5,a5,8
     912:	c81c                	sw	a5,16(s0)
     914:	45c1                	li	a1,16
     916:	3509                	jal	718 <SPI_send_DMA>
     918:	01048593          	addi	a1,s1,16
     91c:	4641                	li	a2,16
     91e:	0808                	addi	a0,sp,16
     920:	2e4010ef          	jal	ra,1c04 <memcpy>
     924:	0e100513          	li	a0,225
     928:	3581                	jal	768 <write_command_8>
     92a:	481c                	lw	a5,16(s0)
     92c:	4605                	li	a2,1
     92e:	45c1                	li	a1,16
     930:	0087e793          	ori	a5,a5,8
     934:	c81c                	sw	a5,16(s0)
     936:	0808                	addi	a0,sp,16
     938:	33c5                	jal	718 <SPI_send_DMA>
     93a:	4529                	li	a0,10
     93c:	3d9000ef          	jal	ra,1514 <Delay_Ms>
     940:	02100513          	li	a0,33
     944:	3515                	jal	768 <write_command_8>
     946:	454d                	li	a0,19
     948:	3505                	jal	768 <write_command_8>
     94a:	4529                	li	a0,10
     94c:	3c9000ef          	jal	ra,1514 <Delay_Ms>
     950:	02900513          	li	a0,41
     954:	3d11                	jal	768 <write_command_8>
     956:	4529                	li	a0,10
     958:	3bd000ef          	jal	ra,1514 <Delay_Ms>
     95c:	481c                	lw	a5,16(s0)
     95e:	0107e793          	ori	a5,a5,16
     962:	c81c                	sw	a5,16(s0)
     964:	6105                	addi	sp,sp,32
     966:	f44ff06f          	j	aa <__riscv_restore_0>

0000096a <tft_set_cursor>:
     96a:	0505                	addi	a0,a0,1
     96c:	96a19223          	sh	a0,-1692(gp) # 200001a4 <_cursor_x>
     970:	05e9                	addi	a1,a1,26
     972:	96b19323          	sh	a1,-1690(gp) # 200001a6 <_cursor_y>
     976:	8082                	ret

00000978 <tft_set_color>:
     978:	200007b7          	lui	a5,0x20000
     97c:	00a79023          	sh	a0,0(a5) # 20000000 <_highcode_vma_end>
     980:	8082                	ret

00000982 <tft_set_background_color>:
     982:	82a19123          	sh	a0,-2014(gp) # 20000062 <_bg_color>
     986:	8082                	ret

00000988 <tft_print_char>:
     988:	f18ff2ef          	jal	t0,a0 <__riscv_save_0>
     98c:	00251793          	slli	a5,a0,0x2
     990:	953e                	add	a0,a0,a5
     992:	8221d783          	lhu	a5,-2014(gp) # 20000062 <_bg_color>
     996:	1131                	addi	sp,sp,-20
     998:	0087d713          	srli	a4,a5,0x8
     99c:	0ff7f793          	andi	a5,a5,255
     9a0:	c63e                	sw	a5,12(sp)
     9a2:	200007b7          	lui	a5,0x20000
     9a6:	0007d783          	lhu	a5,0(a5) # 20000000 <_highcode_vma_end>
     9aa:	c43a                	sw	a4,8(sp)
     9ac:	4281                	li	t0,0
     9ae:	0087d713          	srli	a4,a5,0x8
     9b2:	0ff7f793          	andi	a5,a5,255
     9b6:	c23e                	sw	a5,4(sp)
     9b8:	6789                	lui	a5,0x2
     9ba:	ed878793          	addi	a5,a5,-296 # 1ed8 <font>
     9be:	c03a                	sw	a4,0(sp)
     9c0:	4681                	li	a3,0
     9c2:	c83e                	sw	a5,16(sp)
     9c4:	82418313          	addi	t1,gp,-2012 # 20000064 <_buffer>
     9c8:	4785                	li	a5,1
     9ca:	005790b3          	sll	ra,a5,t0
     9ce:	85b6                	mv	a1,a3
     9d0:	4601                	li	a2,0
     9d2:	44c2                	lw	s1,16(sp)
     9d4:	00c503b3          	add	t2,a0,a2
     9d8:	872e                	mv	a4,a1
     9da:	93a6                	add	t2,t2,s1
     9dc:	0003c383          	lbu	t2,0(t2)
     9e0:	00158793          	addi	a5,a1,1
     9e4:	0589                	addi	a1,a1,2
     9e6:	07c2                	slli	a5,a5,0x10
     9e8:	05c2                	slli	a1,a1,0x10
     9ea:	0013f3b3          	and	t2,t2,ra
     9ee:	83c1                	srli	a5,a5,0x10
     9f0:	81c1                	srli	a1,a1,0x10
     9f2:	971a                	add	a4,a4,t1
     9f4:	06038763          	beqz	t2,a62 <tft_print_char+0xda>
     9f8:	4482                	lw	s1,0(sp)
     9fa:	979a                	add	a5,a5,t1
     9fc:	a304                	sb	s1,0(a4)
     9fe:	4712                	lw	a4,4(sp)
     a00:	a398                	sb	a4,0(a5)
     a02:	0605                	addi	a2,a2,1
     a04:	4795                	li	a5,5
     a06:	fcf616e3          	bne	a2,a5,9d2 <tft_print_char+0x4a>
     a0a:	06a9                	addi	a3,a3,10
     a0c:	06c2                	slli	a3,a3,0x10
     a0e:	82c1                	srli	a3,a3,0x10
     a10:	04600793          	li	a5,70
     a14:	0285                	addi	t0,t0,1
     a16:	faf699e3          	bne	a3,a5,9c8 <tft_print_char+0x40>
     a1a:	400114b7          	lui	s1,0x40011
     a1e:	48dc                	lw	a5,20(s1)
     a20:	0107e793          	ori	a5,a5,16
     a24:	c8dc                	sw	a5,20(s1)
     a26:	9641d503          	lhu	a0,-1692(gp) # 200001a4 <_cursor_x>
     a2a:	9661d583          	lhu	a1,-1690(gp) # 200001a6 <_cursor_y>
     a2e:	00450613          	addi	a2,a0,4
     a32:	0642                	slli	a2,a2,0x10
     a34:	00658693          	addi	a3,a1,6
     a38:	06c2                	slli	a3,a3,0x10
     a3a:	82c1                	srli	a3,a3,0x10
     a3c:	8241                	srli	a2,a2,0x10
     a3e:	3385                	jal	79e <tft_set_window>
     a40:	489c                	lw	a5,16(s1)
     a42:	4605                	li	a2,1
     a44:	04600593          	li	a1,70
     a48:	0087e793          	ori	a5,a5,8
     a4c:	c89c                	sw	a5,16(s1)
     a4e:	82418513          	addi	a0,gp,-2012 # 20000064 <_buffer>
     a52:	31d9                	jal	718 <SPI_send_DMA>
     a54:	489c                	lw	a5,16(s1)
     a56:	0107e793          	ori	a5,a5,16
     a5a:	c89c                	sw	a5,16(s1)
     a5c:	0151                	addi	sp,sp,20
     a5e:	e4cff06f          	j	aa <__riscv_restore_0>
     a62:	44a2                	lw	s1,8(sp)
     a64:	979a                	add	a5,a5,t1
     a66:	a304                	sb	s1,0(a4)
     a68:	4732                	lw	a4,12(sp)
     a6a:	bf59                	j	a00 <tft_print_char+0x78>

00000a6c <tft_print>:
     a6c:	e34ff2ef          	jal	t0,a0 <__riscv_save_0>
     a70:	842a                	mv	s0,a0
     a72:	00040503          	lb	a0,0(s0) # 40011000 <__global_pointer$+0x200107c0>
     a76:	e119                	bnez	a0,a7c <tft_print+0x10>
     a78:	e32ff06f          	j	aa <__riscv_restore_0>
     a7c:	3731                	jal	988 <tft_print_char>
     a7e:	96418713          	addi	a4,gp,-1692 # 200001a4 <_cursor_x>
     a82:	231e                	lhu	a5,0(a4)
     a84:	0405                	addi	s0,s0,1
     a86:	0799                	addi	a5,a5,6
     a88:	a31e                	sh	a5,0(a4)
     a8a:	b7e5                	j	a72 <tft_print+0x6>

00000a8c <tft_print_number>:
     a8c:	e14ff2ef          	jal	t0,a0 <__riscv_save_0>
     a90:	1141                	addi	sp,sp,-16
     a92:	87aa                	mv	a5,a0
     a94:	86ae                	mv	a3,a1
     a96:	4701                	li	a4,0
     a98:	00055563          	bgez	a0,aa2 <tft_print_number+0x16>
     a9c:	40a007b3          	neg	a5,a0
     aa0:	4705                	li	a4,1
     aa2:	96818613          	addi	a2,gp,-1688 # 200001a8 <str.4169>
     aa6:	000605a3          	sb	zero,11(a2)
     aaa:	442d                	li	s0,11
     aac:	96818493          	addi	s1,gp,-1688 # 200001a8 <str.4169>
     ab0:	eba9                	bnez	a5,b02 <tft_print_number+0x76>
     ab2:	47ad                	li	a5,11
     ab4:	00f41663          	bne	s0,a5,ac0 <tft_print_number+0x34>
     ab8:	03000793          	li	a5,48
     abc:	a4bc                	sb	a5,10(s1)
     abe:	4429                	li	s0,10
     ac0:	cb09                	beqz	a4,ad2 <tft_print_number+0x46>
     ac2:	147d                	addi	s0,s0,-1
     ac4:	0ff47413          	andi	s0,s0,255
     ac8:	008487b3          	add	a5,s1,s0
     acc:	02d00713          	li	a4,45
     ad0:	a398                	sb	a4,0(a5)
     ad2:	472d                	li	a4,11
     ad4:	8f01                	sub	a4,a4,s0
     ad6:	00171793          	slli	a5,a4,0x1
     ada:	97ba                	add	a5,a5,a4
     adc:	0786                	slli	a5,a5,0x1
     ade:	17fd                	addi	a5,a5,-1
     ae0:	07c2                	slli	a5,a5,0x10
     ae2:	83c1                	srli	a5,a5,0x10
     ae4:	00d7f963          	bgeu	a5,a3,af6 <tft_print_number+0x6a>
     ae8:	96418713          	addi	a4,gp,-1692 # 200001a4 <_cursor_x>
     aec:	2312                	lhu	a2,0(a4)
     aee:	96b2                	add	a3,a3,a2
     af0:	40f687b3          	sub	a5,a3,a5
     af4:	a31e                	sh	a5,0(a4)
     af6:	00848533          	add	a0,s1,s0
     afa:	3f8d                	jal	a6c <tft_print>
     afc:	0141                	addi	sp,sp,16
     afe:	dacff06f          	j	aa <__riscv_restore_0>
     b02:	147d                	addi	s0,s0,-1
     b04:	0ff47413          	andi	s0,s0,255
     b08:	00848633          	add	a2,s1,s0
     b0c:	45a9                	li	a1,10
     b0e:	853e                	mv	a0,a5
     b10:	c636                	sw	a3,12(sp)
     b12:	c43a                	sw	a4,8(sp)
     b14:	c232                	sw	a2,4(sp)
     b16:	c03e                	sw	a5,0(sp)
     b18:	e0cff0ef          	jal	ra,124 <__modsi3>
     b1c:	4782                	lw	a5,0(sp)
     b1e:	4612                	lw	a2,4(sp)
     b20:	03050513          	addi	a0,a0,48
     b24:	45a9                	li	a1,10
     b26:	a208                	sb	a0,0(a2)
     b28:	853e                	mv	a0,a5
     b2a:	da2ff0ef          	jal	ra,cc <__divsi3>
     b2e:	87aa                	mv	a5,a0
     b30:	46b2                	lw	a3,12(sp)
     b32:	4722                	lw	a4,8(sp)
     b34:	bfb5                	j	ab0 <tft_print_number+0x24>

00000b36 <tft_fill_rect>:
     b36:	d6aff2ef          	jal	t0,a0 <__riscv_save_0>
     b3a:	0505                	addi	a0,a0,1
     b3c:	05e9                	addi	a1,a1,26
     b3e:	0542                	slli	a0,a0,0x10
     b40:	05c2                	slli	a1,a1,0x10
     b42:	8336                	mv	t1,a3
     b44:	1171                	addi	sp,sp,-4
     b46:	8141                	srli	a0,a0,0x10
     b48:	81c1                	srli	a1,a1,0x10
     b4a:	00875393          	srli	t2,a4,0x8
     b4e:	4781                	li	a5,0
     b50:	82418693          	addi	a3,gp,-2012 # 20000064 <_buffer>
     b54:	00179413          	slli	s0,a5,0x1
     b58:	0442                	slli	s0,s0,0x10
     b5a:	8041                	srli	s0,s0,0x10
     b5c:	04c79663          	bne	a5,a2,ba8 <tft_fill_rect+0x72>
     b60:	400114b7          	lui	s1,0x40011
     b64:	48d8                	lw	a4,20(s1)
     b66:	fff30693          	addi	a3,t1,-1
     b6a:	fff78613          	addi	a2,a5,-1
     b6e:	96ae                	add	a3,a3,a1
     b70:	962a                	add	a2,a2,a0
     b72:	01076713          	ori	a4,a4,16
     b76:	06c2                	slli	a3,a3,0x10
     b78:	0642                	slli	a2,a2,0x10
     b7a:	c8d8                	sw	a4,20(s1)
     b7c:	82c1                	srli	a3,a3,0x10
     b7e:	8241                	srli	a2,a2,0x10
     b80:	c01a                	sw	t1,0(sp)
     b82:	3931                	jal	79e <tft_set_window>
     b84:	489c                	lw	a5,16(s1)
     b86:	4302                	lw	t1,0(sp)
     b88:	0087e793          	ori	a5,a5,8
     b8c:	c89c                	sw	a5,16(s1)
     b8e:	861a                	mv	a2,t1
     b90:	85a2                	mv	a1,s0
     b92:	82418513          	addi	a0,gp,-2012 # 20000064 <_buffer>
     b96:	b83ff0ef          	jal	ra,718 <SPI_send_DMA>
     b9a:	489c                	lw	a5,16(s1)
     b9c:	0107e793          	ori	a5,a5,16
     ba0:	c89c                	sw	a5,16(s1)
     ba2:	0111                	addi	sp,sp,4
     ba4:	d06ff06f          	j	aa <__riscv_restore_0>
     ba8:	008684b3          	add	s1,a3,s0
     bac:	0405                	addi	s0,s0,1
     bae:	0442                	slli	s0,s0,0x10
     bb0:	8041                	srli	s0,s0,0x10
     bb2:	0785                	addi	a5,a5,1
     bb4:	00748023          	sb	t2,0(s1) # 40011000 <__global_pointer$+0x200107c0>
     bb8:	9436                	add	s0,s0,a3
     bba:	07c2                	slli	a5,a5,0x10
     bbc:	a018                	sb	a4,0(s0)
     bbe:	83c1                	srli	a5,a5,0x10
     bc0:	bf51                	j	b54 <tft_fill_rect+0x1e>

00000bc2 <SystemInit>:
     bc2:	cdeff2ef          	jal	t0,a0 <__riscv_save_0>
     bc6:	40021437          	lui	s0,0x40021
     bca:	401c                	lw	a5,0(s0)
     bcc:	f8ff0737          	lui	a4,0xf8ff0
     bd0:	1161                	addi	sp,sp,-8
     bd2:	0017e793          	ori	a5,a5,1
     bd6:	c01c                	sw	a5,0(s0)
     bd8:	405c                	lw	a5,4(s0)
     bda:	4541                	li	a0,16
     bdc:	8ff9                	and	a5,a5,a4
     bde:	c05c                	sw	a5,4(s0)
     be0:	401c                	lw	a5,0(s0)
     be2:	fef70737          	lui	a4,0xfef70
     be6:	177d                	addi	a4,a4,-1
     be8:	8ff9                	and	a5,a5,a4
     bea:	c01c                	sw	a5,0(s0)
     bec:	401c                	lw	a5,0(s0)
     bee:	fffc0737          	lui	a4,0xfffc0
     bf2:	177d                	addi	a4,a4,-1
     bf4:	8ff9                	and	a5,a5,a4
     bf6:	c01c                	sw	a5,0(s0)
     bf8:	405c                	lw	a5,4(s0)
     bfa:	7741                	lui	a4,0xffff0
     bfc:	177d                	addi	a4,a4,-1
     bfe:	8ff9                	and	a5,a5,a4
     c00:	c05c                	sw	a5,4(s0)
     c02:	009f07b7          	lui	a5,0x9f0
     c06:	c41c                	sw	a5,8(s0)
     c08:	2375                	jal	11b4 <RCC_AdjustHSICalibrationValue>
     c0a:	4c1c                	lw	a5,24(s0)
     c0c:	00020637          	lui	a2,0x20
     c10:	0207e793          	ori	a5,a5,32
     c14:	cc1c                	sw	a5,24(s0)
     c16:	400117b7          	lui	a5,0x40011
     c1a:	4007a703          	lw	a4,1024(a5) # 40011400 <__global_pointer$+0x20010bc0>
     c1e:	40078693          	addi	a3,a5,1024
     c22:	f0f77713          	andi	a4,a4,-241
     c26:	40e7a023          	sw	a4,1024(a5)
     c2a:	4007a703          	lw	a4,1024(a5)
     c2e:	08076713          	ori	a4,a4,128
     c32:	40e7a023          	sw	a4,1024(a5)
     c36:	4789                	li	a5,2
     c38:	ca9c                	sw	a5,16(a3)
     c3a:	c002                	sw	zero,0(sp)
     c3c:	c202                	sw	zero,4(sp)
     c3e:	4c1c                	lw	a5,24(s0)
     c40:	40010737          	lui	a4,0x40010
     c44:	66a1                	lui	a3,0x8
     c46:	0017e793          	ori	a5,a5,1
     c4a:	cc1c                	sw	a5,24(s0)
     c4c:	435c                	lw	a5,4(a4)
     c4e:	8fd5                	or	a5,a5,a3
     c50:	c35c                	sw	a5,4(a4)
     c52:	401c                	lw	a5,0(s0)
     c54:	6741                	lui	a4,0x10
     c56:	400216b7          	lui	a3,0x40021
     c5a:	8fd9                	or	a5,a5,a4
     c5c:	c01c                	sw	a5,0(s0)
     c5e:	6709                	lui	a4,0x2
     c60:	429c                	lw	a5,0(a3)
     c62:	8ff1                	and	a5,a5,a2
     c64:	c23e                	sw	a5,4(sp)
     c66:	4782                	lw	a5,0(sp)
     c68:	0785                	addi	a5,a5,1
     c6a:	c03e                	sw	a5,0(sp)
     c6c:	4792                	lw	a5,4(sp)
     c6e:	e781                	bnez	a5,c76 <SystemInit+0xb4>
     c70:	4782                	lw	a5,0(sp)
     c72:	fee797e3          	bne	a5,a4,c60 <SystemInit+0x9e>
     c76:	400217b7          	lui	a5,0x40021
     c7a:	439c                	lw	a5,0(a5)
     c7c:	00e79713          	slli	a4,a5,0xe
     c80:	06075963          	bgez	a4,cf2 <SystemInit+0x130>
     c84:	4785                	li	a5,1
     c86:	c23e                	sw	a5,4(sp)
     c88:	4712                	lw	a4,4(sp)
     c8a:	4785                	li	a5,1
     c8c:	06f71063          	bne	a4,a5,cec <SystemInit+0x12a>
     c90:	400227b7          	lui	a5,0x40022
     c94:	4398                	lw	a4,0(a5)
     c96:	76c1                	lui	a3,0xffff0
     c98:	16fd                	addi	a3,a3,-1
     c9a:	9b71                	andi	a4,a4,-4
     c9c:	c398                	sw	a4,0(a5)
     c9e:	4398                	lw	a4,0(a5)
     ca0:	00176713          	ori	a4,a4,1
     ca4:	c398                	sw	a4,0(a5)
     ca6:	400217b7          	lui	a5,0x40021
     caa:	43d8                	lw	a4,4(a5)
     cac:	c3d8                	sw	a4,4(a5)
     cae:	43d8                	lw	a4,4(a5)
     cb0:	8f75                	and	a4,a4,a3
     cb2:	c3d8                	sw	a4,4(a5)
     cb4:	43d8                	lw	a4,4(a5)
     cb6:	66c1                	lui	a3,0x10
     cb8:	8f55                	or	a4,a4,a3
     cba:	c3d8                	sw	a4,4(a5)
     cbc:	4398                	lw	a4,0(a5)
     cbe:	010006b7          	lui	a3,0x1000
     cc2:	8f55                	or	a4,a4,a3
     cc4:	c398                	sw	a4,0(a5)
     cc6:	4398                	lw	a4,0(a5)
     cc8:	00671693          	slli	a3,a4,0x6
     ccc:	fe06dde3          	bgez	a3,cc6 <SystemInit+0x104>
     cd0:	43d8                	lw	a4,4(a5)
     cd2:	400216b7          	lui	a3,0x40021
     cd6:	9b71                	andi	a4,a4,-4
     cd8:	c3d8                	sw	a4,4(a5)
     cda:	43d8                	lw	a4,4(a5)
     cdc:	00276713          	ori	a4,a4,2
     ce0:	c3d8                	sw	a4,4(a5)
     ce2:	4721                	li	a4,8
     ce4:	42dc                	lw	a5,4(a3)
     ce6:	8bb1                	andi	a5,a5,12
     ce8:	fee79ee3          	bne	a5,a4,ce4 <SystemInit+0x122>
     cec:	0121                	addi	sp,sp,8
     cee:	bbcff06f          	j	aa <__riscv_restore_0>
     cf2:	c202                	sw	zero,4(sp)
     cf4:	bf51                	j	c88 <SystemInit+0xc6>

00000cf6 <SystemCoreClockUpdate>:
     cf6:	baaff2ef          	jal	t0,a0 <__riscv_save_0>
     cfa:	40021737          	lui	a4,0x40021
     cfe:	435c                	lw	a5,4(a4)
     d00:	20000437          	lui	s0,0x20000
     d04:	4691                	li	a3,4
     d06:	8bb1                	andi	a5,a5,12
     d08:	01440413          	addi	s0,s0,20 # 20000014 <SystemCoreClock>
     d0c:	00d78563          	beq	a5,a3,d16 <SystemCoreClockUpdate+0x20>
     d10:	46a1                	li	a3,8
     d12:	04d78263          	beq	a5,a3,d56 <SystemCoreClockUpdate+0x60>
     d16:	016e37b7          	lui	a5,0x16e3
     d1a:	60078793          	addi	a5,a5,1536 # 16e3600 <_data_lma+0x16e1220>
     d1e:	c01c                	sw	a5,0(s0)
     d20:	400216b7          	lui	a3,0x40021
     d24:	42dc                	lw	a5,4(a3)
     d26:	4008                	lw	a0,0(s0)
     d28:	8391                	srli	a5,a5,0x4
     d2a:	00f7f713          	andi	a4,a5,15
     d2e:	200007b7          	lui	a5,0x20000
     d32:	00478793          	addi	a5,a5,4 # 20000004 <AHBPrescTable>
     d36:	97ba                	add	a5,a5,a4
     d38:	238c                	lbu	a1,0(a5)
     d3a:	42dc                	lw	a5,4(a3)
     d3c:	0ff5f593          	andi	a1,a1,255
     d40:	0807f793          	andi	a5,a5,128
     d44:	00b55733          	srl	a4,a0,a1
     d48:	e781                	bnez	a5,d50 <SystemCoreClockUpdate+0x5a>
     d4a:	b8aff0ef          	jal	ra,d4 <__udivsi3>
     d4e:	872a                	mv	a4,a0
     d50:	c018                	sw	a4,0(s0)
     d52:	b58ff06f          	j	aa <__riscv_restore_0>
     d56:	435c                	lw	a5,4(a4)
     d58:	02dc77b7          	lui	a5,0x2dc7
     d5c:	c0078793          	addi	a5,a5,-1024 # 2dc6c00 <_data_lma+0x2dc4820>
     d60:	bf7d                	j	d1e <SystemCoreClockUpdate+0x28>

00000d62 <Touch_Button_GPIO_Config>:
     d62:	b3eff2ef          	jal	t0,a0 <__riscv_save_0>
     d66:	1151                	addi	sp,sp,-12
     d68:	4585                	li	a1,1
     d6a:	02000513          	li	a0,32
     d6e:	c002                	sw	zero,0(sp)
     d70:	c202                	sw	zero,4(sp)
     d72:	c402                	sw	zero,8(sp)
     d74:	29d5                	jal	1268 <RCC_APB2PeriphClockCmd>
     d76:	4785                	li	a5,1
     d78:	40011537          	lui	a0,0x40011
     d7c:	807c                	sh	a5,0(sp)
     d7e:	858a                	mv	a1,sp
     d80:	04800793          	li	a5,72
     d84:	40050513          	addi	a0,a0,1024 # 40011400 <__global_pointer$+0x20010bc0>
     d88:	c43e                	sw	a5,8(sp)
     d8a:	2e29                	jal	10a4 <GPIO_Init>
     d8c:	0131                	addi	sp,sp,12
     d8e:	b1cff06f          	j	aa <__riscv_restore_0>

00000d92 <Touch_Button_Timer_Init>:
     d92:	b0eff2ef          	jal	t0,a0 <__riscv_save_0>
     d96:	1131                	addi	sp,sp,-20
     d98:	4585                	li	a1,1
     d9a:	4505                	li	a0,1
     d9c:	c402                	sw	zero,8(sp)
     d9e:	c602                	sw	zero,12(sp)
     da0:	00011823          	sh	zero,16(sp)
     da4:	c002                	sw	zero,0(sp)
     da6:	c202                	sw	zero,4(sp)
     da8:	29f9                	jal	1286 <RCC_APB1PeriphClockCmd>
     daa:	02f00793          	li	a5,47
     dae:	c43e                	sw	a5,8(sp)
     db0:	002c                	addi	a1,sp,8
     db2:	3e700793          	li	a5,999
     db6:	40000537          	lui	a0,0x40000
     dba:	c63e                	sw	a5,12(sp)
     dbc:	21e5                	jal	12a4 <TIM_TimeBaseInit>
     dbe:	4605                	li	a2,1
     dc0:	4585                	li	a1,1
     dc2:	40000537          	lui	a0,0x40000
     dc6:	23d1                	jal	138a <TIM_ITConfig>
     dc8:	22600793          	li	a5,550
     dcc:	807c                	sh	a5,0(sp)
     dce:	850a                	mv	a0,sp
     dd0:	4785                	li	a5,1
     dd2:	c23e                	sw	a5,4(sp)
     dd4:	00010123          	sb	zero,2(sp)
     dd8:	2ead                	jal	1152 <NVIC_Init>
     dda:	4585                	li	a1,1
     ddc:	40000537          	lui	a0,0x40000
     de0:	2bb5                	jal	135c <TIM_Cmd>
     de2:	0151                	addi	sp,sp,20
     de4:	ac6ff06f          	j	aa <__riscv_restore_0>

00000de8 <Touch_Button_EXTI_Config>:
     de8:	ab8ff2ef          	jal	t0,a0 <__riscv_save_0>
     dec:	1121                	addi	sp,sp,-24
     dee:	4585                	li	a1,1
     df0:	4505                	li	a0,1
     df2:	c402                	sw	zero,8(sp)
     df4:	c602                	sw	zero,12(sp)
     df6:	c802                	sw	zero,16(sp)
     df8:	ca02                	sw	zero,20(sp)
     dfa:	c002                	sw	zero,0(sp)
     dfc:	c202                	sw	zero,4(sp)
     dfe:	21ad                	jal	1268 <RCC_APB2PeriphClockCmd>
     e00:	4581                	li	a1,0
     e02:	450d                	li	a0,3
     e04:	261d                	jal	112a <GPIO_EXTILineConfig>
     e06:	4405                	li	s0,1
     e08:	47c1                	li	a5,16
     e0a:	0028                	addi	a0,sp,8
     e0c:	c422                	sw	s0,8(sp)
     e0e:	c83e                	sw	a5,16(sp)
     e10:	ca22                	sw	s0,20(sp)
     e12:	c602                	sw	zero,12(sp)
     e14:	2afd                	jal	1012 <EXTI_Init>
     e16:	11400793          	li	a5,276
     e1a:	850a                	mv	a0,sp
     e1c:	807c                	sh	a5,0(sp)
     e1e:	8140                	sb	s0,2(sp)
     e20:	c222                	sw	s0,4(sp)
     e22:	2e05                	jal	1152 <NVIC_Init>
     e24:	0161                	addi	sp,sp,24
     e26:	a84ff06f          	j	aa <__riscv_restore_0>

00000e2a <Touch_Button_Init>:
     e2a:	a76ff2ef          	jal	t0,a0 <__riscv_save_0>
     e2e:	97818793          	addi	a5,gp,-1672 # 200001b8 <touch_button>
     e32:	00079823          	sh	zero,16(a5)
     e36:	0007a023          	sw	zero,0(a5)
     e3a:	0007a223          	sw	zero,4(a5)
     e3e:	0007a423          	sw	zero,8(a5)
     e42:	0007a623          	sw	zero,12(a5)
     e46:	00078923          	sb	zero,18(a5)
     e4a:	3f21                	jal	d62 <Touch_Button_GPIO_Config>
     e4c:	3f71                	jal	de8 <Touch_Button_EXTI_Config>
     e4e:	3791                	jal	d92 <Touch_Button_Timer_Init>
     e50:	a5aff06f          	j	aa <__riscv_restore_0>

00000e54 <Touch_Button_Update>:
     e54:	9741a683          	lw	a3,-1676(gp) # 200001b4 <system_tick_ms>
     e58:	97818793          	addi	a5,gp,-1672 # 200001b8 <touch_button>
     e5c:	4790                	lw	a2,8(a5)
     e5e:	438c                	lw	a1,0(a5)
     e60:	4505                	li	a0,1
     e62:	40c68633          	sub	a2,a3,a2
     e66:	97818713          	addi	a4,gp,-1672 # 200001b8 <touch_button>
     e6a:	02a58663          	beq	a1,a0,e96 <Touch_Button_Update+0x42>
     e6e:	c589                	beqz	a1,e78 <Touch_Button_Update+0x24>
     e70:	478d                	li	a5,3
     e72:	04f58063          	beq	a1,a5,eb2 <Touch_Button_Update+0x5e>
     e76:	8082                	ret
     e78:	3b98                	lbu	a4,17(a5)
     e7a:	c729                	beqz	a4,ec4 <Touch_Button_Update+0x70>
     e7c:	2bb8                	lbu	a4,18(a5)
     e7e:	e339                	bnez	a4,ec4 <Touch_Button_Update+0x70>
     e80:	47d8                	lw	a4,12(a5)
     e82:	8e99                	sub	a3,a3,a4
     e84:	7cf00713          	li	a4,1999
     e88:	02d77e63          	bgeu	a4,a3,ec4 <Touch_Button_Update+0x70>
     e8c:	470d                	li	a4,3
     e8e:	000788a3          	sb	zero,17(a5)
     e92:	c3d8                	sw	a4,4(a5)
     e94:	8082                	ret
     e96:	3e700713          	li	a4,999
     e9a:	02c77563          	bgeu	a4,a2,ec4 <Touch_Button_Update+0x70>
     e9e:	4709                	li	a4,2
     ea0:	c398                	sw	a4,0(a5)
     ea2:	c3d8                	sw	a4,4(a5)
     ea4:	2bb8                	lbu	a4,18(a5)
     ea6:	bb8c                	sb	a1,17(a5)
     ea8:	c7d4                	sw	a3,12(a5)
     eaa:	00173713          	seqz	a4,a4
     eae:	abb8                	sb	a4,18(a5)
     eb0:	8082                	ret
     eb2:	3e700793          	li	a5,999
     eb6:	00c7e563          	bltu	a5,a2,ec0 <Touch_Button_Update+0x6c>
     eba:	c348                	sw	a0,4(a4)
     ebc:	bb08                	sb	a0,17(a4)
     ebe:	c754                	sw	a3,12(a4)
     ec0:	00072023          	sw	zero,0(a4) # 40021000 <__global_pointer$+0x200207c0>
     ec4:	8082                	ret

00000ec6 <Touch_Button_Get_Event>:
     ec6:	97818793          	addi	a5,gp,-1672 # 200001b8 <touch_button>
     eca:	43c8                	lw	a0,4(a5)
     ecc:	0007a223          	sw	zero,4(a5)
     ed0:	8082                	ret

00000ed2 <Touch_Button_Display_Hello>:
     ed2:	9ceff2ef          	jal	t0,a0 <__riscv_save_0>
     ed6:	6541                	lui	a0,0x10
     ed8:	157d                	addi	a0,a0,-1
     eda:	a9fff0ef          	jal	ra,978 <tft_set_color>
     ede:	4501                	li	a0,0
     ee0:	aa3ff0ef          	jal	ra,982 <tft_set_background_color>
     ee4:	4701                	li	a4,0
     ee6:	05000693          	li	a3,80
     eea:	0a000613          	li	a2,160
     eee:	4581                	li	a1,0
     ef0:	4501                	li	a0,0
     ef2:	c45ff0ef          	jal	ra,b36 <tft_fill_rect>
     ef6:	02800593          	li	a1,40
     efa:	4579                	li	a0,30
     efc:	a6fff0ef          	jal	ra,96a <tft_set_cursor>
     f00:	00002537          	lui	a0,0x2
     f04:	d6050513          	addi	a0,a0,-672 # 1d60 <CSWTCH.2+0x54>
     f08:	b65ff0ef          	jal	ra,a6c <tft_print>
     f0c:	99eff06f          	j	aa <__riscv_restore_0>

00000f10 <Touch_Button_IRQ_Handler>:
     f10:	990ff2ef          	jal	t0,a0 <__riscv_save_0>
     f14:	4505                	li	a0,1
     f16:	229d                	jal	107c <EXTI_GetITStatus>
     f18:	c505                	beqz	a0,f40 <Touch_Button_IRQ_Handler+0x30>
     f1a:	40011537          	lui	a0,0x40011
     f1e:	4585                	li	a1,1
     f20:	40050513          	addi	a0,a0,1024 # 40011400 <__global_pointer$+0x20010bc0>
     f24:	9741a403          	lw	s0,-1676(gp) # 200001b4 <system_tick_ms>
     f28:	2ae5                	jal	1120 <GPIO_ReadInputDataBit>
     f2a:	97818793          	addi	a5,gp,-1672 # 200001b8 <touch_button>
     f2e:	4398                	lw	a4,0(a5)
     f30:	c911                	beqz	a0,f44 <Touch_Button_IRQ_Handler+0x34>
     f32:	e709                	bnez	a4,f3c <Touch_Button_IRQ_Handler+0x2c>
     f34:	4705                	li	a4,1
     f36:	c398                	sw	a4,0(a5)
     f38:	c780                	sw	s0,8(a5)
     f3a:	ab98                	sb	a4,16(a5)
     f3c:	4505                	li	a0,1
     f3e:	2ab1                	jal	109a <EXTI_ClearITPendingBit>
     f40:	96aff06f          	j	aa <__riscv_restore_0>
     f44:	177d                	addi	a4,a4,-1
     f46:	4685                	li	a3,1
     f48:	fee6eae3          	bltu	a3,a4,f3c <Touch_Button_IRQ_Handler+0x2c>
     f4c:	470d                	li	a4,3
     f4e:	c398                	sw	a4,0(a5)
     f50:	00078823          	sb	zero,16(a5)
     f54:	b7e5                	j	f3c <Touch_Button_IRQ_Handler+0x2c>

00000f56 <ADC1_IRQHandler>:
     f56:	a001                	j	f56 <ADC1_IRQHandler>

00000f58 <handle_reset>:
     f58:	20000197          	auipc	gp,0x20000
     f5c:	8e818193          	addi	gp,gp,-1816 # 20000840 <__global_pointer$>
     f60:	fc018113          	addi	sp,gp,-64 # 20000800 <_eusrstack>
     f64:	0a000513          	li	a0,160
     f68:	1ffff597          	auipc	a1,0x1ffff
     f6c:	09858593          	addi	a1,a1,152 # 20000000 <_highcode_vma_end>
     f70:	1ffff617          	auipc	a2,0x1ffff
     f74:	09060613          	addi	a2,a2,144 # 20000000 <_highcode_vma_end>
     f78:	00c5fa63          	bgeu	a1,a2,f8c <handle_reset+0x34>
     f7c:	00052283          	lw	t0,0(a0)
     f80:	0055a023          	sw	t0,0(a1)
     f84:	0511                	addi	a0,a0,4
     f86:	0591                	addi	a1,a1,4
     f88:	fec5eae3          	bltu	a1,a2,f7c <handle_reset+0x24>
     f8c:	00001517          	auipc	a0,0x1
     f90:	45450513          	addi	a0,a0,1108 # 23e0 <_data_lma>
     f94:	1ffff597          	auipc	a1,0x1ffff
     f98:	06c58593          	addi	a1,a1,108 # 20000000 <_highcode_vma_end>
     f9c:	1ffff617          	auipc	a2,0x1ffff
     fa0:	0a460613          	addi	a2,a2,164 # 20000040 <_edata>
     fa4:	00c5fa63          	bgeu	a1,a2,fb8 <handle_reset+0x60>
     fa8:	00052283          	lw	t0,0(a0)
     fac:	0055a023          	sw	t0,0(a1)
     fb0:	0511                	addi	a0,a0,4
     fb2:	0591                	addi	a1,a1,4
     fb4:	fec5eae3          	bltu	a1,a2,fa8 <handle_reset+0x50>
     fb8:	1ffff517          	auipc	a0,0x1ffff
     fbc:	08850513          	addi	a0,a0,136 # 20000040 <_edata>
     fc0:	99418593          	addi	a1,gp,-1644 # 200001d4 <_ebss>
     fc4:	00b57763          	bgeu	a0,a1,fd2 <handle_reset+0x7a>
     fc8:	00052023          	sw	zero,0(a0)
     fcc:	0511                	addi	a0,a0,4
     fce:	feb56de3          	bltu	a0,a1,fc8 <handle_reset+0x70>
     fd2:	000022b7          	lui	t0,0x2
     fd6:	88028293          	addi	t0,t0,-1920 # 1880 <print+0x4a>
     fda:	30029073          	csrw	mstatus,t0
     fde:	428d                	li	t0,3
     fe0:	80429073          	csrw	0x804,t0
     fe4:	fffff297          	auipc	t0,0xfffff
     fe8:	01c28293          	addi	t0,t0,28 # 0 <_sinit>
     fec:	0032e293          	ori	t0,t0,3
     ff0:	30529073          	csrw	mtvec,t0
     ff4:	bcfff0ef          	jal	ra,bc2 <SystemInit>
     ff8:	fffff297          	auipc	t0,0xfffff
     ffc:	4e828293          	addi	t0,t0,1256 # 4e0 <main>
    1000:	34129073          	csrw	mepc,t0
    1004:	30200073          	mret

00001008 <DBGMCU_GetCHIPID>:
    1008:	1ffff7b7          	lui	a5,0x1ffff
    100c:	7c47a503          	lw	a0,1988(a5) # 1ffff7c4 <_data_lma+0x1fffd3e4>
    1010:	8082                	ret

00001012 <EXTI_Init>:
    1012:	4158                	lw	a4,4(a0)
    1014:	00052303          	lw	t1,0(a0)
    1018:	454c                	lw	a1,12(a0)
    101a:	40010637          	lui	a2,0x40010
    101e:	40060793          	addi	a5,a2,1024 # 40010400 <__global_pointer$+0x2000fbc0>
    1022:	973e                	add	a4,a4,a5
    1024:	fff34693          	not	a3,t1
    1028:	c5b1                	beqz	a1,1074 <EXTI_Init+0x62>
    102a:	40062583          	lw	a1,1024(a2)
    102e:	8df5                	and	a1,a1,a3
    1030:	40b62023          	sw	a1,1024(a2)
    1034:	43d0                	lw	a2,4(a5)
    1036:	8ef1                	and	a3,a3,a2
    1038:	c3d4                	sw	a3,4(a5)
    103a:	4314                	lw	a3,0(a4)
    103c:	0066e6b3          	or	a3,a3,t1
    1040:	c314                	sw	a3,0(a4)
    1042:	4118                	lw	a4,0(a0)
    1044:	4790                	lw	a2,8(a5)
    1046:	fff74693          	not	a3,a4
    104a:	8e75                	and	a2,a2,a3
    104c:	c790                	sw	a2,8(a5)
    104e:	47d0                	lw	a2,12(a5)
    1050:	8ef1                	and	a3,a3,a2
    1052:	c7d4                	sw	a3,12(a5)
    1054:	4514                	lw	a3,8(a0)
    1056:	4641                	li	a2,16
    1058:	00c69963          	bne	a3,a2,106a <EXTI_Init+0x58>
    105c:	4794                	lw	a3,8(a5)
    105e:	8ed9                	or	a3,a3,a4
    1060:	c794                	sw	a3,8(a5)
    1062:	47d4                	lw	a3,12(a5)
    1064:	8f55                	or	a4,a4,a3
    1066:	c7d8                	sw	a4,12(a5)
    1068:	8082                	ret
    106a:	97b6                	add	a5,a5,a3
    106c:	4394                	lw	a3,0(a5)
    106e:	8f55                	or	a4,a4,a3
    1070:	c398                	sw	a4,0(a5)
    1072:	8082                	ret
    1074:	431c                	lw	a5,0(a4)
    1076:	8ff5                	and	a5,a5,a3
    1078:	c31c                	sw	a5,0(a4)
    107a:	8082                	ret

0000107c <EXTI_GetITStatus>:
    107c:	400107b7          	lui	a5,0x40010
    1080:	40078713          	addi	a4,a5,1024 # 40010400 <__global_pointer$+0x2000fbc0>
    1084:	4007a783          	lw	a5,1024(a5)
    1088:	4b58                	lw	a4,20(a4)
    108a:	8f69                	and	a4,a4,a0
    108c:	c709                	beqz	a4,1096 <EXTI_GetITStatus+0x1a>
    108e:	8d7d                	and	a0,a0,a5
    1090:	00a03533          	snez	a0,a0
    1094:	8082                	ret
    1096:	4501                	li	a0,0
    1098:	8082                	ret

0000109a <EXTI_ClearITPendingBit>:
    109a:	400107b7          	lui	a5,0x40010
    109e:	40a7aa23          	sw	a0,1044(a5) # 40010414 <__global_pointer$+0x2000fbd4>
    10a2:	8082                	ret

000010a4 <GPIO_Init>:
    10a4:	4594                	lw	a3,8(a1)
    10a6:	0106f793          	andi	a5,a3,16
    10aa:	00f6f293          	andi	t0,a3,15
    10ae:	c781                	beqz	a5,10b6 <GPIO_Init+0x12>
    10b0:	41dc                	lw	a5,4(a1)
    10b2:	00f2e2b3          	or	t0,t0,a5
    10b6:	0005d383          	lhu	t2,0(a1)
    10ba:	0ff3f793          	andi	a5,t2,255
    10be:	c3a5                	beqz	a5,111e <GPIO_Init+0x7a>
    10c0:	00052303          	lw	t1,0(a0)
    10c4:	1161                	addi	sp,sp,-8
    10c6:	c222                	sw	s0,4(sp)
    10c8:	c026                	sw	s1,0(sp)
    10ca:	4781                	li	a5,0
    10cc:	02800413          	li	s0,40
    10d0:	04800493          	li	s1,72
    10d4:	4705                	li	a4,1
    10d6:	00f71633          	sll	a2,a4,a5
    10da:	00c3f733          	and	a4,t2,a2
    10de:	02e61263          	bne	a2,a4,1102 <GPIO_Init+0x5e>
    10e2:	00279593          	slli	a1,a5,0x2
    10e6:	473d                	li	a4,15
    10e8:	00b71733          	sll	a4,a4,a1
    10ec:	fff74713          	not	a4,a4
    10f0:	00677333          	and	t1,a4,t1
    10f4:	00b295b3          	sll	a1,t0,a1
    10f8:	0065e333          	or	t1,a1,t1
    10fc:	00869d63          	bne	a3,s0,1116 <GPIO_Init+0x72>
    1100:	c950                	sw	a2,20(a0)
    1102:	0785                	addi	a5,a5,1
    1104:	4721                	li	a4,8
    1106:	fce797e3          	bne	a5,a4,10d4 <GPIO_Init+0x30>
    110a:	4412                	lw	s0,4(sp)
    110c:	00652023          	sw	t1,0(a0)
    1110:	4482                	lw	s1,0(sp)
    1112:	0121                	addi	sp,sp,8
    1114:	8082                	ret
    1116:	fe9696e3          	bne	a3,s1,1102 <GPIO_Init+0x5e>
    111a:	c910                	sw	a2,16(a0)
    111c:	b7dd                	j	1102 <GPIO_Init+0x5e>
    111e:	8082                	ret

00001120 <GPIO_ReadInputDataBit>:
    1120:	4508                	lw	a0,8(a0)
    1122:	8d6d                	and	a0,a0,a1
    1124:	00a03533          	snez	a0,a0
    1128:	8082                	ret

0000112a <GPIO_EXTILineConfig>:
    112a:	40010737          	lui	a4,0x40010
    112e:	4714                	lw	a3,8(a4)
    1130:	0586                	slli	a1,a1,0x1
    1132:	478d                	li	a5,3
    1134:	00b797b3          	sll	a5,a5,a1
    1138:	fff7c793          	not	a5,a5
    113c:	8ff5                	and	a5,a5,a3
    113e:	c71c                	sw	a5,8(a4)
    1140:	471c                	lw	a5,8(a4)
    1142:	00b515b3          	sll	a1,a0,a1
    1146:	8ddd                	or	a1,a1,a5
    1148:	c70c                	sw	a1,8(a4)
    114a:	8082                	ret

0000114c <NVIC_PriorityGroupConfig>:
    114c:	98a1a623          	sw	a0,-1652(gp) # 200001cc <NVIC_Priority_Group>
    1150:	8082                	ret

00001152 <NVIC_Init>:
    1152:	98c1a683          	lw	a3,-1652(gp) # 200001cc <NVIC_Priority_Group>
    1156:	4785                	li	a5,1
    1158:	2118                	lbu	a4,0(a0)
    115a:	02f69063          	bne	a3,a5,117a <NVIC_Init+0x28>
    115e:	311c                	lbu	a5,1(a0)
    1160:	02d79c63          	bne	a5,a3,1198 <NVIC_Init+0x46>
    1164:	213c                	lbu	a5,2(a0)
    1166:	079a                	slli	a5,a5,0x6
    1168:	f807e793          	ori	a5,a5,-128
    116c:	e000e6b7          	lui	a3,0xe000e
    1170:	0ff7f793          	andi	a5,a5,255
    1174:	96ba                	add	a3,a3,a4
    1176:	40f68023          	sb	a5,1024(a3) # e000e400 <__global_pointer$+0xc000dbc0>
    117a:	4685                	li	a3,1
    117c:	00575793          	srli	a5,a4,0x5
    1180:	00e69733          	sll	a4,a3,a4
    1184:	4154                	lw	a3,4(a0)
    1186:	ce89                	beqz	a3,11a0 <NVIC_Init+0x4e>
    1188:	04078793          	addi	a5,a5,64
    118c:	078a                	slli	a5,a5,0x2
    118e:	e000e6b7          	lui	a3,0xe000e
    1192:	97b6                	add	a5,a5,a3
    1194:	c398                	sw	a4,0(a5)
    1196:	8082                	ret
    1198:	f3ed                	bnez	a5,117a <NVIC_Init+0x28>
    119a:	213c                	lbu	a5,2(a0)
    119c:	079a                	slli	a5,a5,0x6
    119e:	b7f9                	j	116c <NVIC_Init+0x1a>
    11a0:	06078793          	addi	a5,a5,96
    11a4:	e000e6b7          	lui	a3,0xe000e
    11a8:	078a                	slli	a5,a5,0x2
    11aa:	97b6                	add	a5,a5,a3
    11ac:	c398                	sw	a4,0(a5)
    11ae:	0000100f          	fence.i
    11b2:	8082                	ret

000011b4 <RCC_AdjustHSICalibrationValue>:
    11b4:	40021737          	lui	a4,0x40021
    11b8:	431c                	lw	a5,0(a4)
    11ba:	050e                	slli	a0,a0,0x3
    11bc:	f077f793          	andi	a5,a5,-249
    11c0:	8d5d                	or	a0,a0,a5
    11c2:	c308                	sw	a0,0(a4)
    11c4:	8082                	ret

000011c6 <RCC_GetClocksFreq>:
    11c6:	edbfe2ef          	jal	t0,a0 <__riscv_save_0>
    11ca:	40021737          	lui	a4,0x40021
    11ce:	435c                	lw	a5,4(a4)
    11d0:	4691                	li	a3,4
    11d2:	842a                	mv	s0,a0
    11d4:	8bb1                	andi	a5,a5,12
    11d6:	00d78563          	beq	a5,a3,11e0 <RCC_GetClocksFreq+0x1a>
    11da:	46a1                	li	a3,8
    11dc:	08d78063          	beq	a5,a3,125c <RCC_GetClocksFreq+0x96>
    11e0:	016e37b7          	lui	a5,0x16e3
    11e4:	60078793          	addi	a5,a5,1536 # 16e3600 <_data_lma+0x16e1220>
    11e8:	c01c                	sw	a5,0(s0)
    11ea:	400216b7          	lui	a3,0x40021
    11ee:	42dc                	lw	a5,4(a3)
    11f0:	8391                	srli	a5,a5,0x4
    11f2:	00f7f713          	andi	a4,a5,15
    11f6:	200007b7          	lui	a5,0x20000
    11fa:	02c78793          	addi	a5,a5,44 # 2000002c <APBAHBPrescTable>
    11fe:	97ba                	add	a5,a5,a4
    1200:	238c                	lbu	a1,0(a5)
    1202:	42dc                	lw	a5,4(a3)
    1204:	4018                	lw	a4,0(s0)
    1206:	0ff5f593          	andi	a1,a1,255
    120a:	0807f793          	andi	a5,a5,128
    120e:	00b75533          	srl	a0,a4,a1
    1212:	e781                	bnez	a5,121a <RCC_GetClocksFreq+0x54>
    1214:	853a                	mv	a0,a4
    1216:	ebffe0ef          	jal	ra,d4 <__udivsi3>
    121a:	c048                	sw	a0,4(s0)
    121c:	c408                	sw	a0,8(s0)
    121e:	c448                	sw	a0,12(s0)
    1220:	400217b7          	lui	a5,0x40021
    1224:	43dc                	lw	a5,4(a5)
    1226:	468d                	li	a3,3
    1228:	83ad                	srli	a5,a5,0xb
    122a:	8bfd                	andi	a5,a5,31
    122c:	0037d713          	srli	a4,a5,0x3
    1230:	078a                	slli	a5,a5,0x2
    1232:	8bf1                	andi	a5,a5,28
    1234:	8fd9                	or	a5,a5,a4
    1236:	0137f613          	andi	a2,a5,19
    123a:	0037f713          	andi	a4,a5,3
    123e:	00c6f463          	bgeu	a3,a2,1246 <RCC_GetClocksFreq+0x80>
    1242:	ff478713          	addi	a4,a5,-12 # 40020ff4 <__global_pointer$+0x200207b4>
    1246:	200007b7          	lui	a5,0x20000
    124a:	01878793          	addi	a5,a5,24 # 20000018 <ADCPrescTable>
    124e:	97ba                	add	a5,a5,a4
    1250:	238c                	lbu	a1,0(a5)
    1252:	e83fe0ef          	jal	ra,d4 <__udivsi3>
    1256:	c808                	sw	a0,16(s0)
    1258:	e53fe06f          	j	aa <__riscv_restore_0>
    125c:	435c                	lw	a5,4(a4)
    125e:	02dc77b7          	lui	a5,0x2dc7
    1262:	c0078793          	addi	a5,a5,-1024 # 2dc6c00 <_data_lma+0x2dc4820>
    1266:	b749                	j	11e8 <RCC_GetClocksFreq+0x22>

00001268 <RCC_APB2PeriphClockCmd>:
    1268:	c599                	beqz	a1,1276 <RCC_APB2PeriphClockCmd+0xe>
    126a:	40021737          	lui	a4,0x40021
    126e:	4f1c                	lw	a5,24(a4)
    1270:	8d5d                	or	a0,a0,a5
    1272:	cf08                	sw	a0,24(a4)
    1274:	8082                	ret
    1276:	400217b7          	lui	a5,0x40021
    127a:	4f98                	lw	a4,24(a5)
    127c:	fff54513          	not	a0,a0
    1280:	8d79                	and	a0,a0,a4
    1282:	cf88                	sw	a0,24(a5)
    1284:	8082                	ret

00001286 <RCC_APB1PeriphClockCmd>:
    1286:	c599                	beqz	a1,1294 <RCC_APB1PeriphClockCmd+0xe>
    1288:	40021737          	lui	a4,0x40021
    128c:	4f5c                	lw	a5,28(a4)
    128e:	8d5d                	or	a0,a0,a5
    1290:	cf48                	sw	a0,28(a4)
    1292:	8082                	ret
    1294:	400217b7          	lui	a5,0x40021
    1298:	4fd8                	lw	a4,28(a5)
    129a:	fff54513          	not	a0,a0
    129e:	8d79                	and	a0,a0,a4
    12a0:	cfc8                	sw	a0,28(a5)
    12a2:	8082                	ret

000012a4 <TIM_TimeBaseInit>:
    12a4:	211e                	lhu	a5,0(a0)
    12a6:	40013737          	lui	a4,0x40013
    12aa:	c0070713          	addi	a4,a4,-1024 # 40012c00 <__global_pointer$+0x200123c0>
    12ae:	07c2                	slli	a5,a5,0x10
    12b0:	83c1                	srli	a5,a5,0x10
    12b2:	00e50663          	beq	a0,a4,12be <TIM_TimeBaseInit+0x1a>
    12b6:	40000737          	lui	a4,0x40000
    12ba:	00e51663          	bne	a0,a4,12c6 <TIM_TimeBaseInit+0x22>
    12be:	21ba                	lhu	a4,2(a1)
    12c0:	f8f7f793          	andi	a5,a5,-113
    12c4:	8fd9                	or	a5,a5,a4
    12c6:	21fa                	lhu	a4,6(a1)
    12c8:	cff7f793          	andi	a5,a5,-769
    12cc:	07c2                	slli	a5,a5,0x10
    12ce:	83c1                	srli	a5,a5,0x10
    12d0:	8fd9                	or	a5,a5,a4
    12d2:	a11e                	sh	a5,0(a0)
    12d4:	21de                	lhu	a5,4(a1)
    12d6:	b55e                	sh	a5,44(a0)
    12d8:	219e                	lhu	a5,0(a1)
    12da:	b51e                	sh	a5,40(a0)
    12dc:	400137b7          	lui	a5,0x40013
    12e0:	c0078793          	addi	a5,a5,-1024 # 40012c00 <__global_pointer$+0x200123c0>
    12e4:	00f51463          	bne	a0,a5,12ec <TIM_TimeBaseInit+0x48>
    12e8:	259c                	lbu	a5,8(a1)
    12ea:	b91e                	sh	a5,48(a0)
    12ec:	4785                	li	a5,1
    12ee:	a95e                	sh	a5,20(a0)
    12f0:	8082                	ret

000012f2 <TIM_OC1Init>:
    12f2:	311e                	lhu	a5,32(a0)
    12f4:	2192                	lhu	a2,0(a1)
    12f6:	0025d303          	lhu	t1,2(a1)
    12fa:	07c2                	slli	a5,a5,0x10
    12fc:	83c1                	srli	a5,a5,0x10
    12fe:	9bf9                	andi	a5,a5,-2
    1300:	07c2                	slli	a5,a5,0x10
    1302:	83c1                	srli	a5,a5,0x10
    1304:	b11e                	sh	a5,32(a0)
    1306:	311e                	lhu	a5,32(a0)
    1308:	2156                	lhu	a3,4(a0)
    130a:	2d1a                	lhu	a4,24(a0)
    130c:	07c2                	slli	a5,a5,0x10
    130e:	83c1                	srli	a5,a5,0x10
    1310:	0742                	slli	a4,a4,0x10
    1312:	8341                	srli	a4,a4,0x10
    1314:	f8c77713          	andi	a4,a4,-116
    1318:	8f51                	or	a4,a4,a2
    131a:	2592                	lhu	a2,8(a1)
    131c:	9bf5                	andi	a5,a5,-3
    131e:	06c2                	slli	a3,a3,0x10
    1320:	00666633          	or	a2,a2,t1
    1324:	8fd1                	or	a5,a5,a2
    1326:	40013637          	lui	a2,0x40013
    132a:	c0060613          	addi	a2,a2,-1024 # 40012c00 <__global_pointer$+0x200123c0>
    132e:	82c1                	srli	a3,a3,0x10
    1330:	02c51063          	bne	a0,a2,1350 <TIM_OC1Init+0x5e>
    1334:	25b2                	lhu	a2,10(a1)
    1336:	9bdd                	andi	a5,a5,-9
    1338:	00e5d303          	lhu	t1,14(a1)
    133c:	8fd1                	or	a5,a5,a2
    133e:	21d2                	lhu	a2,4(a1)
    1340:	9bed                	andi	a5,a5,-5
    1342:	cff6f693          	andi	a3,a3,-769
    1346:	8fd1                	or	a5,a5,a2
    1348:	25d2                	lhu	a2,12(a1)
    134a:	00666633          	or	a2,a2,t1
    134e:	8ed1                	or	a3,a3,a2
    1350:	a156                	sh	a3,4(a0)
    1352:	ad1a                	sh	a4,24(a0)
    1354:	21fa                	lhu	a4,6(a1)
    1356:	d958                	sw	a4,52(a0)
    1358:	b11e                	sh	a5,32(a0)
    135a:	8082                	ret

0000135c <TIM_Cmd>:
    135c:	211e                	lhu	a5,0(a0)
    135e:	c589                	beqz	a1,1368 <TIM_Cmd+0xc>
    1360:	0017e793          	ori	a5,a5,1
    1364:	a11e                	sh	a5,0(a0)
    1366:	8082                	ret
    1368:	07c2                	slli	a5,a5,0x10
    136a:	83c1                	srli	a5,a5,0x10
    136c:	9bf9                	andi	a5,a5,-2
    136e:	07c2                	slli	a5,a5,0x10
    1370:	83c1                	srli	a5,a5,0x10
    1372:	bfcd                	j	1364 <TIM_Cmd+0x8>

00001374 <TIM_CtrlPWMOutputs>:
    1374:	04455783          	lhu	a5,68(a0)
    1378:	c591                	beqz	a1,1384 <TIM_CtrlPWMOutputs+0x10>
    137a:	6721                	lui	a4,0x8
    137c:	8fd9                	or	a5,a5,a4
    137e:	04f51223          	sh	a5,68(a0)
    1382:	8082                	ret
    1384:	07c6                	slli	a5,a5,0x11
    1386:	83c5                	srli	a5,a5,0x11
    1388:	bfdd                	j	137e <TIM_CtrlPWMOutputs+0xa>

0000138a <TIM_ITConfig>:
    138a:	255e                	lhu	a5,12(a0)
    138c:	c601                	beqz	a2,1394 <TIM_ITConfig+0xa>
    138e:	8ddd                	or	a1,a1,a5
    1390:	a54e                	sh	a1,12(a0)
    1392:	8082                	ret
    1394:	fff5c593          	not	a1,a1
    1398:	8dfd                	and	a1,a1,a5
    139a:	bfdd                	j	1390 <TIM_ITConfig+0x6>

0000139c <TIM_ARRPreloadConfig>:
    139c:	211e                	lhu	a5,0(a0)
    139e:	c589                	beqz	a1,13a8 <TIM_ARRPreloadConfig+0xc>
    13a0:	0807e793          	ori	a5,a5,128
    13a4:	a11e                	sh	a5,0(a0)
    13a6:	8082                	ret
    13a8:	07c2                	slli	a5,a5,0x10
    13aa:	83c1                	srli	a5,a5,0x10
    13ac:	f7f7f793          	andi	a5,a5,-129
    13b0:	07c2                	slli	a5,a5,0x10
    13b2:	83c1                	srli	a5,a5,0x10
    13b4:	bfc5                	j	13a4 <TIM_ARRPreloadConfig+0x8>

000013b6 <TIM_OC1PreloadConfig>:
    13b6:	2d1e                	lhu	a5,24(a0)
    13b8:	07c2                	slli	a5,a5,0x10
    13ba:	83c1                	srli	a5,a5,0x10
    13bc:	9bdd                	andi	a5,a5,-9
    13be:	8ddd                	or	a1,a1,a5
    13c0:	ad0e                	sh	a1,24(a0)
    13c2:	8082                	ret

000013c4 <TIM_SetCompare1>:
    13c4:	d94c                	sw	a1,52(a0)
    13c6:	8082                	ret

000013c8 <TIM_GetITStatus>:
    13c8:	291e                	lhu	a5,16(a0)
    13ca:	254a                	lhu	a0,12(a0)
    13cc:	8fed                	and	a5,a5,a1
    13ce:	0542                	slli	a0,a0,0x10
    13d0:	8141                	srli	a0,a0,0x10
    13d2:	c789                	beqz	a5,13dc <TIM_GetITStatus+0x14>
    13d4:	8d6d                	and	a0,a0,a1
    13d6:	00a03533          	snez	a0,a0
    13da:	8082                	ret
    13dc:	4501                	li	a0,0
    13de:	8082                	ret

000013e0 <TIM_ClearITPendingBit>:
    13e0:	fff5c593          	not	a1,a1
    13e4:	05c2                	slli	a1,a1,0x10
    13e6:	81c1                	srli	a1,a1,0x10
    13e8:	a90e                	sh	a1,16(a0)
    13ea:	8082                	ret

000013ec <USART_Init>:
    13ec:	cb5fe2ef          	jal	t0,a0 <__riscv_save_0>
    13f0:	2916                	lhu	a3,16(a0)
    13f2:	77f5                	lui	a5,0xffffd
    13f4:	17fd                	addi	a5,a5,-1
    13f6:	8ff5                	and	a5,a5,a3
    13f8:	21f6                	lhu	a3,6(a1)
    13fa:	25da                	lhu	a4,12(a1)
    13fc:	1121                	addi	sp,sp,-24
    13fe:	8fd5                	or	a5,a5,a3
    1400:	a91e                	sh	a5,16(a0)
    1402:	2556                	lhu	a3,12(a0)
    1404:	77fd                	lui	a5,0xfffff
    1406:	9f378793          	addi	a5,a5,-1549 # ffffe9f3 <__global_pointer$+0xdfffe1b3>
    140a:	8ff5                	and	a5,a5,a3
    140c:	21d6                	lhu	a3,4(a1)
    140e:	842a                	mv	s0,a0
    1410:	c02e                	sw	a1,0(sp)
    1412:	8fd5                	or	a5,a5,a3
    1414:	2596                	lhu	a3,8(a1)
    1416:	8fd5                	or	a5,a5,a3
    1418:	25b6                	lhu	a3,10(a1)
    141a:	8fd5                	or	a5,a5,a3
    141c:	a55e                	sh	a5,12(a0)
    141e:	295e                	lhu	a5,20(a0)
    1420:	07c2                	slli	a5,a5,0x10
    1422:	83c1                	srli	a5,a5,0x10
    1424:	cff7f793          	andi	a5,a5,-769
    1428:	8f5d                	or	a4,a4,a5
    142a:	a95a                	sh	a4,20(a0)
    142c:	0048                	addi	a0,sp,4
    142e:	3b61                	jal	11c6 <RCC_GetClocksFreq>
    1430:	400147b7          	lui	a5,0x40014
    1434:	80078793          	addi	a5,a5,-2048 # 40013800 <__global_pointer$+0x20012fc0>
    1438:	4582                	lw	a1,0(sp)
    143a:	06f41263          	bne	s0,a5,149e <USART_Init+0xb2>
    143e:	47c2                	lw	a5,16(sp)
    1440:	245a                	lhu	a4,12(s0)
    1442:	00179513          	slli	a0,a5,0x1
    1446:	953e                	add	a0,a0,a5
    1448:	0742                	slli	a4,a4,0x10
    144a:	050e                	slli	a0,a0,0x3
    144c:	8741                	srai	a4,a4,0x10
    144e:	953e                	add	a0,a0,a5
    1450:	418c                	lw	a1,0(a1)
    1452:	04075863          	bgez	a4,14a2 <USART_Init+0xb6>
    1456:	0586                	slli	a1,a1,0x1
    1458:	c7dfe0ef          	jal	ra,d4 <__udivsi3>
    145c:	06400593          	li	a1,100
    1460:	c02a                	sw	a0,0(sp)
    1462:	c73fe0ef          	jal	ra,d4 <__udivsi3>
    1466:	4782                	lw	a5,0(sp)
    1468:	00451493          	slli	s1,a0,0x4
    146c:	06400593          	li	a1,100
    1470:	853e                	mv	a0,a5
    1472:	c8ffe0ef          	jal	ra,100 <__umodsi3>
    1476:	245e                	lhu	a5,12(s0)
    1478:	07c2                	slli	a5,a5,0x10
    147a:	87c1                	srai	a5,a5,0x10
    147c:	0207d563          	bgez	a5,14a6 <USART_Init+0xba>
    1480:	050e                	slli	a0,a0,0x3
    1482:	06400593          	li	a1,100
    1486:	03250513          	addi	a0,a0,50
    148a:	c4bfe0ef          	jal	ra,d4 <__udivsi3>
    148e:	891d                	andi	a0,a0,7
    1490:	8cc9                	or	s1,s1,a0
    1492:	04c2                	slli	s1,s1,0x10
    1494:	80c1                	srli	s1,s1,0x10
    1496:	a406                	sh	s1,8(s0)
    1498:	0161                	addi	sp,sp,24
    149a:	c11fe06f          	j	aa <__riscv_restore_0>
    149e:	47b2                	lw	a5,12(sp)
    14a0:	b745                	j	1440 <USART_Init+0x54>
    14a2:	058a                	slli	a1,a1,0x2
    14a4:	bf55                	j	1458 <USART_Init+0x6c>
    14a6:	0512                	slli	a0,a0,0x4
    14a8:	06400593          	li	a1,100
    14ac:	03250513          	addi	a0,a0,50
    14b0:	c25fe0ef          	jal	ra,d4 <__udivsi3>
    14b4:	893d                	andi	a0,a0,15
    14b6:	bfe9                	j	1490 <USART_Init+0xa4>

000014b8 <USART_Cmd>:
    14b8:	c591                	beqz	a1,14c4 <USART_Cmd+0xc>
    14ba:	255e                	lhu	a5,12(a0)
    14bc:	6709                	lui	a4,0x2
    14be:	8fd9                	or	a5,a5,a4
    14c0:	a55e                	sh	a5,12(a0)
    14c2:	8082                	ret
    14c4:	255a                	lhu	a4,12(a0)
    14c6:	77f9                	lui	a5,0xffffe
    14c8:	17fd                	addi	a5,a5,-1
    14ca:	8ff9                	and	a5,a5,a4
    14cc:	bfd5                	j	14c0 <USART_Cmd+0x8>

000014ce <USART_SendData>:
    14ce:	1ff5f593          	andi	a1,a1,511
    14d2:	a14e                	sh	a1,4(a0)
    14d4:	8082                	ret

000014d6 <USART_GetFlagStatus>:
    14d6:	210a                	lhu	a0,0(a0)
    14d8:	8d6d                	and	a0,a0,a1
    14da:	00a03533          	snez	a0,a0
    14de:	8082                	ret

000014e0 <Delay_Init>:
    14e0:	bc1fe2ef          	jal	t0,a0 <__riscv_save_0>
    14e4:	200007b7          	lui	a5,0x20000
    14e8:	0147a503          	lw	a0,20(a5) # 20000014 <SystemCoreClock>
    14ec:	007a15b7          	lui	a1,0x7a1
    14f0:	20058593          	addi	a1,a1,512 # 7a1200 <_data_lma+0x79ee20>
    14f4:	be1fe0ef          	jal	ra,d4 <__udivsi3>
    14f8:	0ff57513          	andi	a0,a0,255
    14fc:	98a18923          	sb	a0,-1646(gp) # 200001d2 <p_us>
    1500:	00551793          	slli	a5,a0,0x5
    1504:	8f89                	sub	a5,a5,a0
    1506:	078a                	slli	a5,a5,0x2
    1508:	953e                	add	a0,a0,a5
    150a:	050e                	slli	a0,a0,0x3
    150c:	98a19823          	sh	a0,-1648(gp) # 200001d0 <p_ms>
    1510:	b9bfe06f          	j	aa <__riscv_restore_0>

00001514 <Delay_Ms>:
    1514:	b8dfe2ef          	jal	t0,a0 <__riscv_save_0>
    1518:	e000f437          	lui	s0,0xe000f
    151c:	405c                	lw	a5,4(s0)
    151e:	85aa                	mv	a1,a0
    1520:	9bf9                	andi	a5,a5,-2
    1522:	c05c                	sw	a5,4(s0)
    1524:	9901d503          	lhu	a0,-1648(gp) # 200001d0 <p_ms>
    1528:	b8dfe0ef          	jal	ra,b4 <__mulsi3>
    152c:	c808                	sw	a0,16(s0)
    152e:	00042423          	sw	zero,8(s0) # e000f008 <__global_pointer$+0xc000e7c8>
    1532:	401c                	lw	a5,0(s0)
    1534:	0017e793          	ori	a5,a5,1
    1538:	c01c                	sw	a5,0(s0)
    153a:	e000f7b7          	lui	a5,0xe000f
    153e:	43d8                	lw	a4,4(a5)
    1540:	8b05                	andi	a4,a4,1
    1542:	df75                	beqz	a4,153e <Delay_Ms+0x2a>
    1544:	4398                	lw	a4,0(a5)
    1546:	9b79                	andi	a4,a4,-2
    1548:	c398                	sw	a4,0(a5)
    154a:	b61fe06f          	j	aa <__riscv_restore_0>

0000154e <USART_Printf_Init>:
    154e:	b53fe2ef          	jal	t0,a0 <__riscv_save_0>
    1552:	842a                	mv	s0,a0
    1554:	6511                	lui	a0,0x4
    1556:	1111                	addi	sp,sp,-28
    1558:	4585                	li	a1,1
    155a:	02050513          	addi	a0,a0,32 # 4020 <_data_lma+0x1c40>
    155e:	d0bff0ef          	jal	ra,1268 <RCC_APB2PeriphClockCmd>
    1562:	02000793          	li	a5,32
    1566:	807c                	sh	a5,0(sp)
    1568:	40011537          	lui	a0,0x40011
    156c:	478d                	li	a5,3
    156e:	c23e                	sw	a5,4(sp)
    1570:	858a                	mv	a1,sp
    1572:	47e1                	li	a5,24
    1574:	40050513          	addi	a0,a0,1024 # 40011400 <__global_pointer$+0x20010bc0>
    1578:	c43e                	sw	a5,8(sp)
    157a:	b2bff0ef          	jal	ra,10a4 <GPIO_Init>
    157e:	c622                	sw	s0,12(sp)
    1580:	40014437          	lui	s0,0x40014
    1584:	000807b7          	lui	a5,0x80
    1588:	006c                	addi	a1,sp,12
    158a:	80040513          	addi	a0,s0,-2048 # 40013800 <__global_pointer$+0x20012fc0>
    158e:	ca3e                	sw	a5,20(sp)
    1590:	c802                	sw	zero,16(sp)
    1592:	00011c23          	sh	zero,24(sp)
    1596:	3d99                	jal	13ec <USART_Init>
    1598:	4585                	li	a1,1
    159a:	80040513          	addi	a0,s0,-2048
    159e:	3f29                	jal	14b8 <USART_Cmd>
    15a0:	0171                	addi	sp,sp,28
    15a2:	b09fe06f          	j	aa <__riscv_restore_0>

000015a6 <_write>:
    15a6:	afbfe2ef          	jal	t0,a0 <__riscv_save_0>
    15aa:	1171                	addi	sp,sp,-4
    15ac:	84ae                	mv	s1,a1
    15ae:	4401                	li	s0,0
    15b0:	02c45d63          	bge	s0,a2,15ea <_write+0x44>
    15b4:	400147b7          	lui	a5,0x40014
    15b8:	80078713          	addi	a4,a5,-2048 # 40013800 <__global_pointer$+0x20012fc0>
    15bc:	853a                	mv	a0,a4
    15be:	04000593          	li	a1,64
    15c2:	c032                	sw	a2,0(sp)
    15c4:	3f09                	jal	14d6 <USART_GetFlagStatus>
    15c6:	400147b7          	lui	a5,0x40014
    15ca:	80078713          	addi	a4,a5,-2048 # 40013800 <__global_pointer$+0x20012fc0>
    15ce:	4602                	lw	a2,0(sp)
    15d0:	d575                	beqz	a0,15bc <_write+0x16>
    15d2:	00848733          	add	a4,s1,s0
    15d6:	00070583          	lb	a1,0(a4) # 2000 <font+0x128>
    15da:	80078513          	addi	a0,a5,-2048
    15de:	0405                	addi	s0,s0,1
    15e0:	05c2                	slli	a1,a1,0x10
    15e2:	81c1                	srli	a1,a1,0x10
    15e4:	35ed                	jal	14ce <USART_SendData>
    15e6:	4602                	lw	a2,0(sp)
    15e8:	b7e1                	j	15b0 <_write+0xa>
    15ea:	8532                	mv	a0,a2
    15ec:	0111                	addi	sp,sp,4
    15ee:	abdfe06f          	j	aa <__riscv_restore_0>

000015f2 <printchar>:
    15f2:	1141                	addi	sp,sp,-16
    15f4:	c606                	sw	ra,12(sp)
    15f6:	c02e                	sw	a1,0(sp)
    15f8:	cd0d                	beqz	a0,1632 <printchar+0x40>
    15fa:	4118                	lw	a4,0(a0)
    15fc:	87aa                	mv	a5,a0
    15fe:	c305                	beqz	a4,161e <printchar+0x2c>
    1600:	4158                	lw	a4,4(a0)
    1602:	557d                	li	a0,-1
    1604:	cb11                	beqz	a4,1618 <printchar+0x26>
    1606:	4685                	li	a3,1
    1608:	00d71b63          	bne	a4,a3,161e <printchar+0x2c>
    160c:	4798                	lw	a4,8(a5)
    160e:	00070023          	sb	zero,0(a4)
    1612:	0007a223          	sw	zero,4(a5)
    1616:	4505                	li	a0,1
    1618:	40b2                	lw	ra,12(sp)
    161a:	0141                	addi	sp,sp,16
    161c:	8082                	ret
    161e:	4798                	lw	a4,8(a5)
    1620:	4682                	lw	a3,0(sp)
    1622:	a314                	sb	a3,0(a4)
    1624:	4798                	lw	a4,8(a5)
    1626:	0705                	addi	a4,a4,1
    1628:	c798                	sw	a4,8(a5)
    162a:	43d8                	lw	a4,4(a5)
    162c:	177d                	addi	a4,a4,-1
    162e:	c3d8                	sw	a4,4(a5)
    1630:	b7dd                	j	1616 <printchar+0x24>
    1632:	4605                	li	a2,1
    1634:	858a                	mv	a1,sp
    1636:	3f85                	jal	15a6 <_write>
    1638:	bff9                	j	1616 <printchar+0x24>

0000163a <prints>:
    163a:	1101                	addi	sp,sp,-32
    163c:	cc22                	sw	s0,24(sp)
    163e:	c22e                	sw	a1,4(sp)
    1640:	ce06                	sw	ra,28(sp)
    1642:	ca26                	sw	s1,20(sp)
    1644:	842a                	mv	s0,a0
    1646:	4781                	li	a5,0
    1648:	02000593          	li	a1,32
    164c:	02064563          	bltz	a2,1676 <prints+0x3c>
    1650:	4592                	lw	a1,4(sp)
    1652:	95be                	add	a1,a1,a5
    1654:	00058583          	lb	a1,0(a1)
    1658:	e58d                	bnez	a1,1682 <prints+0x48>
    165a:	02c7d863          	bge	a5,a2,168a <prints+0x50>
    165e:	02e7d463          	bge	a5,a4,1686 <prints+0x4c>
    1662:	8e19                	sub	a2,a2,a4
    1664:	02000513          	li	a0,32
    1668:	0026f593          	andi	a1,a3,2
    166c:	c02a                	sw	a0,0(sp)
    166e:	c589                	beqz	a1,1678 <prints+0x3e>
    1670:	e701                	bnez	a4,1678 <prints+0x3e>
    1672:	03000593          	li	a1,48
    1676:	c02e                	sw	a1,0(sp)
    1678:	8a85                	andi	a3,a3,1
    167a:	4481                	li	s1,0
    167c:	ea95                	bnez	a3,16b0 <prints+0x76>
    167e:	84b2                	mv	s1,a2
    1680:	a00d                	j	16a2 <prints+0x68>
    1682:	0785                	addi	a5,a5,1
    1684:	b7f1                	j	1650 <prints+0x16>
    1686:	8e1d                	sub	a2,a2,a5
    1688:	bff1                	j	1664 <prints+0x2a>
    168a:	4601                	li	a2,0
    168c:	bfe1                	j	1664 <prints+0x2a>
    168e:	4582                	lw	a1,0(sp)
    1690:	8522                	mv	a0,s0
    1692:	c83a                	sw	a4,16(sp)
    1694:	c632                	sw	a2,12(sp)
    1696:	c43e                	sw	a5,8(sp)
    1698:	3fa9                	jal	15f2 <printchar>
    169a:	47a2                	lw	a5,8(sp)
    169c:	4632                	lw	a2,12(sp)
    169e:	4742                	lw	a4,16(sp)
    16a0:	14fd                	addi	s1,s1,-1
    16a2:	fe9046e3          	bgtz	s1,168e <prints+0x54>
    16a6:	84b2                	mv	s1,a2
    16a8:	00065363          	bgez	a2,16ae <prints+0x74>
    16ac:	4481                	li	s1,0
    16ae:	8e05                	sub	a2,a2,s1
    16b0:	02e7c763          	blt	a5,a4,16de <prints+0xa4>
    16b4:	87a6                	mv	a5,s1
    16b6:	4692                	lw	a3,4(sp)
    16b8:	40978733          	sub	a4,a5,s1
    16bc:	9736                	add	a4,a4,a3
    16be:	00070583          	lb	a1,0(a4)
    16c2:	ed95                	bnez	a1,16fe <prints+0xc4>
    16c4:	84b2                	mv	s1,a2
    16c6:	04904463          	bgtz	s1,170e <prints+0xd4>
    16ca:	00065363          	bgez	a2,16d0 <prints+0x96>
    16ce:	4601                	li	a2,0
    16d0:	40f2                	lw	ra,28(sp)
    16d2:	4462                	lw	s0,24(sp)
    16d4:	44d2                	lw	s1,20(sp)
    16d6:	00f60533          	add	a0,a2,a5
    16da:	6105                	addi	sp,sp,32
    16dc:	8082                	ret
    16de:	8f1d                	sub	a4,a4,a5
    16e0:	87ba                	mv	a5,a4
    16e2:	03000593          	li	a1,48
    16e6:	8522                	mv	a0,s0
    16e8:	c832                	sw	a2,16(sp)
    16ea:	c63e                	sw	a5,12(sp)
    16ec:	c43a                	sw	a4,8(sp)
    16ee:	3711                	jal	15f2 <printchar>
    16f0:	47b2                	lw	a5,12(sp)
    16f2:	4722                	lw	a4,8(sp)
    16f4:	4642                	lw	a2,16(sp)
    16f6:	17fd                	addi	a5,a5,-1
    16f8:	f7ed                	bnez	a5,16e2 <prints+0xa8>
    16fa:	94ba                	add	s1,s1,a4
    16fc:	bf65                	j	16b4 <prints+0x7a>
    16fe:	8522                	mv	a0,s0
    1700:	c632                	sw	a2,12(sp)
    1702:	c43e                	sw	a5,8(sp)
    1704:	35fd                	jal	15f2 <printchar>
    1706:	47a2                	lw	a5,8(sp)
    1708:	4632                	lw	a2,12(sp)
    170a:	0785                	addi	a5,a5,1
    170c:	b76d                	j	16b6 <prints+0x7c>
    170e:	4582                	lw	a1,0(sp)
    1710:	8522                	mv	a0,s0
    1712:	c432                	sw	a2,8(sp)
    1714:	c23e                	sw	a5,4(sp)
    1716:	3df1                	jal	15f2 <printchar>
    1718:	14fd                	addi	s1,s1,-1
    171a:	4622                	lw	a2,8(sp)
    171c:	4792                	lw	a5,4(sp)
    171e:	b765                	j	16c6 <prints+0x8c>

00001720 <printInt>:
    1720:	7139                	addi	sp,sp,-64
    1722:	de06                	sw	ra,60(sp)
    1724:	dc22                	sw	s0,56(sp)
    1726:	da26                	sw	s1,52(sp)
    1728:	c23e                	sw	a5,4(sp)
    172a:	8332                	mv	t1,a2
    172c:	863a                	mv	a2,a4
    172e:	ed89                	bnez	a1,1748 <printInt+0x28>
    1730:	4692                	lw	a3,4(sp)
    1732:	03000793          	li	a5,48
    1736:	4701                	li	a4,0
    1738:	086c                	addi	a1,sp,28
    173a:	86fc                	sh	a5,28(sp)
    173c:	3dfd                	jal	163a <prints>
    173e:	50f2                	lw	ra,60(sp)
    1740:	5462                	lw	s0,56(sp)
    1742:	54d2                	lw	s1,52(sp)
    1744:	6121                	addi	sp,sp,64
    1746:	8082                	ret
    1748:	84aa                	mv	s1,a0
    174a:	8436                	mv	s0,a3
    174c:	87ae                	mv	a5,a1
    174e:	ca91                	beqz	a3,1762 <printInt+0x42>
    1750:	4729                	li	a4,10
    1752:	4401                	li	s0,0
    1754:	00e31763          	bne	t1,a4,1762 <printInt+0x42>
    1758:	0005d563          	bgez	a1,1762 <printInt+0x42>
    175c:	40b007b3          	neg	a5,a1
    1760:	4405                	li	s0,1
    1762:	4686                	lw	a3,64(sp)
    1764:	020109a3          	sb	zero,51(sp)
    1768:	03310713          	addi	a4,sp,51
    176c:	fc668693          	addi	a3,a3,-58 # 40020fc6 <__global_pointer$+0x20020786>
    1770:	c436                	sw	a3,8(sp)
    1772:	859a                	mv	a1,t1
    1774:	853e                	mv	a0,a5
    1776:	ca32                	sw	a2,20(sp)
    1778:	c83a                	sw	a4,16(sp)
    177a:	c61a                	sw	t1,12(sp)
    177c:	c03e                	sw	a5,0(sp)
    177e:	983fe0ef          	jal	ra,100 <__umodsi3>
    1782:	46a5                	li	a3,9
    1784:	4782                	lw	a5,0(sp)
    1786:	4332                	lw	t1,12(sp)
    1788:	4742                	lw	a4,16(sp)
    178a:	4652                	lw	a2,20(sp)
    178c:	00a6d463          	bge	a3,a0,1794 <printInt+0x74>
    1790:	46a2                	lw	a3,8(sp)
    1792:	9536                	add	a0,a0,a3
    1794:	03050513          	addi	a0,a0,48
    1798:	fff70693          	addi	a3,a4,-1
    179c:	fea70fa3          	sb	a0,-1(a4)
    17a0:	859a                	mv	a1,t1
    17a2:	853e                	mv	a0,a5
    17a4:	cc32                	sw	a2,24(sp)
    17a6:	ca3a                	sw	a4,20(sp)
    17a8:	c81a                	sw	t1,16(sp)
    17aa:	c63e                	sw	a5,12(sp)
    17ac:	c036                	sw	a3,0(sp)
    17ae:	927fe0ef          	jal	ra,d4 <__udivsi3>
    17b2:	47b2                	lw	a5,12(sp)
    17b4:	4342                	lw	t1,16(sp)
    17b6:	4752                	lw	a4,20(sp)
    17b8:	4662                	lw	a2,24(sp)
    17ba:	0467f963          	bgeu	a5,t1,180c <printInt+0xec>
    17be:	cc01                	beqz	s0,17d6 <printInt+0xb6>
    17c0:	ca29                	beqz	a2,1812 <printInt+0xf2>
    17c2:	4792                	lw	a5,4(sp)
    17c4:	8b89                	andi	a5,a5,2
    17c6:	c7b1                	beqz	a5,1812 <printInt+0xf2>
    17c8:	02d00593          	li	a1,45
    17cc:	8526                	mv	a0,s1
    17ce:	c432                	sw	a2,8(sp)
    17d0:	350d                	jal	15f2 <printchar>
    17d2:	4622                	lw	a2,8(sp)
    17d4:	167d                	addi	a2,a2,-1
    17d6:	4792                	lw	a5,4(sp)
    17d8:	8b91                	andi	a5,a5,4
    17da:	c395                	beqz	a5,17fe <printInt+0xde>
    17dc:	4706                	lw	a4,64(sp)
    17de:	06100793          	li	a5,97
    17e2:	c432                	sw	a2,8(sp)
    17e4:	03000593          	li	a1,48
    17e8:	8526                	mv	a0,s1
    17ea:	02f71e63          	bne	a4,a5,1826 <printInt+0x106>
    17ee:	3511                	jal	15f2 <printchar>
    17f0:	07800593          	li	a1,120
    17f4:	8526                	mv	a0,s1
    17f6:	3bf5                	jal	15f2 <printchar>
    17f8:	4622                	lw	a2,8(sp)
    17fa:	0409                	addi	s0,s0,2
    17fc:	1679                	addi	a2,a2,-2
    17fe:	4716                	lw	a4,68(sp)
    1800:	4692                	lw	a3,4(sp)
    1802:	4582                	lw	a1,0(sp)
    1804:	8526                	mv	a0,s1
    1806:	3d15                	jal	163a <prints>
    1808:	9522                	add	a0,a0,s0
    180a:	bf15                	j	173e <printInt+0x1e>
    180c:	87aa                	mv	a5,a0
    180e:	4702                	lw	a4,0(sp)
    1810:	b78d                	j	1772 <printInt+0x52>
    1812:	4682                	lw	a3,0(sp)
    1814:	02d00793          	li	a5,45
    1818:	4401                	li	s0,0
    181a:	fef68fa3          	sb	a5,-1(a3)
    181e:	ffe70793          	addi	a5,a4,-2
    1822:	c03e                	sw	a5,0(sp)
    1824:	bf4d                	j	17d6 <printInt+0xb6>
    1826:	33f1                	jal	15f2 <printchar>
    1828:	05800593          	li	a1,88
    182c:	b7e1                	j	17f4 <printInt+0xd4>

0000182e <printLongLongInt>:
    182e:	4501                	li	a0,0
    1830:	8082                	ret

00001832 <printDouble>:
    1832:	4501                	li	a0,0
    1834:	8082                	ret

00001836 <print>:
    1836:	fd810113          	addi	sp,sp,-40
    183a:	d022                	sw	s0,32(sp)
    183c:	ce26                	sw	s1,28(sp)
    183e:	d206                	sw	ra,36(sp)
    1840:	c42a                	sw	a0,8(sp)
    1842:	82ae                	mv	t0,a1
    1844:	8432                	mv	s0,a2
    1846:	c602                	sw	zero,12(sp)
    1848:	4481                	li	s1,0
    184a:	00028583          	lb	a1,0(t0)
    184e:	ed91                	bnez	a1,186a <print+0x34>
    1850:	47a2                	lw	a5,8(sp)
    1852:	c789                	beqz	a5,185c <print+0x26>
    1854:	4581                	li	a1,0
    1856:	853e                	mv	a0,a5
    1858:	d9bff0ef          	jal	ra,15f2 <printchar>
    185c:	5092                	lw	ra,36(sp)
    185e:	5402                	lw	s0,32(sp)
    1860:	8526                	mv	a0,s1
    1862:	44f2                	lw	s1,28(sp)
    1864:	02810113          	addi	sp,sp,40
    1868:	8082                	ret
    186a:	02500793          	li	a5,37
    186e:	00f58963          	beq	a1,a5,1880 <print+0x4a>
    1872:	4522                	lw	a0,8(sp)
    1874:	c816                	sw	t0,16(sp)
    1876:	0485                	addi	s1,s1,1
    1878:	d7bff0ef          	jal	ra,15f2 <printchar>
    187c:	42c2                	lw	t0,16(sp)
    187e:	a005                	j	189e <print+0x68>
    1880:	00128783          	lb	a5,1(t0)
    1884:	00128713          	addi	a4,t0,1
    1888:	00b79d63          	bne	a5,a1,18a2 <print+0x6c>
    188c:	4522                	lw	a0,8(sp)
    188e:	02500593          	li	a1,37
    1892:	c83a                	sw	a4,16(sp)
    1894:	d5fff0ef          	jal	ra,15f2 <printchar>
    1898:	4742                	lw	a4,16(sp)
    189a:	0485                	addi	s1,s1,1
    189c:	82ba                	mv	t0,a4
    189e:	0285                	addi	t0,t0,1
    18a0:	b76d                	j	184a <print+0x14>
    18a2:	d7dd                	beqz	a5,1850 <print+0x1a>
    18a4:	02b00693          	li	a3,43
    18a8:	04d78963          	beq	a5,a3,18fa <print+0xc4>
    18ac:	00f6c863          	blt	a3,a5,18bc <print+0x86>
    18b0:	02300693          	li	a3,35
    18b4:	04d78663          	beq	a5,a3,1900 <print+0xca>
    18b8:	4781                	li	a5,0
    18ba:	a005                	j	18da <print+0xa4>
    18bc:	02d00693          	li	a3,45
    18c0:	00d78a63          	beq	a5,a3,18d4 <print+0x9e>
    18c4:	03000693          	li	a3,48
    18c8:	fed798e3          	bne	a5,a3,18b8 <print+0x82>
    18cc:	00228713          	addi	a4,t0,2
    18d0:	4789                	li	a5,2
    18d2:	a021                	j	18da <print+0xa4>
    18d4:	00228713          	addi	a4,t0,2
    18d8:	4785                	li	a5,1
    18da:	00070683          	lb	a3,0(a4)
    18de:	02b00613          	li	a2,43
    18e2:	04c68363          	beq	a3,a2,1928 <print+0xf2>
    18e6:	02d64163          	blt	a2,a3,1908 <print+0xd2>
    18ea:	02300613          	li	a2,35
    18ee:	02c68b63          	beq	a3,a2,1924 <print+0xee>
    18f2:	82ba                	mv	t0,a4
    18f4:	4501                	li	a0,0
    18f6:	46a5                	li	a3,9
    18f8:	a081                	j	1938 <print+0x102>
    18fa:	00228713          	addi	a4,t0,2
    18fe:	bf6d                	j	18b8 <print+0x82>
    1900:	00228713          	addi	a4,t0,2
    1904:	4791                	li	a5,4
    1906:	bfd1                	j	18da <print+0xa4>
    1908:	02d00613          	li	a2,45
    190c:	00c68963          	beq	a3,a2,191e <print+0xe8>
    1910:	03000613          	li	a2,48
    1914:	fcc69fe3          	bne	a3,a2,18f2 <print+0xbc>
    1918:	0027e793          	ori	a5,a5,2
    191c:	a031                	j	1928 <print+0xf2>
    191e:	0705                	addi	a4,a4,1
    1920:	4785                	li	a5,1
    1922:	bfc1                	j	18f2 <print+0xbc>
    1924:	0047e793          	ori	a5,a5,4
    1928:	0705                	addi	a4,a4,1
    192a:	b7e1                	j	18f2 <print+0xbc>
    192c:	00251613          	slli	a2,a0,0x2
    1930:	9532                	add	a0,a0,a2
    1932:	0506                	slli	a0,a0,0x1
    1934:	953a                	add	a0,a0,a4
    1936:	0285                	addi	t0,t0,1
    1938:	00028603          	lb	a2,0(t0)
    193c:	fd060713          	addi	a4,a2,-48
    1940:	0ff77593          	andi	a1,a4,255
    1944:	feb6f4e3          	bgeu	a3,a1,192c <print+0xf6>
    1948:	02e00713          	li	a4,46
    194c:	4699                	li	a3,6
    194e:	00e61e63          	bne	a2,a4,196a <print+0x134>
    1952:	0285                	addi	t0,t0,1
    1954:	4681                	li	a3,0
    1956:	45a5                	li	a1,9
    1958:	00028603          	lb	a2,0(t0)
    195c:	fd060613          	addi	a2,a2,-48
    1960:	0ff67713          	andi	a4,a2,255
    1964:	02e5f563          	bgeu	a1,a4,198e <print+0x158>
    1968:	c636                	sw	a3,12(sp)
    196a:	00028703          	lb	a4,0(t0)
    196e:	06a00613          	li	a2,106
    1972:	0ac70d63          	beq	a4,a2,1a2c <print+0x1f6>
    1976:	02e64363          	blt	a2,a4,199c <print+0x166>
    197a:	04c00613          	li	a2,76
    197e:	0ac70763          	beq	a4,a2,1a2c <print+0x1f6>
    1982:	06800613          	li	a2,104
    1986:	08c70c63          	beq	a4,a2,1a1e <print+0x1e8>
    198a:	4581                	li	a1,0
    198c:	a82d                	j	19c6 <print+0x190>
    198e:	00269713          	slli	a4,a3,0x2
    1992:	96ba                	add	a3,a3,a4
    1994:	0686                	slli	a3,a3,0x1
    1996:	96b2                	add	a3,a3,a2
    1998:	0285                	addi	t0,t0,1
    199a:	bf7d                	j	1958 <print+0x122>
    199c:	07400613          	li	a2,116
    19a0:	08c70663          	beq	a4,a2,1a2c <print+0x1f6>
    19a4:	07a00613          	li	a2,122
    19a8:	08c70263          	beq	a4,a2,1a2c <print+0x1f6>
    19ac:	06c00613          	li	a2,108
    19b0:	4581                	li	a1,0
    19b2:	00c71a63          	bne	a4,a2,19c6 <print+0x190>
    19b6:	00128603          	lb	a2,1(t0)
    19ba:	458d                	li	a1,3
    19bc:	00e61463          	bne	a2,a4,19c4 <print+0x18e>
    19c0:	0285                	addi	t0,t0,1
    19c2:	4591                	li	a1,4
    19c4:	0285                	addi	t0,t0,1
    19c6:	00028603          	lb	a2,0(t0)
    19ca:	06000393          	li	t2,96
    19ce:	06100713          	li	a4,97
    19d2:	00c3c463          	blt	t2,a2,19da <print+0x1a4>
    19d6:	04100713          	li	a4,65
    19da:	06700393          	li	t2,103
    19de:	06c3c463          	blt	t2,a2,1a46 <print+0x210>
    19e2:	06500393          	li	t2,101
    19e6:	18765663          	bge	a2,t2,1b72 <print+0x33c>
    19ea:	04700393          	li	t2,71
    19ee:	04c3c163          	blt	t2,a2,1a30 <print+0x1fa>
    19f2:	04500593          	li	a1,69
    19f6:	16b65e63          	bge	a2,a1,1b72 <print+0x33c>
    19fa:	04300713          	li	a4,67
    19fe:	eae610e3          	bne	a2,a4,189e <print+0x68>
    1a02:	4018                	lw	a4,0(s0)
    1a04:	00440393          	addi	t2,s0,4
    1a08:	ca16                	sw	t0,20(sp)
    1a0a:	00e10c23          	sb	a4,24(sp)
    1a0e:	c81e                	sw	t2,16(sp)
    1a10:	00010ca3          	sb	zero,25(sp)
    1a14:	4701                	li	a4,0
    1a16:	86be                	mv	a3,a5
    1a18:	862a                	mv	a2,a0
    1a1a:	082c                	addi	a1,sp,24
    1a1c:	a849                	j	1aae <print+0x278>
    1a1e:	00128603          	lb	a2,1(t0)
    1a22:	4581                	li	a1,0
    1a24:	fae611e3          	bne	a2,a4,19c6 <print+0x190>
    1a28:	0289                	addi	t0,t0,2
    1a2a:	bf71                	j	19c6 <print+0x190>
    1a2c:	0285                	addi	t0,t0,1
    1a2e:	bfb1                	j	198a <print+0x154>
    1a30:	06300693          	li	a3,99
    1a34:	fcd607e3          	beq	a2,a3,1a02 <print+0x1cc>
    1a38:	06c6cf63          	blt	a3,a2,1ab6 <print+0x280>
    1a3c:	05800693          	li	a3,88
    1a40:	02d60363          	beq	a2,a3,1a66 <print+0x230>
    1a44:	bda9                	j	189e <print+0x68>
    1a46:	07300693          	li	a3,115
    1a4a:	04d60463          	beq	a2,a3,1a92 <print+0x25c>
    1a4e:	02c6cb63          	blt	a3,a2,1a84 <print+0x24e>
    1a52:	06f00693          	li	a3,111
    1a56:	0ed60563          	beq	a2,a3,1b40 <print+0x30a>
    1a5a:	07000693          	li	a3,112
    1a5e:	0047e793          	ori	a5,a5,4
    1a62:	e2d61ee3          	bne	a2,a3,189e <print+0x68>
    1a66:	4691                	li	a3,4
    1a68:	0cd59263          	bne	a1,a3,1b2c <print+0x2f6>
    1a6c:	00840393          	addi	t2,s0,8
    1a70:	400c                	lw	a1,0(s0)
    1a72:	4050                	lw	a2,4(s0)
    1a74:	ca16                	sw	t0,20(sp)
    1a76:	c23a                	sw	a4,4(sp)
    1a78:	c03e                	sw	a5,0(sp)
    1a7a:	c81e                	sw	t2,16(sp)
    1a7c:	87aa                	mv	a5,a0
    1a7e:	4701                	li	a4,0
    1a80:	46c1                	li	a3,16
    1a82:	a881                	j	1ad2 <print+0x29c>
    1a84:	07500693          	li	a3,117
    1a88:	06d60b63          	beq	a2,a3,1afe <print+0x2c8>
    1a8c:	07800693          	li	a3,120
    1a90:	bf45                	j	1a40 <print+0x20a>
    1a92:	4018                	lw	a4,0(s0)
    1a94:	000026b7          	lui	a3,0x2
    1a98:	00440393          	addi	t2,s0,4
    1a9c:	3d868593          	addi	a1,a3,984 # 23d8 <font+0x500>
    1aa0:	c311                	beqz	a4,1aa4 <print+0x26e>
    1aa2:	85ba                	mv	a1,a4
    1aa4:	4732                	lw	a4,12(sp)
    1aa6:	ca16                	sw	t0,20(sp)
    1aa8:	c81e                	sw	t2,16(sp)
    1aaa:	86be                	mv	a3,a5
    1aac:	862a                	mv	a2,a0
    1aae:	4522                	lw	a0,8(sp)
    1ab0:	b8bff0ef          	jal	ra,163a <prints>
    1ab4:	a015                	j	1ad8 <print+0x2a2>
    1ab6:	4691                	li	a3,4
    1ab8:	02d59563          	bne	a1,a3,1ae2 <print+0x2ac>
    1abc:	00840393          	addi	t2,s0,8
    1ac0:	400c                	lw	a1,0(s0)
    1ac2:	4050                	lw	a2,4(s0)
    1ac4:	ca16                	sw	t0,20(sp)
    1ac6:	c23a                	sw	a4,4(sp)
    1ac8:	c03e                	sw	a5,0(sp)
    1aca:	c81e                	sw	t2,16(sp)
    1acc:	87aa                	mv	a5,a0
    1ace:	4705                	li	a4,1
    1ad0:	46a9                	li	a3,10
    1ad2:	4522                	lw	a0,8(sp)
    1ad4:	d5bff0ef          	jal	ra,182e <printLongLongInt>
    1ad8:	43c2                	lw	t2,16(sp)
    1ada:	94aa                	add	s1,s1,a0
    1adc:	841e                	mv	s0,t2
    1ade:	42d2                	lw	t0,20(sp)
    1ae0:	bb7d                	j	189e <print+0x68>
    1ae2:	46b2                	lw	a3,12(sp)
    1ae4:	400c                	lw	a1,0(s0)
    1ae6:	c816                	sw	t0,16(sp)
    1ae8:	c236                	sw	a3,4(sp)
    1aea:	c03a                	sw	a4,0(sp)
    1aec:	0411                	addi	s0,s0,4
    1aee:	872a                	mv	a4,a0
    1af0:	4685                	li	a3,1
    1af2:	4629                	li	a2,10
    1af4:	4522                	lw	a0,8(sp)
    1af6:	c2bff0ef          	jal	ra,1720 <printInt>
    1afa:	94aa                	add	s1,s1,a0
    1afc:	b341                	j	187c <print+0x46>
    1afe:	4691                	li	a3,4
    1b00:	00d59d63          	bne	a1,a3,1b1a <print+0x2e4>
    1b04:	00840393          	addi	t2,s0,8
    1b08:	400c                	lw	a1,0(s0)
    1b0a:	4050                	lw	a2,4(s0)
    1b0c:	ca16                	sw	t0,20(sp)
    1b0e:	c23a                	sw	a4,4(sp)
    1b10:	c03e                	sw	a5,0(sp)
    1b12:	c81e                	sw	t2,16(sp)
    1b14:	87aa                	mv	a5,a0
    1b16:	4701                	li	a4,0
    1b18:	bf65                	j	1ad0 <print+0x29a>
    1b1a:	46b2                	lw	a3,12(sp)
    1b1c:	400c                	lw	a1,0(s0)
    1b1e:	c816                	sw	t0,16(sp)
    1b20:	c236                	sw	a3,4(sp)
    1b22:	c03a                	sw	a4,0(sp)
    1b24:	0411                	addi	s0,s0,4
    1b26:	872a                	mv	a4,a0
    1b28:	4681                	li	a3,0
    1b2a:	b7e1                	j	1af2 <print+0x2bc>
    1b2c:	46b2                	lw	a3,12(sp)
    1b2e:	c816                	sw	t0,16(sp)
    1b30:	400c                	lw	a1,0(s0)
    1b32:	4641                	li	a2,16
    1b34:	c236                	sw	a3,4(sp)
    1b36:	c03a                	sw	a4,0(sp)
    1b38:	0411                	addi	s0,s0,4
    1b3a:	872a                	mv	a4,a0
    1b3c:	4681                	li	a3,0
    1b3e:	bf5d                	j	1af4 <print+0x2be>
    1b40:	4691                	li	a3,4
    1b42:	00d59e63          	bne	a1,a3,1b5e <print+0x328>
    1b46:	00840393          	addi	t2,s0,8
    1b4a:	400c                	lw	a1,0(s0)
    1b4c:	4050                	lw	a2,4(s0)
    1b4e:	ca16                	sw	t0,20(sp)
    1b50:	c23a                	sw	a4,4(sp)
    1b52:	c03e                	sw	a5,0(sp)
    1b54:	c81e                	sw	t2,16(sp)
    1b56:	87aa                	mv	a5,a0
    1b58:	4701                	li	a4,0
    1b5a:	46a1                	li	a3,8
    1b5c:	bf9d                	j	1ad2 <print+0x29c>
    1b5e:	46b2                	lw	a3,12(sp)
    1b60:	400c                	lw	a1,0(s0)
    1b62:	c816                	sw	t0,16(sp)
    1b64:	c236                	sw	a3,4(sp)
    1b66:	c03a                	sw	a4,0(sp)
    1b68:	0411                	addi	s0,s0,4
    1b6a:	872a                	mv	a4,a0
    1b6c:	4681                	li	a3,0
    1b6e:	4621                	li	a2,8
    1b70:	b751                	j	1af4 <print+0x2be>
    1b72:	400c                	lw	a1,0(s0)
    1b74:	00840613          	addi	a2,s0,8
    1b78:	4040                	lw	s0,4(s0)
    1b7a:	c23a                	sw	a4,4(sp)
    1b7c:	872a                	mv	a4,a0
    1b7e:	4522                	lw	a0,8(sp)
    1b80:	c832                	sw	a2,16(sp)
    1b82:	c03e                	sw	a5,0(sp)
    1b84:	8622                	mv	a2,s0
    1b86:	87b6                	mv	a5,a3
    1b88:	46a9                	li	a3,10
    1b8a:	ca16                	sw	t0,20(sp)
    1b8c:	ca7ff0ef          	jal	ra,1832 <printDouble>
    1b90:	94aa                	add	s1,s1,a0
    1b92:	4442                	lw	s0,16(sp)
    1b94:	b7a9                	j	1ade <print+0x2a8>

00001b96 <printf>:
    1b96:	fdc10113          	addi	sp,sp,-36
    1b9a:	c82e                	sw	a1,16(sp)
    1b9c:	ca32                	sw	a2,20(sp)
    1b9e:	85aa                	mv	a1,a0
    1ba0:	0810                	addi	a2,sp,16
    1ba2:	4501                	li	a0,0
    1ba4:	c606                	sw	ra,12(sp)
    1ba6:	cc36                	sw	a3,24(sp)
    1ba8:	ce3a                	sw	a4,28(sp)
    1baa:	d03e                	sw	a5,32(sp)
    1bac:	c032                	sw	a2,0(sp)
    1bae:	c89ff0ef          	jal	ra,1836 <print>
    1bb2:	40b2                	lw	ra,12(sp)
    1bb4:	02410113          	addi	sp,sp,36
    1bb8:	8082                	ret

00001bba <puts>:
    1bba:	1141                	addi	sp,sp,-16
    1bbc:	c422                	sw	s0,8(sp)
    1bbe:	c226                	sw	s1,4(sp)
    1bc0:	c606                	sw	ra,12(sp)
    1bc2:	211c                	lbu	a5,0(a0)
    1bc4:	84aa                	mv	s1,a0
    1bc6:	4401                	li	s0,0
    1bc8:	81dc                	sb	a5,3(sp)
    1bca:	00310783          	lb	a5,3(sp)
    1bce:	0405                	addi	s0,s0,1
    1bd0:	ef99                	bnez	a5,1bee <puts+0x34>
    1bd2:	47a9                	li	a5,10
    1bd4:	00310593          	addi	a1,sp,3
    1bd8:	4605                	li	a2,1
    1bda:	4501                	li	a0,0
    1bdc:	81dc                	sb	a5,3(sp)
    1bde:	9c9ff0ef          	jal	ra,15a6 <_write>
    1be2:	8522                	mv	a0,s0
    1be4:	40b2                	lw	ra,12(sp)
    1be6:	4422                	lw	s0,8(sp)
    1be8:	4492                	lw	s1,4(sp)
    1bea:	0141                	addi	sp,sp,16
    1bec:	8082                	ret
    1bee:	4605                	li	a2,1
    1bf0:	00310593          	addi	a1,sp,3
    1bf4:	4501                	li	a0,0
    1bf6:	9b1ff0ef          	jal	ra,15a6 <_write>
    1bfa:	008487b3          	add	a5,s1,s0
    1bfe:	239c                	lbu	a5,0(a5)
    1c00:	81dc                	sb	a5,3(sp)
    1c02:	b7e1                	j	1bca <puts+0x10>

00001c04 <memcpy>:
    1c04:	00a5c7b3          	xor	a5,a1,a0
    1c08:	8b8d                	andi	a5,a5,3
    1c0a:	00c50733          	add	a4,a0,a2
    1c0e:	e781                	bnez	a5,1c16 <memcpy+0x12>
    1c10:	478d                	li	a5,3
    1c12:	02c7e763          	bltu	a5,a2,1c40 <memcpy+0x3c>
    1c16:	87aa                	mv	a5,a0
    1c18:	0ae57e63          	bgeu	a0,a4,1cd4 <memcpy+0xd0>
    1c1c:	2194                	lbu	a3,0(a1)
    1c1e:	0785                	addi	a5,a5,1
    1c20:	0585                	addi	a1,a1,1
    1c22:	fed78fa3          	sb	a3,-1(a5)
    1c26:	fee7ebe3          	bltu	a5,a4,1c1c <memcpy+0x18>
    1c2a:	8082                	ret
    1c2c:	2194                	lbu	a3,0(a1)
    1c2e:	0785                	addi	a5,a5,1
    1c30:	0585                	addi	a1,a1,1
    1c32:	fed78fa3          	sb	a3,-1(a5)
    1c36:	fee7ebe3          	bltu	a5,a4,1c2c <memcpy+0x28>
    1c3a:	4402                	lw	s0,0(sp)
    1c3c:	0111                	addi	sp,sp,4
    1c3e:	8082                	ret
    1c40:	00357693          	andi	a3,a0,3
    1c44:	87aa                	mv	a5,a0
    1c46:	ca89                	beqz	a3,1c58 <memcpy+0x54>
    1c48:	2194                	lbu	a3,0(a1)
    1c4a:	0785                	addi	a5,a5,1
    1c4c:	0585                	addi	a1,a1,1
    1c4e:	fed78fa3          	sb	a3,-1(a5)
    1c52:	0037f693          	andi	a3,a5,3
    1c56:	bfc5                	j	1c46 <memcpy+0x42>
    1c58:	ffc77693          	andi	a3,a4,-4
    1c5c:	fe068613          	addi	a2,a3,-32
    1c60:	06c7f563          	bgeu	a5,a2,1cca <memcpy+0xc6>
    1c64:	1171                	addi	sp,sp,-4
    1c66:	c022                	sw	s0,0(sp)
    1c68:	49c0                	lw	s0,20(a1)
    1c6a:	0005a303          	lw	t1,0(a1)
    1c6e:	0085a383          	lw	t2,8(a1)
    1c72:	cbc0                	sw	s0,20(a5)
    1c74:	4d80                	lw	s0,24(a1)
    1c76:	0067a023          	sw	t1,0(a5)
    1c7a:	0045a303          	lw	t1,4(a1)
    1c7e:	cf80                	sw	s0,24(a5)
    1c80:	4dc0                	lw	s0,28(a1)
    1c82:	0067a223          	sw	t1,4(a5)
    1c86:	00c5a283          	lw	t0,12(a1)
    1c8a:	0105a303          	lw	t1,16(a1)
    1c8e:	02458593          	addi	a1,a1,36
    1c92:	cfc0                	sw	s0,28(a5)
    1c94:	ffc5a403          	lw	s0,-4(a1)
    1c98:	0077a423          	sw	t2,8(a5)
    1c9c:	0057a623          	sw	t0,12(a5)
    1ca0:	0067a823          	sw	t1,16(a5)
    1ca4:	02478793          	addi	a5,a5,36
    1ca8:	fe87ae23          	sw	s0,-4(a5)
    1cac:	fac7eee3          	bltu	a5,a2,1c68 <memcpy+0x64>
    1cb0:	f8d7f3e3          	bgeu	a5,a3,1c36 <memcpy+0x32>
    1cb4:	4190                	lw	a2,0(a1)
    1cb6:	0791                	addi	a5,a5,4
    1cb8:	0591                	addi	a1,a1,4
    1cba:	fec7ae23          	sw	a2,-4(a5)
    1cbe:	bfcd                	j	1cb0 <memcpy+0xac>
    1cc0:	4190                	lw	a2,0(a1)
    1cc2:	0791                	addi	a5,a5,4
    1cc4:	0591                	addi	a1,a1,4
    1cc6:	fec7ae23          	sw	a2,-4(a5)
    1cca:	fed7ebe3          	bltu	a5,a3,1cc0 <memcpy+0xbc>
    1cce:	f4e7e7e3          	bltu	a5,a4,1c1c <memcpy+0x18>
    1cd2:	8082                	ret
    1cd4:	8082                	ret
    1cd6:	0000                	unimp
    1cd8:	1609                	addi	a2,a2,-30
    1cda:	2009                	jal	1cdc <memcpy+0xd8>
    1cdc:	1b21                	addi	s6,s6,-24
    1cde:	15171913          	0x15171913
    1ce2:	2b1e                	lhu	a5,16(a4)
    1ce4:	0504                	addi	s1,sp,640
    1ce6:	0e02                	c.slli64	t3
    1ce8:	1e08140b          	0x1e08140b
    1cec:	1d22                	slli	s10,s10,0x28
    1cee:	1e18                	addi	a4,sp,816
    1cf0:	2b241a1b          	0x2b241a1b
    1cf4:	0606                	slli	a2,a2,0x1
    1cf6:	0f02                	c.slli64	t5
    1cf8:	00004843          	fmadd.s	fa6,ft0,ft0,ft0,rmm
    1cfc:	003a                	c.slli	zero,0xe
    1cfe:	0000                	unimp
    1d00:	4441                	li	s0,16
    1d02:	6f4d2043          	fmadd.q	ft0,fs10,fs4,fa3,rdn
    1d06:	696e                	flw	fs2,216(sp)
    1d08:	6f74                	flw	fa3,92(a4)
    1d0a:	0072                	c.slli	zero,0x1c

00001d0c <CSWTCH.2>:
    1d0c:	07ff 07e0 ffe0 f81f 6944 7073 616c 2079     ........Display 
    1d1c:	664f 0066 4843 3233 3056 3330 4120 4344     Off.CH32V003 ADC
    1d2c:	0000 0000 6f4d 696e 6f74 2072 3176 302e     ....Monitor v1.0
    1d3c:	0000 0000 6f54 6375 3a68 5420 7275 206e     ....Touch: Turn 
    1d4c:	6e4f 0000 6f48 646c 203a 6f54 6767 656c     On..Hold: Toggle
    1d5c:	0000 0000 6568 6c6c 006f 0000 4843 3233     ....hello...CH32
    1d6c:	3056 3330 0000 0000 4441 2043 6e69 7469     V003....ADC init
    1d7c:	6169 696c 657a 0d64 0000 0000 5750 204d     ialized.....PWM 
    1d8c:	6e69 7469 6169 696c 657a 0d64 0000 0000     initialized.....
    1d9c:	6f54 6375 2068 7562 7474 6e6f 6920 696e     Touch button ini
    1dac:	6974 6c61 7a69 6465 000d 0000 6944 7073     tialized....Disp
    1dbc:	616c 2079 6f63 746e 6f72 206c 6e69 7469     lay control init
    1dcc:	6169 696c 657a 0d64 0000 0000 4441 2043     ialized.....ADC 
    1ddc:	6964 7073 616c 2079 6e69 7469 6169 696c     display initiali
    1dec:	657a 0d64 0000 0000 7953 7473 6d65 6920     zed.....System i
    1dfc:	696e 6974 6c61 7a69 7461 6f69 206e 6f63     nitialization co
    1e0c:	706d 656c 6574 000d 0a0d 3d3d 203d 4843     mplete....=== CH
    1e1c:	3233 3056 3330 4120 4344 4d20 6e6f 7469     32V003 ADC Monit
    1e2c:	726f 3d20 3d3d 000d 7953 7473 6d65 6c43     or ===..SystemCl
    1e3c:	3a6b 2520 2064 7a48 0a0d 0000 6843 7069     k: %d Hz....Chip
    1e4c:	4449 203a 3025 7838 0a0d 0000 6f54 6375     ID: %08x....Touc
    1e5c:	3a68 5320 6f68 7472 7020 6572 7373 2d20     h: Short press -
    1e6c:	7420 7275 696e 676e 6f20 206e 6964 7073      turning on disp
    1e7c:	616c 0d79 0000 0000 6f54 6375 3a68 4c20     lay.....Touch: L
    1e8c:	6e6f 2067 7270 7365 2073 202d 6f74 6767     ong press - togg
    1e9c:	696c 676e 6420 7369 6c70 7961 6d20 646f     ling display mod
    1eac:	0d65 0000 6f54 6375 3a68 5420 6d69 6f65     e...Touch: Timeo
    1ebc:	7475 2d20 7420 7275 696e 676e 6f20 6666     ut - turning off
    1ecc:	6420 7369 6c70 7961 000d 0000                display....

00001ed8 <font>:
    1ed8:	0000 0000 3e00 4f5b 3e5b 6b3e 6b4f 1c3e     .....>[O[>>kOk>.
    1ee8:	7c3e 1c3e 3c18 3c7e 1c18 7d57 1c57 5e1c     >|>..<~<..W}W..^
    1ef8:	5e7f 001c 3c18 0018 e7ff e7c3 00ff 2418     .^...<.........$
    1f08:	0018 e7ff e7db 30ff 3a48 0e06 2926 2979     .......0H:..&)y)
    1f18:	4026 057f 0705 7f40 2505 5a3f e73c 5a3c     &@....@..%?Z<.<Z
    1f28:	3e7f 1c1c 0808 1c1c 7f3e 2214 227f 5f14     .>......>.."."._
    1f38:	005f 5f5f 0906 017f 007f 8966 6a95 6060     _.__......f..j``
    1f48:	6060 9460 ffa2 94a2 0408 047e 1008 7e20     ```.......~... ~
    1f58:	1020 0808 1c2a 0808 2a1c 0808 101e 1010      ...*....*......
    1f68:	0c10 0c1e 0c1e 3830 383e 0630 3e0e 060e     ......08>80..>..
    1f78:	0000 0000 0000 5f00 0000 0700 0700 1400     ......._........
    1f88:	147f 147f 2a24 2a7f 2312 0813 6264 4936     ....$*.*.#..db6I
    1f98:	2056 0050 0708 0003 1c00 4122 0000 2241     V P......."A..A"
    1fa8:	001c 1c2a 1c7f 082a 3e08 0808 8000 3070     ..*...*..>....p0
    1fb8:	0800 0808 0808 0000 6060 2000 0810 0204     ........``. ....
    1fc8:	513e 4549 003e 7f42 0040 4972 4949 2146     >QIE>.B.@.rIIIF!
    1fd8:	4941 334d 1418 7f12 2710 4545 3945 4a3c     AIM3.....'EEE9<J
    1fe8:	4949 4131 1121 0709 4936 4949 4636 4949     II1A!...6III6FII
    1ff8:	1e29 0000 0014 0000 3440 0000 0800 2214     ).......@4....."
    2008:	1441 1414 1414 4100 1422 0208 5901 0609     A......A"....Y..
    2018:	413e 595d 7c4e 1112 7c12 497f 4949 3e36     >A]YN|...|.III6>
    2028:	4141 2241 417f 4141 7f3e 4949 4149 097f     AAA".AAA>.IIIA..
    2038:	0909 3e01 4141 7351 087f 0808 007f 7f41     ...>AAQs......A.
    2048:	0041 4020 3f41 7f01 1408 4122 407f 4040     A. @A?...."A.@@@
    2058:	7f40 1c02 7f02 047f 1008 3e7f 4141 3e41     @..........>AAA>
    2068:	097f 0909 3e06 5141 5e21 097f 2919 2646     .....>AQ!^...)F&
    2078:	4949 3249 0103 017f 3f03 4040 3f40 201f     III2.....?@@@?. 
    2088:	2040 3f1f 3840 3f40 1463 1408 0363 7804     @ .?@8@?c...c..x
    2098:	0304 5961 4d49 0043 417f 4141 0402 1008     ..aYIMC..AAA....
    20a8:	0020 4141 7f41 0204 0201 4004 4040 4040      .AAA......@@@@@
    20b8:	0300 0807 2000 5454 4078 287f 4444 3838     ..... TTx@.(DD88
    20c8:	4444 2844 4438 2844 387f 5454 1854 0800     DDD(8DD(.8TTT...
    20d8:	097e 1802 a4a4 789c 087f 0404 0078 7d44     ~......x....x.D}
    20e8:	0040 4020 3d40 7f00 2810 0044 4100 407f     @. @@=...(D..A.@
    20f8:	7c00 7804 7804 087c 0404 3878 4444 3844     .|.x.x|...x8DDD8
    2108:	18fc 2424 1818 2424 fc18 087c 0404 4808     ..$$..$$..|....H
    2118:	5454 2454 0404 443f 3c24 4040 7c20 201c     TTT$..?D$<@@ |. 
    2128:	2040 3c1c 3040 3c40 2844 2810 4c44 9090     @ .<@0@<D(.(DL..
    2138:	7c90 6444 4c54 0044 3608 0041 0000 0077     .|DdTLD..6A...w.
    2148:	0000 3641 0008 0102 0402 3c02 2326 3c26     ..A6.......<&#&<
    2158:	a11e 61a1 3a12 4040 7a20 5438 5554 2159     ...a.:@@ z8TTUY!
    2168:	5555 4179 5422 7854 2142 5455 4078 5420     UUyA"TTxB!UTx@ T
    2178:	7955 0c40 521e 1272 5539 5555 3959 5454     <EMAIL>.9UUUY9TT
    2188:	5954 5539 5454 0058 4500 417c 0200 7d45     TY9UTTX..E|A..E}
    2198:	0042 4501 407c 127d 1211 f07d 2528 f028     B..E|@}...}.(%(.
    21a8:	547c 4555 2000 5454 547c 0a7c 7f09 3249     |TUE. TT|T|...I2
    21b8:	4949 3249 443a 4444 323a 484a 3048 413a     III2:DDD:2JHH0:A
    21c8:	2141 3a7a 4042 7820 9d00 a0a0 3d7d 4242     A!z:B@ x....}=BB
    21d8:	3d42 403d 4040 3c3d ff24 2424 7e48 4349     B==@@@=<$.$$H~IC
    21e8:	2b66 fc2f 2b2f 09ff f629 c020 7e88 0309     f+/./+..). ..~..
    21f8:	5420 7954 0041 4400 417d 4830 4a48 3832      TTyA..D}A0HHJ28
    2208:	4040 7a22 7a00 0a0a 7d72 190d 7d31 2926     @@"z.z..r}..1}&)
    2218:	2f29 2628 2929 2629 4830 404d 3820 0808     )/(&)))&0HM@ 8..
    2228:	0808 0808 0808 2f38 c810 baac 102f 3428     ......8/..../.(4
    2238:	00fa 7b00 0000 1408 142a 2222 2a14 0814     ...{....*."".*..
    2248:	0055 0055 aa55 aa55 aa55 55ff 55ff 00ff     U.U.U.U.U..U.U..
    2258:	0000 00ff 1010 ff10 1400 1414 00ff 1010     ................
    2268:	00ff 10ff f010 f010 1414 fc14 1400 f714     ................
    2278:	ff00 0000 00ff 14ff f414 fc04 1414 1017     ................
    2288:	101f 1f10 1f10 1414 1f14 1000 1010 00f0     ................
    2298:	0000 1f00 1010 1010 101f 1010 f010 0010     ................
    22a8:	0000 10ff 1010 1010 1010 1010 10ff 0000     ................
    22b8:	ff00 0014 ff00 ff00 0000 101f 0017 fc00     ................
    22c8:	f404 1414 1017 1417 f414 f404 0000 00ff     ................
    22d8:	14f7 1414 1414 1414 00f7 14f7 1414 1417     ................
    22e8:	1010 101f 141f 1414 14f4 1010 10f0 00f0     ................
    22f8:	1f00 1f10 0000 1f00 0014 0000 14fc 0000     ................
    2308:	10f0 10f0 ff10 ff10 1414 ff14 1014 1010     ................
    2318:	001f 0000 f000 ff10 ffff ffff f0f0 f0f0     ................
    2328:	fff0 ffff 0000 0000 ff00 0fff 0f0f 0f0f     ................
    2338:	4438 3844 fc44 4a4a 344a 027e 0602 0206     8DD8D.JJJ4~.....
    2348:	027e 027e 5563 4149 3863 4444 043c 7e40     ~.~.cUIAc8DD<.@~
    2358:	1e20 0620 7e02 0202 a599 a5e7 1c99 492a      . ..~........*I
    2368:	1c2a 724c 7201 304c 4d4a 304d 4830 4878     *.Lr.rL0JMM00HxH
    2378:	bc30 5a62 3d46 493e 4949 7e00 0101 7e01     0.bZF=>III.~...~
    2388:	2a2a 2a2a 442a 5f44 4444 5140 444a 4040     *****DD_DD@QJD@@
    2398:	4a44 4051 0000 01ff e003 ff80 0000 0808     DJQ@............
    23a8:	6b6b 3608 3612 3624 0600 0909 0006 1800     kk.6.6$6........
    23b8:	0018 0000 1010 3000 ff40 0101 1f00 0101     .......0@.......
    23c8:	001e 1d19 1217 3c00 3c3c 003c 0000 0000     .......<<<<.....
    23d8:	6e28 6c75 296c 0000                         (null)..
