
CH32V003F4U6.elf:     file format elf32-littleriscv
CH32V003F4U6.elf
architecture: riscv:rv32, flags 0x00000112:
EXEC_P, HAS_SYMS, D_PAGED
start address 0x00000000

Program Header:
    LOAD off    0x00001000 vaddr 0x00000000 paddr 0x00000000 align 2**12
         filesz 0x000026c8 memsz 0x000026c8 flags r-x
    LOAD off    0x00004000 vaddr 0x20000000 paddr 0x000026c8 align 2**12
         filesz 0x00000040 memsz 0x000001d4 flags rw-
    LOAD off    0x00004700 vaddr 0x20000700 paddr 0x20000700 align 2**12
         filesz 0x00000000 memsz 0x00000100 flags rw-

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .init         000000a0  00000000  00000000  00001000  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  1 .highcodelalign 00000000  000000a0  000000a0  00004040  2**0
                  CONTENTS
  2 .highcode     00000000  20000000  20000000  00004040  2**0
                  CONTENTS
  3 .text         00002628  000000a0  000000a0  000010a0  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  4 .fini         00000000  000026c8  000026c8  00004040  2**0
                  CONTENTS, ALLOC, LOAD, CODE
  5 .dalign       00000000  20000000  20000000  00004040  2**0
                  CONTENTS
  6 .dlalign      00000000  000026c8  000026c8  00004040  2**0
                  CONTENTS
  7 .data         00000040  20000000  000026c8  00004000  2**2
                  CONTENTS, ALLOC, LOAD, DATA
  8 .bss          00000194  20000040  00002708  00004040  2**2
                  ALLOC
  9 .stack        00000100  20000700  20000700  00004700  2**0
                  ALLOC
 10 .debug_info   00012761  00000000  00000000  00004040  2**0
                  CONTENTS, READONLY, DEBUGGING
 11 .debug_abbrev 00003472  00000000  00000000  000167a1  2**0
                  CONTENTS, READONLY, DEBUGGING
 12 .debug_loc    00004a01  00000000  00000000  00019c13  2**0
                  CONTENTS, READONLY, DEBUGGING
 13 .debug_aranges 000009f8  00000000  00000000  0001e618  2**3
                  CONTENTS, READONLY, DEBUGGING
 14 .debug_ranges 00000bd0  00000000  00000000  0001f010  2**3
                  CONTENTS, READONLY, DEBUGGING
 15 .debug_line   0000bfe0  00000000  00000000  0001fbe0  2**0
                  CONTENTS, READONLY, DEBUGGING
 16 .debug_str    00003058  00000000  00000000  0002bbc0  2**0
                  CONTENTS, READONLY, DEBUGGING
 17 .comment      00000033  00000000  00000000  0002ec18  2**0
                  CONTENTS, READONLY
 18 .debug_frame  00001740  00000000  00000000  0002ec4c  2**2
                  CONTENTS, READONLY, DEBUGGING
SYMBOL TABLE:
00000000 l    d  .init	00000000 .init
000000a0 l    d  .highcodelalign	00000000 .highcodelalign
20000000 l    d  .highcode	00000000 .highcode
000000a0 l    d  .text	00000000 .text
000026c8 l    d  .fini	00000000 .fini
20000000 l    d  .dalign	00000000 .dalign
000026c8 l    d  .dlalign	00000000 .dlalign
20000000 l    d  .data	00000000 .data
20000040 l    d  .bss	00000000 .bss
20000700 l    d  .stack	00000000 .stack
00000000 l    d  .debug_info	00000000 .debug_info
00000000 l    d  .debug_abbrev	00000000 .debug_abbrev
00000000 l    d  .debug_loc	00000000 .debug_loc
00000000 l    d  .debug_aranges	00000000 .debug_aranges
00000000 l    d  .debug_ranges	00000000 .debug_ranges
00000000 l    d  .debug_line	00000000 .debug_line
00000000 l    d  .debug_str	00000000 .debug_str
00000000 l    d  .comment	00000000 .comment
00000000 l    d  .debug_frame	00000000 .debug_frame
00000000 l    df *ABS*	00000000 adc_display.c
0000014a l     F .text	00000048 ADC_Display_Format_Voltage.part.0
00001ff4 l     O .text	00000008 CSWTCH.3
00000000 l    df *ABS*	00000000 ch32v00x_it.c
00000000 l    df *ABS*	00000000 display_control.c
00000000 l    df *ABS*	00000000 display_text.c
00000000 l    df *ABS*	00000000 main.c
00000000 l    df *ABS*	00000000 pwm_config.c
00000000 l    df *ABS*	00000000 st7735.c
000009d0 l     F .text	0000003e SPI_send_DMA
00000a0e l     F .text	00000012 SPI_send
00000a20 l     F .text	00000016 write_command_8
00000a36 l     F .text	00000020 write_data_16
00000a56 l     F .text	0000003c tft_set_window
20000062 l     O .bss	00000002 _bg_color
20000064 l     O .bss	00000140 _buffer
200001a4 l     O .bss	00000002 _cursor_x
200001a6 l     O .bss	00000002 _cursor_y
200001a8 l     O .bss	0000000c str.4169
20000000 l     O .data	00000002 _color
000021c0 l     O .text	00000500 font
00000000 l    df *ABS*	00000000 system_ch32v00x.c
00000000 l    df *ABS*	00000000 touch_button.c
00000000 l    df *ABS*	00000000 ch32v00x_dbgmcu.c
00000000 l    df *ABS*	00000000 ch32v00x_exti.c
00000000 l    df *ABS*	00000000 ch32v00x_gpio.c
00000000 l    df *ABS*	00000000 ch32v00x_misc.c
00000000 l    df *ABS*	00000000 ch32v00x_rcc.c
20000018 l     O .data	00000014 ADCPrescTable
2000002c l     O .data	00000010 APBAHBPrescTable
00000000 l    df *ABS*	00000000 ch32v00x_tim.c
00000000 l    df *ABS*	00000000 ch32v00x_usart.c
00000000 l    df *ABS*	00000000 debug.c
200001d0 l     O .bss	00000002 p_ms
200001d2 l     O .bss	00000001 p_us
00000000 l    df *ABS*	00000000 wchprintf.c
00000000 l    df *ABS*	00000000 memcpy.c
00001aba  w    F .text	00000004 printDouble
00000216 g     F .text	00000026 ADC_Display_Init
20000040 g     O .bss	0000000c adc_display_config
00001abe  w    F .text	00000360 print
00001e1e  w    F .text	00000024 printf
20000840 g       .data	00000000 __global_pointer$
0000055c g     F .text	00000024 Display_Control_Init
000011d2  w      .text	00000000 TIM1_CC_IRQHandler
0000044c g     F .text	00000010 HardFault_Handler
0000199e  w    F .text	00000118 printInt
00001632 g     F .text	0000000e TIM_OC1PreloadConfig
000010e4 g     F .text	0000002a Touch_Button_Init
000011d2  w      .text	00000000 SysTick_Handler
000013ce g     F .text	00000062 NVIC_Init
000011d2  w      .text	00000000 PVD_IRQHandler
00001e42 g     F .text	0000002a snprintf
0000044a g     F .text	00000002 NMI_Handler
00001284 g     F .text	0000000a DBGMCU_GetCHIPID
200001b4 g     O .bss	00000004 system_tick_ms
00000c22 g     F .text	0000000e tft_set_cursor
00001754 g     F .text	0000000a USART_GetFlagStatus
20000040 g       .bss	00000000 _sbss
00000100 g       *ABS*	00000000 __stack_size
000017cc g     F .text	00000058 USART_Printf_Init
00000678 g     F .text	0000001a Display_Text_Clear_Screen
000000aa g     F .text	0000000a .hidden __riscv_restore_2
000015f0 g     F .text	00000016 TIM_CtrlPWMOutputs
00001eb6 g     F .text	000000d2 memcpy
00001ab6  w    F .text	00000004 printLongLongInt
000001ba g     F .text	0000005c ADC_Display_Draw_Channel_Labels
000015d8 g     F .text	00000018 TIM_Cmd
00001e6c g     F .text	0000004a puts
00000192 g     F .text	00000028 ADC_Display_Draw_Header
20000014 g     O .data	00000004 SystemCoreClock
2000004c g     O .bss	0000000c display_control
00000488 g     F .text	00000016 Display_Control_Turn_On
000000d4 g     F .text	0000002c .hidden __udivsi3
000000a0 g       .init	00000000 _einit
0000165c g     F .text	0000000c TIM_ClearITPendingBit
000014e4 g     F .text	0000001e RCC_APB2PeriphClockCmd
00001320 g     F .text	0000007c GPIO_Init
200001cc g     O .bss	00000004 NVIC_Priority_Group
000011d2  w      .text	00000000 SPI1_IRQHandler
00001736 g     F .text	00000016 USART_Cmd
000000a0 g     F .text	0000000a .hidden __riscv_save_1
00000d24 g     F .text	00000020 tft_print
000000aa g     F .text	0000000a .hidden __riscv_restore_0
000011d2  w      .text	00000000 AWU_IRQHandler
00000326 g     F .text	00000086 ADC_Display_Draw_Logo_Channels
0000045c g     F .text	00000008 EXTI7_0_IRQHandler
00000692 g     F .text	0000004e Display_Text_Welcome
00001502 g     F .text	0000001e RCC_APB1PeriphClockCmd
20000700 g       .stack	00000000 _heap_end
00000580 g     F .text	0000002e Display_Control_Show_Off_Message
000005ae g     F .text	0000003e Display_Control_Update
000011d2  w      .text	00000000 DMA1_Channel4_IRQHandler
000011d2  w      .text	00000000 ADC1_IRQHandler
0000110e g     F .text	00000072 Touch_Button_Update
00001316 g     F .text	0000000a EXTI_ClearITPendingBit
200001d4 g       .bss	00000000 _ebss
000011d2  w      .text	00000000 DMA1_Channel7_IRQHandler
0000156e g     F .text	0000006a TIM_OC1Init
0000175e g     F .text	00000034 Delay_Init
000010a2 g     F .text	00000042 Touch_Button_EXTI_Config
00001618 g     F .text	0000001a TIM_ARRPreloadConfig
00000c3a g     F .text	00000006 tft_set_background_color
00000100 g     F .text	00000008 .hidden __umodsi3
00000872 g     F .text	0000009a PWM_Timer_Config
000011d2  w      .text	00000000 I2C1_EV_IRQHandler
000002b4 g     F .text	00000072 ADC_Display_Draw_Logo_Header
00001644 g     F .text	00000018 TIM_GetITStatus
00001442 g     F .text	000000a2 RCC_GetClocksFreq
0000101c g     F .text	00000030 Touch_Button_GPIO_Config
000011d2  w      .text	00000000 DMA1_Channel6_IRQHandler
00001668 g     F .text	000000ce USART_Init
000011d2  w      .text	00000000 RCC_IRQHandler
000011d2  w      .text	00000000 TIM1_TRG_COM_IRQHandler
000011d2  w      .text	00000000 DMA1_Channel1_IRQHandler
00000000 g       .init	00000000 _start
20000004 g     O .data	00000010 AHBPrescTable
000009bc g     F .text	0000000e PWM_Turn_Off
000004d0 g     F .text	00000018 Display_Control_Clear_Screen
000018b8  w    F .text	000000e6 prints
00001606 g     F .text	00000012 TIM_ITConfig
20000000 g       .highcode	00000000 _highcode_vma_start
0000128e g     F .text	0000006a EXTI_Init
000000b4 g     F .text	00000014 .hidden __mulsi3
000004b8 g     F .text	00000018 Display_Control_Toggle
20000000 g       .dalign	00000000 _data_vma
00000938 g     F .text	00000022 PWM_Config_Init
00001180 g     F .text	0000000c Touch_Button_Get_Event
00000632 g     F .text	00000046 Display_Text_Centered
0000041c g     F .text	0000002e ADC_Display_Draw_Logo_Style
000013a6 g     F .text	00000022 GPIO_EXTILineConfig
0000049e g     F .text	0000001a Display_Control_Turn_Off
000012f8 g     F .text	0000001e EXTI_GetITStatus
00001430 g     F .text	00000012 RCC_AdjustHSICalibrationValue
00000740 g     F .text	000000fc main
000006e0 g     F .text	00000060 System_Init
000003ac g     F .text	00000070 ADC_Display_Draw_Logo_Main_Voltage
000011d2  w      .text	00000000 DMA1_Channel5_IRQHandler
000005ec g     F .text	00000046 Display_Text_Custom
000000cc g     F .text	00000058 .hidden __divsi3
00001792 g     F .text	0000003a Delay_Ms
00000d44 g     F .text	000000aa tft_print_number
00000a92 g     F .text	00000190 tft_init
000000a0 g       .highcodelalign	00000000 _highcode_lma
00000e7c g     F .text	00000134 SystemInit
000009ca g     F .text	00000006 PWM_Get_Brightness
00001870  w    F .text	00000048 printchar
00000000 g       .init	00000000 _sinit
00000dee g     F .text	0000008e tft_fill_rect
000011d2  w      .text	00000000 DMA1_Channel3_IRQHandler
000009ac g     F .text	00000010 PWM_Turn_On
000011d2  w      .text	00000000 TIM1_UP_IRQHandler
20000058 g     O .bss	00000001 system_initialized
000011d2  w      .text	00000000 WWDG_IRQHandler
0000083c g     F .text	00000036 PWM_GPIO_Config
00000464 g     F .text	00000024 TIM2_IRQHandler
20000800 g       .stack	00000000 _eusrstack
000000a0 g     F .text	0000000a .hidden __riscv_save_2
000011d2  w      .text	00000000 SW_Handler
2000005c g     O .bss	00000006 pwm_control
000011d2  w      .text	00000000 TIM1_BRK_IRQHandler
0000174c g     F .text	00000008 USART_SendData
00001824 g     F .text	0000004c _write
20000040 g       .data	00000000 _edata
200001d4 g       .bss	00000000 _end
20000000 g       .highcode	00000000 _highcode_vma_end
00001520 g     F .text	0000004e TIM_TimeBaseInit
000026c8 g       .dlalign	00000000 _data_lma
00000fb0 g     F .text	0000006c SystemCoreClockUpdate
0000118c g     F .text	00000046 Touch_Button_IRQ_Handler
00000124 g     F .text	00000024 .hidden __modsi3
000011d2  w      .text	00000000 DMA1_Channel2_IRQHandler
000011d4  w      .text	00000000 handle_reset
000011d2  w      .text	00000000 FLASH_IRQHandler
000000a0 g     F .text	0000000a .hidden __riscv_save_0
000011d2  w      .text	00000000 USART1_IRQHandler
0000104c g     F .text	00000056 Touch_Button_Timer_Init
00000c30 g     F .text	0000000a tft_set_color
0000090c g     F .text	0000002c PWM_Set_Brightness
000011d2  w      .text	00000000 I2C1_ER_IRQHandler
000013c8 g     F .text	00000006 NVIC_PriorityGroupConfig
00000c40 g     F .text	000000e4 tft_print_char
0000095a g     F .text	00000052 PWM_Update_Fade
0000023c g     F .text	00000078 ADC_Display_Draw_Logo_Border
000004e8 g     F .text	00000074 Display_Control_Show_Startup_Message
000000aa g     F .text	0000000a .hidden __riscv_restore_1
00001640 g     F .text	00000004 TIM_SetCompare1
0000139c g     F .text	0000000a GPIO_ReadInputDataBit
200001b8 g     O .bss	00000014 touch_button



Disassembly of section .init:

00000000 <_sinit>:
   0:	1d40106f          	j	11d4 <handle_reset>
   4:	0000                	unimp
   6:	0000                	unimp
   8:	044a                	slli	s0,s0,0x12
   a:	0000                	unimp
   c:	044c                	addi	a1,sp,516
	...
  2e:	0000                	unimp
  30:	11d2                	slli	gp,gp,0x34
  32:	0000                	unimp
  34:	0000                	unimp
  36:	0000                	unimp
  38:	11d2                	slli	gp,gp,0x34
  3a:	0000                	unimp
  3c:	0000                	unimp
  3e:	0000                	unimp
  40:	11d2                	slli	gp,gp,0x34
  42:	0000                	unimp
  44:	11d2                	slli	gp,gp,0x34
  46:	0000                	unimp
  48:	11d2                	slli	gp,gp,0x34
  4a:	0000                	unimp
  4c:	11d2                	slli	gp,gp,0x34
  4e:	0000                	unimp
  50:	045c                	addi	a5,sp,516
  52:	0000                	unimp
  54:	11d2                	slli	gp,gp,0x34
  56:	0000                	unimp
  58:	11d2                	slli	gp,gp,0x34
  5a:	0000                	unimp
  5c:	11d2                	slli	gp,gp,0x34
  5e:	0000                	unimp
  60:	11d2                	slli	gp,gp,0x34
  62:	0000                	unimp
  64:	11d2                	slli	gp,gp,0x34
  66:	0000                	unimp
  68:	11d2                	slli	gp,gp,0x34
  6a:	0000                	unimp
  6c:	11d2                	slli	gp,gp,0x34
  6e:	0000                	unimp
  70:	11d2                	slli	gp,gp,0x34
  72:	0000                	unimp
  74:	11d2                	slli	gp,gp,0x34
  76:	0000                	unimp
  78:	11d2                	slli	gp,gp,0x34
  7a:	0000                	unimp
  7c:	11d2                	slli	gp,gp,0x34
  7e:	0000                	unimp
  80:	11d2                	slli	gp,gp,0x34
  82:	0000                	unimp
  84:	11d2                	slli	gp,gp,0x34
  86:	0000                	unimp
  88:	11d2                	slli	gp,gp,0x34
  8a:	0000                	unimp
  8c:	11d2                	slli	gp,gp,0x34
  8e:	0000                	unimp
  90:	11d2                	slli	gp,gp,0x34
  92:	0000                	unimp
  94:	11d2                	slli	gp,gp,0x34
  96:	0000                	unimp
  98:	0464                	addi	s1,sp,524
  9a:	0000                	unimp
  9c:	0000                	unimp
	...

Disassembly of section .text:

000000a0 <__riscv_save_0>:
      a0:	1151                	addi	sp,sp,-12
      a2:	c026                	sw	s1,0(sp)
      a4:	c222                	sw	s0,4(sp)
      a6:	c406                	sw	ra,8(sp)
      a8:	8282                	jr	t0

000000aa <__riscv_restore_0>:
      aa:	4482                	lw	s1,0(sp)
      ac:	4412                	lw	s0,4(sp)
      ae:	40a2                	lw	ra,8(sp)
      b0:	0131                	addi	sp,sp,12
      b2:	8082                	ret

000000b4 <__mulsi3>:
      b4:	862a                	mv	a2,a0
      b6:	4501                	li	a0,0
      b8:	0015f693          	andi	a3,a1,1
      bc:	c291                	beqz	a3,c0 <__mulsi3+0xc>
      be:	9532                	add	a0,a0,a2
      c0:	8185                	srli	a1,a1,0x1
      c2:	0606                	slli	a2,a2,0x1
      c4:	f9f5                	bnez	a1,b8 <__mulsi3+0x4>
      c6:	8082                	ret
      c8:	0000                	unimp
	...

000000cc <__divsi3>:
      cc:	02054e63          	bltz	a0,108 <__umodsi3+0x8>
      d0:	0405c363          	bltz	a1,116 <__umodsi3+0x16>

000000d4 <__udivsi3>:
      d4:	862e                	mv	a2,a1
      d6:	85aa                	mv	a1,a0
      d8:	557d                	li	a0,-1
      da:	c215                	beqz	a2,fe <__udivsi3+0x2a>
      dc:	4685                	li	a3,1
      de:	00b67863          	bgeu	a2,a1,ee <__udivsi3+0x1a>
      e2:	00c05663          	blez	a2,ee <__udivsi3+0x1a>
      e6:	0606                	slli	a2,a2,0x1
      e8:	0686                	slli	a3,a3,0x1
      ea:	feb66ce3          	bltu	a2,a1,e2 <__udivsi3+0xe>
      ee:	4501                	li	a0,0
      f0:	00c5e463          	bltu	a1,a2,f8 <__udivsi3+0x24>
      f4:	8d91                	sub	a1,a1,a2
      f6:	8d55                	or	a0,a0,a3
      f8:	8285                	srli	a3,a3,0x1
      fa:	8205                	srli	a2,a2,0x1
      fc:	faf5                	bnez	a3,f0 <__udivsi3+0x1c>
      fe:	8082                	ret

00000100 <__umodsi3>:
     100:	8286                	mv	t0,ra
     102:	3fc9                	jal	d4 <__udivsi3>
     104:	852e                	mv	a0,a1
     106:	8282                	jr	t0
     108:	40a00533          	neg	a0,a0
     10c:	0005d763          	bgez	a1,11a <__umodsi3+0x1a>
     110:	40b005b3          	neg	a1,a1
     114:	b7c1                	j	d4 <__udivsi3>
     116:	40b005b3          	neg	a1,a1
     11a:	8286                	mv	t0,ra
     11c:	3f65                	jal	d4 <__udivsi3>
     11e:	40a00533          	neg	a0,a0
     122:	8282                	jr	t0

00000124 <__modsi3>:
     124:	8286                	mv	t0,ra
     126:	0005c763          	bltz	a1,134 <__modsi3+0x10>
     12a:	00054963          	bltz	a0,13c <__modsi3+0x18>
     12e:	375d                	jal	d4 <__udivsi3>
     130:	852e                	mv	a0,a1
     132:	8282                	jr	t0
     134:	40b005b3          	neg	a1,a1
     138:	fe055be3          	bgez	a0,12e <__modsi3+0xa>
     13c:	40a00533          	neg	a0,a0
     140:	3f51                	jal	d4 <__udivsi3>
     142:	40b00533          	neg	a0,a1
     146:	8282                	jr	t0
	...

0000014a <ADC_Display_Format_Voltage.part.0>:
     14a:	f57ff2ef          	jal	t0,a0 <__riscv_save_0>
     14e:	1161                	addi	sp,sp,-8
     150:	842e                	mv	s0,a1
     152:	3e800593          	li	a1,1000
     156:	84b2                	mv	s1,a2
     158:	c22a                	sw	a0,4(sp)
     15a:	375d                	jal	100 <__umodsi3>
     15c:	0542                	slli	a0,a0,0x10
     15e:	45a9                	li	a1,10
     160:	8141                	srli	a0,a0,0x10
     162:	3f8d                	jal	d4 <__udivsi3>
     164:	4792                	lw	a5,4(sp)
     166:	01051713          	slli	a4,a0,0x10
     16a:	8341                	srli	a4,a4,0x10
     16c:	853e                	mv	a0,a5
     16e:	3e800593          	li	a1,1000
     172:	c03a                	sw	a4,0(sp)
     174:	3785                	jal	d4 <__udivsi3>
     176:	4702                	lw	a4,0(sp)
     178:	01051693          	slli	a3,a0,0x10
     17c:	00002637          	lui	a2,0x2
     180:	82c1                	srli	a3,a3,0x10
     182:	fe860613          	addi	a2,a2,-24 # 1fe8 <memcpy+0x132>
     186:	85a6                	mv	a1,s1
     188:	8522                	mv	a0,s0
     18a:	4b9010ef          	jal	ra,1e42 <snprintf>
     18e:	0121                	addi	sp,sp,8
     190:	bf29                	j	aa <__riscv_restore_0>

00000192 <ADC_Display_Draw_Header>:
     192:	f0fff2ef          	jal	t0,a0 <__riscv_save_0>
     196:	6541                	lui	a0,0x10
     198:	157d                	addi	a0,a0,-1
     19a:	297000ef          	jal	ra,c30 <tft_set_color>
     19e:	4501                	li	a0,0
     1a0:	29b000ef          	jal	ra,c3a <tft_set_background_color>
     1a4:	4581                	li	a1,0
     1a6:	4515                	li	a0,5
     1a8:	27b000ef          	jal	ra,c22 <tft_set_cursor>
     1ac:	00002537          	lui	a0,0x2
     1b0:	fb050513          	addi	a0,a0,-80 # 1fb0 <memcpy+0xfa>
     1b4:	371000ef          	jal	ra,d24 <tft_print>
     1b8:	bdcd                	j	aa <__riscv_restore_0>

000001ba <ADC_Display_Draw_Channel_Labels>:
     1ba:	ee7ff2ef          	jal	t0,a0 <__riscv_save_0>
     1be:	1171                	addi	sp,sp,-4
     1c0:	4501                	li	a0,0
     1c2:	279000ef          	jal	ra,c3a <tft_set_background_color>
     1c6:	6789                	lui	a5,0x2
     1c8:	ff478793          	addi	a5,a5,-12 # 1ff4 <CSWTCH.3>
     1cc:	4451                	li	s0,20
     1ce:	4485                	li	s1,1
     1d0:	238a                	lhu	a0,0(a5)
     1d2:	c03e                	sw	a5,0(sp)
     1d4:	25d000ef          	jal	ra,c30 <tft_set_color>
     1d8:	85a2                	mv	a1,s0
     1da:	4515                	li	a0,5
     1dc:	247000ef          	jal	ra,c22 <tft_set_cursor>
     1e0:	000027b7          	lui	a5,0x2
     1e4:	fa878513          	addi	a0,a5,-88 # 1fa8 <memcpy+0xf2>
     1e8:	33d000ef          	jal	ra,d24 <tft_print>
     1ec:	8526                	mv	a0,s1
     1ee:	4585                	li	a1,1
     1f0:	355000ef          	jal	ra,d44 <tft_print_number>
     1f4:	00002537          	lui	a0,0x2
     1f8:	fac50513          	addi	a0,a0,-84 # 1fac <memcpy+0xf6>
     1fc:	329000ef          	jal	ra,d24 <tft_print>
     200:	4782                	lw	a5,0(sp)
     202:	0449                	addi	s0,s0,18
     204:	0442                	slli	s0,s0,0x10
     206:	0485                	addi	s1,s1,1
     208:	4695                	li	a3,5
     20a:	0789                	addi	a5,a5,2
     20c:	8041                	srli	s0,s0,0x10
     20e:	fcd491e3          	bne	s1,a3,1d0 <ADC_Display_Draw_Channel_Labels+0x16>
     212:	0111                	addi	sp,sp,4
     214:	bd59                	j	aa <__riscv_restore_0>

00000216 <ADC_Display_Init>:
     216:	e8bff2ef          	jal	t0,a0 <__riscv_save_0>
     21a:	200007b7          	lui	a5,0x20000
     21e:	02010737          	lui	a4,0x2010
     222:	04078793          	addi	a5,a5,64 # 20000040 <_edata>
     226:	10070713          	addi	a4,a4,256 # 2010100 <_data_lma+0x200da38>
     22a:	c398                	sw	a4,0(a5)
     22c:	06400713          	li	a4,100
     230:	a3da                	sh	a4,4(a5)
     232:	0007a423          	sw	zero,8(a5)
     236:	3fb1                	jal	192 <ADC_Display_Draw_Header>
     238:	3749                	jal	1ba <ADC_Display_Draw_Channel_Labels>
     23a:	bd85                	j	aa <__riscv_restore_0>

0000023c <ADC_Display_Draw_Logo_Border>:
     23c:	e65ff2ef          	jal	t0,a0 <__riscv_save_0>
     240:	4a000713          	li	a4,1184
     244:	05000693          	li	a3,80
     248:	0a000613          	li	a2,160
     24c:	4581                	li	a1,0
     24e:	4501                	li	a0,0
     250:	39f000ef          	jal	ra,dee <tft_fill_rect>
     254:	4701                	li	a4,0
     256:	04a00693          	li	a3,74
     25a:	09a00613          	li	a2,154
     25e:	458d                	li	a1,3
     260:	450d                	li	a0,3
     262:	38d000ef          	jal	ra,dee <tft_fill_rect>
     266:	7ff00713          	li	a4,2047
     26a:	4685                	li	a3,1
     26c:	09600613          	li	a2,150
     270:	4595                	li	a1,5
     272:	4515                	li	a0,5
     274:	37b000ef          	jal	ra,dee <tft_fill_rect>
     278:	7ff00713          	li	a4,2047
     27c:	4685                	li	a3,1
     27e:	09600613          	li	a2,150
     282:	04a00593          	li	a1,74
     286:	4515                	li	a0,5
     288:	367000ef          	jal	ra,dee <tft_fill_rect>
     28c:	7ff00713          	li	a4,2047
     290:	04600693          	li	a3,70
     294:	4605                	li	a2,1
     296:	4595                	li	a1,5
     298:	4515                	li	a0,5
     29a:	355000ef          	jal	ra,dee <tft_fill_rect>
     29e:	7ff00713          	li	a4,2047
     2a2:	04600693          	li	a3,70
     2a6:	4605                	li	a2,1
     2a8:	4595                	li	a1,5
     2aa:	09a00513          	li	a0,154
     2ae:	341000ef          	jal	ra,dee <tft_fill_rect>
     2b2:	bbe5                	j	aa <__riscv_restore_0>

000002b4 <ADC_Display_Draw_Logo_Header>:
     2b4:	dedff2ef          	jal	t0,a0 <__riscv_save_0>
     2b8:	7ff00513          	li	a0,2047
     2bc:	175000ef          	jal	ra,c30 <tft_set_color>
     2c0:	4501                	li	a0,0
     2c2:	179000ef          	jal	ra,c3a <tft_set_background_color>
     2c6:	45a9                	li	a1,10
     2c8:	4529                	li	a0,10
     2ca:	159000ef          	jal	ra,c22 <tft_set_cursor>
     2ce:	00002537          	lui	a0,0x2
     2d2:	fcc50513          	addi	a0,a0,-52 # 1fcc <memcpy+0x116>
     2d6:	24f000ef          	jal	ra,d24 <tft_print>
     2da:	45d1                	li	a1,20
     2dc:	4529                	li	a0,10
     2de:	145000ef          	jal	ra,c22 <tft_set_cursor>
     2e2:	00002537          	lui	a0,0x2
     2e6:	fd450513          	addi	a0,a0,-44 # 1fd4 <memcpy+0x11e>
     2ea:	23b000ef          	jal	ra,d24 <tft_print>
     2ee:	7ff00713          	li	a4,2047
     2f2:	4685                	li	a3,1
     2f4:	4679                	li	a2,30
     2f6:	45b1                	li	a1,12
     2f8:	03c00513          	li	a0,60
     2fc:	2f3000ef          	jal	ra,dee <tft_fill_rect>
     300:	7ff00713          	li	a4,2047
     304:	4685                	li	a3,1
     306:	4665                	li	a2,25
     308:	45bd                	li	a1,15
     30a:	03c00513          	li	a0,60
     30e:	2e1000ef          	jal	ra,dee <tft_fill_rect>
     312:	7ff00713          	li	a4,2047
     316:	4685                	li	a3,1
     318:	4651                	li	a2,20
     31a:	45c9                	li	a1,18
     31c:	03c00513          	li	a0,60
     320:	2cf000ef          	jal	ra,dee <tft_fill_rect>
     324:	b359                	j	aa <__riscv_restore_0>

00000326 <ADC_Display_Draw_Logo_Channels>:
     326:	c151                	beqz	a0,3aa <ADC_Display_Draw_Logo_Channels+0x84>
     328:	d79ff2ef          	jal	t0,a0 <__riscv_save_0>
     32c:	1141                	addi	sp,sp,-16
     32e:	c02a                	sw	a0,0(sp)
     330:	7ff00513          	li	a0,2047
     334:	0fd000ef          	jal	ra,c30 <tft_set_color>
     338:	4501                	li	a0,0
     33a:	101000ef          	jal	ra,c3a <tft_set_background_color>
     33e:	4405                	li	s0,1
     340:	000024b7          	lui	s1,0x2
     344:	00241793          	slli	a5,s0,0x2
     348:	97a2                	add	a5,a5,s0
     34a:	0786                	slli	a5,a5,0x1
     34c:	07e5                	addi	a5,a5,25
     34e:	07c2                	slli	a5,a5,0x10
     350:	83c1                	srli	a5,a5,0x10
     352:	85be                	mv	a1,a5
     354:	4529                	li	a0,10
     356:	c23e                	sw	a5,4(sp)
     358:	0cb000ef          	jal	ra,c22 <tft_set_cursor>
     35c:	86a2                	mv	a3,s0
     35e:	fbc48613          	addi	a2,s1,-68 # 1fbc <memcpy+0x106>
     362:	45a1                	li	a1,8
     364:	0028                	addi	a0,sp,8
     366:	2dd010ef          	jal	ra,1e42 <snprintf>
     36a:	0028                	addi	a0,sp,8
     36c:	1b9000ef          	jal	ra,d24 <tft_print>
     370:	4792                	lw	a5,4(sp)
     372:	02300513          	li	a0,35
     376:	85be                	mv	a1,a5
     378:	0ab000ef          	jal	ra,c22 <tft_set_cursor>
     37c:	4782                	lw	a5,0(sp)
     37e:	97a2                	add	a5,a5,s0
     380:	37f4                	lbu	a3,15(a5)
     382:	ce99                	beqz	a3,3a0 <ADC_Display_Draw_Logo_Channels+0x7a>
     384:	97a2                	add	a5,a5,s0
     386:	23ea                	lhu	a0,6(a5)
     388:	4621                	li	a2,8
     38a:	002c                	addi	a1,sp,8
     38c:	3b7d                	jal	14a <ADC_Display_Format_Voltage.part.0>
     38e:	0028                	addi	a0,sp,8
     390:	195000ef          	jal	ra,d24 <tft_print>
     394:	0405                	addi	s0,s0,1
     396:	4795                	li	a5,5
     398:	faf416e3          	bne	s0,a5,344 <ADC_Display_Draw_Logo_Channels+0x1e>
     39c:	0141                	addi	sp,sp,16
     39e:	b331                	j	aa <__riscv_restore_0>
     3a0:	00002537          	lui	a0,0x2
     3a4:	fc450513          	addi	a0,a0,-60 # 1fc4 <memcpy+0x10e>
     3a8:	b7e5                	j	390 <ADC_Display_Draw_Logo_Channels+0x6a>
     3aa:	8082                	ret

000003ac <ADC_Display_Draw_Logo_Main_Voltage>:
     3ac:	cf5ff2ef          	jal	t0,a0 <__riscv_save_0>
     3b0:	842a                	mv	s0,a0
     3b2:	6541                	lui	a0,0x10
     3b4:	1141                	addi	sp,sp,-16
     3b6:	157d                	addi	a0,a0,-1
     3b8:	079000ef          	jal	ra,c30 <tft_set_color>
     3bc:	4501                	li	a0,0
     3be:	07d000ef          	jal	ra,c3a <tft_set_background_color>
     3c2:	3e800593          	li	a1,1000
     3c6:	8522                	mv	a0,s0
     3c8:	3b25                	jal	100 <__umodsi3>
     3ca:	01051713          	slli	a4,a0,0x10
     3ce:	8341                	srli	a4,a4,0x10
     3d0:	3e800593          	li	a1,1000
     3d4:	8522                	mv	a0,s0
     3d6:	c03a                	sw	a4,0(sp)
     3d8:	39f5                	jal	d4 <__udivsi3>
     3da:	4702                	lw	a4,0(sp)
     3dc:	01051693          	slli	a3,a0,0x10
     3e0:	00002637          	lui	a2,0x2
     3e4:	82c1                	srli	a3,a3,0x10
     3e6:	fdc60613          	addi	a2,a2,-36 # 1fdc <memcpy+0x126>
     3ea:	45b1                	li	a1,12
     3ec:	0048                	addi	a0,sp,4
     3ee:	255010ef          	jal	ra,1e42 <snprintf>
     3f2:	02d00593          	li	a1,45
     3f6:	06400513          	li	a0,100
     3fa:	029000ef          	jal	ra,c22 <tft_set_cursor>
     3fe:	0048                	addi	a0,sp,4
     400:	125000ef          	jal	ra,d24 <tft_print>
     404:	7ff00713          	li	a4,2047
     408:	4689                	li	a3,2
     40a:	4609                	li	a2,2
     40c:	03200593          	li	a1,50
     410:	09100513          	li	a0,145
     414:	1db000ef          	jal	ra,dee <tft_fill_rect>
     418:	0141                	addi	sp,sp,16
     41a:	b941                	j	aa <__riscv_restore_0>

0000041c <ADC_Display_Draw_Logo_Style>:
     41c:	c85ff2ef          	jal	t0,a0 <__riscv_save_0>
     420:	842a                	mv	s0,a0
     422:	4a000713          	li	a4,1184
     426:	4501                	li	a0,0
     428:	05000693          	li	a3,80
     42c:	0a000613          	li	a2,160
     430:	4581                	li	a1,0
     432:	1bd000ef          	jal	ra,dee <tft_fill_rect>
     436:	3519                	jal	23c <ADC_Display_Draw_Logo_Border>
     438:	3db5                	jal	2b4 <ADC_Display_Draw_Logo_Header>
     43a:	8522                	mv	a0,s0
     43c:	35ed                	jal	326 <ADC_Display_Draw_Logo_Channels>
     43e:	c409                	beqz	s0,448 <ADC_Display_Draw_Logo_Style+0x2c>
     440:	281c                	lbu	a5,16(s0)
     442:	c399                	beqz	a5,448 <ADC_Display_Draw_Logo_Style+0x2c>
     444:	240a                	lhu	a0,8(s0)
     446:	379d                	jal	3ac <ADC_Display_Draw_Logo_Main_Voltage>
     448:	b18d                	j	aa <__riscv_restore_0>

0000044a <NMI_Handler>:
     44a:	a001                	j	44a <NMI_Handler>

0000044c <HardFault_Handler>:
     44c:	beef07b7          	lui	a5,0xbeef0
     450:	e000e737          	lui	a4,0xe000e
     454:	08078793          	addi	a5,a5,128 # beef0080 <__global_pointer$+0x9eeef840>
     458:	c73c                	sw	a5,72(a4)
     45a:	a001                	j	45a <HardFault_Handler+0xe>

0000045c <EXTI7_0_IRQHandler>:
     45c:	531000ef          	jal	ra,118c <Touch_Button_IRQ_Handler>
     460:	30200073          	mret

00000464 <TIM2_IRQHandler>:
     464:	4585                	li	a1,1
     466:	40000537          	lui	a0,0x40000
     46a:	1da010ef          	jal	ra,1644 <TIM_GetITStatus>
     46e:	c919                	beqz	a0,484 <TIM2_IRQHandler+0x20>
     470:	9741a783          	lw	a5,-1676(gp) # 200001b4 <system_tick_ms>
     474:	4585                	li	a1,1
     476:	40000537          	lui	a0,0x40000
     47a:	0785                	addi	a5,a5,1
     47c:	96f1aa23          	sw	a5,-1676(gp) # 200001b4 <system_tick_ms>
     480:	1dc010ef          	jal	ra,165c <TIM_ClearITPendingBit>
     484:	30200073          	mret

00000488 <Display_Control_Turn_On>:
     488:	80c18793          	addi	a5,gp,-2036 # 2000004c <display_control>
     48c:	4398                	lw	a4,0(a5)
     48e:	e719                	bnez	a4,49c <Display_Control_Turn_On+0x14>
     490:	c11ff2ef          	jal	t0,a0 <__riscv_save_0>
     494:	4705                	li	a4,1
     496:	c398                	sw	a4,0(a5)
     498:	2b11                	jal	9ac <PWM_Turn_On>
     49a:	b901                	j	aa <__riscv_restore_0>
     49c:	8082                	ret

0000049e <Display_Control_Turn_Off>:
     49e:	80c18793          	addi	a5,gp,-2036 # 2000004c <display_control>
     4a2:	4394                	lw	a3,0(a5)
     4a4:	4709                	li	a4,2
     4a6:	00e69863          	bne	a3,a4,4b6 <Display_Control_Turn_Off+0x18>
     4aa:	bf7ff2ef          	jal	t0,a0 <__riscv_save_0>
     4ae:	470d                	li	a4,3
     4b0:	c398                	sw	a4,0(a5)
     4b2:	2329                	jal	9bc <PWM_Turn_Off>
     4b4:	bedd                	j	aa <__riscv_restore_0>
     4b6:	8082                	ret

000004b8 <Display_Control_Toggle>:
     4b8:	be9ff2ef          	jal	t0,a0 <__riscv_save_0>
     4bc:	80c1a783          	lw	a5,-2036(gp) # 2000004c <display_control>
     4c0:	e399                	bnez	a5,4c6 <Display_Control_Toggle+0xe>
     4c2:	37d9                	jal	488 <Display_Control_Turn_On>
     4c4:	b6dd                	j	aa <__riscv_restore_0>
     4c6:	4709                	li	a4,2
     4c8:	fee79ee3          	bne	a5,a4,4c4 <Display_Control_Toggle+0xc>
     4cc:	3fc9                	jal	49e <Display_Control_Turn_Off>
     4ce:	bfdd                	j	4c4 <Display_Control_Toggle+0xc>

000004d0 <Display_Control_Clear_Screen>:
     4d0:	bd1ff2ef          	jal	t0,a0 <__riscv_save_0>
     4d4:	4701                	li	a4,0
     4d6:	05000693          	li	a3,80
     4da:	0a000613          	li	a2,160
     4de:	4581                	li	a1,0
     4e0:	4501                	li	a0,0
     4e2:	10d000ef          	jal	ra,dee <tft_fill_rect>
     4e6:	b6d1                	j	aa <__riscv_restore_0>

000004e8 <Display_Control_Show_Startup_Message>:
     4e8:	bb9ff2ef          	jal	t0,a0 <__riscv_save_0>
     4ec:	37d5                	jal	4d0 <Display_Control_Clear_Screen>
     4ee:	6441                	lui	s0,0x10
     4f0:	fff40513          	addi	a0,s0,-1 # ffff <_data_lma+0xd937>
     4f4:	73c000ef          	jal	ra,c30 <tft_set_color>
     4f8:	4501                	li	a0,0
     4fa:	740000ef          	jal	ra,c3a <tft_set_background_color>
     4fe:	45a9                	li	a1,10
     500:	4529                	li	a0,10
     502:	720000ef          	jal	ra,c22 <tft_set_cursor>
     506:	00002537          	lui	a0,0x2
     50a:	00850513          	addi	a0,a0,8 # 2008 <CSWTCH.3+0x14>
     50e:	017000ef          	jal	ra,d24 <tft_print>
     512:	45e5                	li	a1,25
     514:	4529                	li	a0,10
     516:	70c000ef          	jal	ra,c22 <tft_set_cursor>
     51a:	00002537          	lui	a0,0x2
     51e:	01850513          	addi	a0,a0,24 # 2018 <CSWTCH.3+0x24>
     522:	003000ef          	jal	ra,d24 <tft_print>
     526:	02d00593          	li	a1,45
     52a:	4515                	li	a0,5
     52c:	6f6000ef          	jal	ra,c22 <tft_set_cursor>
     530:	fe040513          	addi	a0,s0,-32
     534:	6fc000ef          	jal	ra,c30 <tft_set_color>
     538:	00002537          	lui	a0,0x2
     53c:	02850513          	addi	a0,a0,40 # 2028 <CSWTCH.3+0x34>
     540:	7e4000ef          	jal	ra,d24 <tft_print>
     544:	03c00593          	li	a1,60
     548:	4515                	li	a0,5
     54a:	6d8000ef          	jal	ra,c22 <tft_set_cursor>
     54e:	00002537          	lui	a0,0x2
     552:	03850513          	addi	a0,a0,56 # 2038 <CSWTCH.3+0x44>
     556:	7ce000ef          	jal	ra,d24 <tft_print>
     55a:	be81                	j	aa <__riscv_restore_0>

0000055c <Display_Control_Init>:
     55c:	b45ff2ef          	jal	t0,a0 <__riscv_save_0>
     560:	80c18413          	addi	s0,gp,-2036 # 2000004c <display_control>
     564:	10000793          	li	a5,256
     568:	a05e                	sh	a5,4(s0)
     56a:	00042023          	sw	zero,0(s0)
     56e:	00042423          	sw	zero,8(s0)
     572:	2305                	jal	a92 <tft_init>
     574:	3fb1                	jal	4d0 <Display_Control_Clear_Screen>
     576:	3f8d                	jal	4e8 <Display_Control_Show_Startup_Message>
     578:	4785                	li	a5,1
     57a:	a05c                	sb	a5,4(s0)
     57c:	2181                	jal	9bc <PWM_Turn_Off>
     57e:	b635                	j	aa <__riscv_restore_0>

00000580 <Display_Control_Show_Off_Message>:
     580:	b21ff2ef          	jal	t0,a0 <__riscv_save_0>
     584:	37b1                	jal	4d0 <Display_Control_Clear_Screen>
     586:	6521                	lui	a0,0x8
     588:	bef50513          	addi	a0,a0,-1041 # 7bef <_data_lma+0x5527>
     58c:	6a4000ef          	jal	ra,c30 <tft_set_color>
     590:	4501                	li	a0,0
     592:	6a8000ef          	jal	ra,c3a <tft_set_background_color>
     596:	02300593          	li	a1,35
     59a:	4551                	li	a0,20
     59c:	686000ef          	jal	ra,c22 <tft_set_cursor>
     5a0:	00002537          	lui	a0,0x2
     5a4:	ffc50513          	addi	a0,a0,-4 # 1ffc <CSWTCH.3+0x8>
     5a8:	77c000ef          	jal	ra,d24 <tft_print>
     5ac:	bcfd                	j	aa <__riscv_restore_0>

000005ae <Display_Control_Update>:
     5ae:	af3ff2ef          	jal	t0,a0 <__riscv_save_0>
     5b2:	80c18413          	addi	s0,gp,-2036 # 2000004c <display_control>
     5b6:	205c                	lbu	a5,4(s0)
     5b8:	cb89                	beqz	a5,5ca <Display_Control_Update+0x1c>
     5ba:	2645                	jal	95a <PWM_Update_Fade>
     5bc:	401c                	lw	a5,0(s0)
     5be:	4705                	li	a4,1
     5c0:	00e78663          	beq	a5,a4,5cc <Display_Control_Update+0x1e>
     5c4:	470d                	li	a4,3
     5c6:	00e78a63          	beq	a5,a4,5da <Display_Control_Update+0x2c>
     5ca:	b4c5                	j	aa <__riscv_restore_0>
     5cc:	8201c703          	lbu	a4,-2016(gp) # 20000060 <pwm_control+0x4>
     5d0:	ff6d                	bnez	a4,5ca <Display_Control_Update+0x1c>
     5d2:	4709                	li	a4,2
     5d4:	c018                	sw	a4,0(s0)
     5d6:	b05c                	sb	a5,5(s0)
     5d8:	bfcd                	j	5ca <Display_Control_Update+0x1c>
     5da:	8201c783          	lbu	a5,-2016(gp) # 20000060 <pwm_control+0x4>
     5de:	f7f5                	bnez	a5,5ca <Display_Control_Update+0x1c>
     5e0:	26ed                	jal	9ca <PWM_Get_Brightness>
     5e2:	f565                	bnez	a0,5ca <Display_Control_Update+0x1c>
     5e4:	00042023          	sw	zero,0(s0)
     5e8:	3f61                	jal	580 <Display_Control_Show_Off_Message>
     5ea:	b7c5                	j	5ca <Display_Control_Update+0x1c>

000005ec <Display_Text_Custom>:
     5ec:	ab5ff2ef          	jal	t0,a0 <__riscv_save_0>
     5f0:	1151                	addi	sp,sp,-12
     5f2:	842a                	mv	s0,a0
     5f4:	8536                	mv	a0,a3
     5f6:	c43e                	sw	a5,8(sp)
     5f8:	c02e                	sw	a1,0(sp)
     5fa:	84b2                	mv	s1,a2
     5fc:	c23a                	sw	a4,4(sp)
     5fe:	632000ef          	jal	ra,c30 <tft_set_color>
     602:	4712                	lw	a4,4(sp)
     604:	853a                	mv	a0,a4
     606:	634000ef          	jal	ra,c3a <tft_set_background_color>
     60a:	47a2                	lw	a5,8(sp)
     60c:	4712                	lw	a4,4(sp)
     60e:	cb89                	beqz	a5,620 <Display_Text_Custom+0x34>
     610:	05000693          	li	a3,80
     614:	0a000613          	li	a2,160
     618:	4581                	li	a1,0
     61a:	4501                	li	a0,0
     61c:	7d2000ef          	jal	ra,dee <tft_fill_rect>
     620:	4502                	lw	a0,0(sp)
     622:	85a6                	mv	a1,s1
     624:	5fe000ef          	jal	ra,c22 <tft_set_cursor>
     628:	8522                	mv	a0,s0
     62a:	6fa000ef          	jal	ra,d24 <tft_print>
     62e:	0131                	addi	sp,sp,12
     630:	bcad                	j	aa <__riscv_restore_0>

00000632 <Display_Text_Centered>:
     632:	a6fff2ef          	jal	t0,a0 <__riscv_save_0>
     636:	87ba                	mv	a5,a4
     638:	4301                	li	t1,0
     63a:	00130293          	addi	t0,t1,1
     63e:	00550733          	add	a4,a0,t0
     642:	fff70703          	lb	a4,-1(a4) # e000dfff <__global_pointer$+0xc000d7bf>
     646:	e71d                	bnez	a4,674 <Display_Text_Centered+0x42>
     648:	0342                	slli	t1,t1,0x10
     64a:	01035313          	srli	t1,t1,0x10
     64e:	00231713          	slli	a4,t1,0x2
     652:	40e30333          	sub	t1,t1,a4
     656:	05030313          	addi	t1,t1,80
     65a:	0342                	slli	t1,t1,0x10
     65c:	01035313          	srli	t1,t1,0x10
     660:	e199                	bnez	a1,666 <Display_Text_Centered+0x34>
     662:	02400593          	li	a1,36
     666:	8736                	mv	a4,a3
     668:	86b2                	mv	a3,a2
     66a:	862e                	mv	a2,a1
     66c:	859a                	mv	a1,t1
     66e:	3fbd                	jal	5ec <Display_Text_Custom>
     670:	a3bff06f          	j	aa <__riscv_restore_0>
     674:	8316                	mv	t1,t0
     676:	b7d1                	j	63a <Display_Text_Centered+0x8>

00000678 <Display_Text_Clear_Screen>:
     678:	a29ff2ef          	jal	t0,a0 <__riscv_save_0>
     67c:	872a                	mv	a4,a0
     67e:	05000693          	li	a3,80
     682:	0a000613          	li	a2,160
     686:	4581                	li	a1,0
     688:	4501                	li	a0,0
     68a:	764000ef          	jal	ra,dee <tft_fill_rect>
     68e:	a1dff06f          	j	aa <__riscv_restore_0>

00000692 <Display_Text_Welcome>:
     692:	a0fff2ef          	jal	t0,a0 <__riscv_save_0>
     696:	4501                	li	a0,0
     698:	37c5                	jal	678 <Display_Text_Clear_Screen>
     69a:	00002537          	lui	a0,0x2
     69e:	4701                	li	a4,0
     6a0:	4681                	li	a3,0
     6a2:	7ff00613          	li	a2,2047
     6a6:	45d1                	li	a1,20
     6a8:	05050513          	addi	a0,a0,80 # 2050 <CSWTCH.3+0x5c>
     6ac:	3759                	jal	632 <Display_Text_Centered>
     6ae:	6441                	lui	s0,0x10
     6b0:	00002537          	lui	a0,0x2
     6b4:	fff40613          	addi	a2,s0,-1 # ffff <_data_lma+0xd937>
     6b8:	4701                	li	a4,0
     6ba:	4681                	li	a3,0
     6bc:	02300593          	li	a1,35
     6c0:	fb050513          	addi	a0,a0,-80 # 1fb0 <memcpy+0xfa>
     6c4:	37bd                	jal	632 <Display_Text_Centered>
     6c6:	00002537          	lui	a0,0x2
     6ca:	4701                	li	a4,0
     6cc:	4681                	li	a3,0
     6ce:	fe040613          	addi	a2,s0,-32
     6d2:	03200593          	li	a1,50
     6d6:	04850513          	addi	a0,a0,72 # 2048 <CSWTCH.3+0x54>
     6da:	3fa1                	jal	632 <Display_Text_Centered>
     6dc:	9cfff06f          	j	aa <__riscv_restore_0>

000006e0 <System_Init>:
     6e0:	9c1ff2ef          	jal	t0,a0 <__riscv_save_0>
     6e4:	00002537          	lui	a0,0x2
     6e8:	05c50513          	addi	a0,a0,92 # 205c <CSWTCH.3+0x68>
     6ec:	780010ef          	jal	ra,1e6c <puts>
     6f0:	24a1                	jal	938 <PWM_Config_Init>
     6f2:	00002537          	lui	a0,0x2
     6f6:	07050513          	addi	a0,a0,112 # 2070 <CSWTCH.3+0x7c>
     6fa:	772010ef          	jal	ra,1e6c <puts>
     6fe:	1e7000ef          	jal	ra,10e4 <Touch_Button_Init>
     702:	00002537          	lui	a0,0x2
     706:	08450513          	addi	a0,a0,132 # 2084 <CSWTCH.3+0x90>
     70a:	762010ef          	jal	ra,1e6c <puts>
     70e:	35b9                	jal	55c <Display_Control_Init>
     710:	00002537          	lui	a0,0x2
     714:	0a050513          	addi	a0,a0,160 # 20a0 <CSWTCH.3+0xac>
     718:	754010ef          	jal	ra,1e6c <puts>
     71c:	3ced                	jal	216 <ADC_Display_Init>
     71e:	00002537          	lui	a0,0x2
     722:	0c050513          	addi	a0,a0,192 # 20c0 <CSWTCH.3+0xcc>
     726:	746010ef          	jal	ra,1e6c <puts>
     72a:	00002537          	lui	a0,0x2
     72e:	4705                	li	a4,1
     730:	0dc50513          	addi	a0,a0,220 # 20dc <CSWTCH.3+0xe8>
     734:	80e18c23          	sb	a4,-2024(gp) # 20000058 <system_initialized>
     738:	734010ef          	jal	ra,1e6c <puts>
     73c:	96fff06f          	j	aa <__riscv_restore_0>

00000740 <main>:
     740:	961ff2ef          	jal	t0,a0 <__riscv_save_0>
     744:	1121                	addi	sp,sp,-24
     746:	4505                	li	a0,1
     748:	481000ef          	jal	ra,13c8 <NVIC_PriorityGroupConfig>
     74c:	065000ef          	jal	ra,fb0 <SystemCoreClockUpdate>
     750:	00e010ef          	jal	ra,175e <Delay_Init>
     754:	6571                	lui	a0,0x1c
     756:	20050513          	addi	a0,a0,512 # 1c200 <_data_lma+0x19b38>
     75a:	072010ef          	jal	ra,17cc <USART_Printf_Init>
     75e:	00002537          	lui	a0,0x2
     762:	0fc50513          	addi	a0,a0,252 # 20fc <CSWTCH.3+0x108>
     766:	706010ef          	jal	ra,1e6c <puts>
     76a:	200007b7          	lui	a5,0x20000
     76e:	0147a583          	lw	a1,20(a5) # 20000014 <SystemCoreClock>
     772:	00002537          	lui	a0,0x2
     776:	11c50513          	addi	a0,a0,284 # 211c <CSWTCH.3+0x128>
     77a:	6a4010ef          	jal	ra,1e1e <printf>
     77e:	307000ef          	jal	ra,1284 <DBGMCU_GetCHIPID>
     782:	85aa                	mv	a1,a0
     784:	00002537          	lui	a0,0x2
     788:	13050513          	addi	a0,a0,304 # 2130 <CSWTCH.3+0x13c>
     78c:	692010ef          	jal	ra,1e1e <printf>
     790:	3f81                	jal	6e0 <System_Init>
     792:	3701                	jal	692 <Display_Text_Welcome>
     794:	7d000513          	li	a0,2000
     798:	7fb000ef          	jal	ra,1792 <Delay_Ms>
     79c:	0ce447b7          	lui	a5,0xce44
     7a0:	ead78793          	addi	a5,a5,-339 # ce43ead <_data_lma+0xce417e5>
     7a4:	c63e                	sw	a5,12(sp)
     7a6:	138807b7          	lui	a5,0x13880
     7aa:	67278793          	addi	a5,a5,1650 # 13880672 <_data_lma+0x1387dfaa>
     7ae:	c83e                	sw	a5,16(sp)
     7b0:	0044                	addi	s1,sp,4
     7b2:	6785                	lui	a5,0x1
     7b4:	c202                	sw	zero,4(sp)
     7b6:	c402                	sw	zero,8(sp)
     7b8:	ca02                	sw	zero,20(sp)
     7ba:	8426                	mv	s0,s1
     7bc:	4605                	li	a2,1
     7be:	ce478793          	addi	a5,a5,-796 # ce4 <tft_print_char+0xa4>
     7c2:	a890                	sb	a2,16(s1)
     7c4:	240a                	lhu	a0,8(s0)
     7c6:	85be                	mv	a1,a5
     7c8:	c032                	sw	a2,0(sp)
     7ca:	0532                	slli	a0,a0,0xc
     7cc:	901ff0ef          	jal	ra,cc <__divsi3>
     7d0:	a00a                	sh	a0,0(s0)
     7d2:	6785                	lui	a5,0x1
     7d4:	0409                	addi	s0,s0,2
     7d6:	0074                	addi	a3,sp,12
     7d8:	0485                	addi	s1,s1,1
     7da:	ce478793          	addi	a5,a5,-796 # ce4 <tft_print_char+0xa4>
     7de:	4602                	lw	a2,0(sp)
     7e0:	0058                	addi	a4,sp,4
     7e2:	fed410e3          	bne	s0,a3,7c2 <main+0x82>
     7e6:	853a                	mv	a0,a4
     7e8:	3915                	jal	41c <ADC_Display_Draw_Logo_Style>
     7ea:	00002437          	lui	s0,0x2
     7ee:	121000ef          	jal	ra,110e <Touch_Button_Update>
     7f2:	18f000ef          	jal	ra,1180 <Touch_Button_Get_Event>
     7f6:	4789                	li	a5,2
     7f8:	02f50463          	beq	a0,a5,820 <main+0xe0>
     7fc:	478d                	li	a5,3
     7fe:	02f50763          	beq	a0,a5,82c <main+0xec>
     802:	4785                	li	a5,1
     804:	00f51963          	bne	a0,a5,816 <main+0xd6>
     808:	00002537          	lui	a0,0x2
     80c:	14050513          	addi	a0,a0,320 # 2140 <CSWTCH.3+0x14c>
     810:	65c010ef          	jal	ra,1e6c <puts>
     814:	3995                	jal	488 <Display_Control_Turn_On>
     816:	3b61                	jal	5ae <Display_Control_Update>
     818:	4505                	li	a0,1
     81a:	779000ef          	jal	ra,1792 <Delay_Ms>
     81e:	bfc1                	j	7ee <main+0xae>
     820:	16c40513          	addi	a0,s0,364 # 216c <CSWTCH.3+0x178>
     824:	648010ef          	jal	ra,1e6c <puts>
     828:	3941                	jal	4b8 <Display_Control_Toggle>
     82a:	b7f5                	j	816 <main+0xd6>
     82c:	00002537          	lui	a0,0x2
     830:	19850513          	addi	a0,a0,408 # 2198 <CSWTCH.3+0x1a4>
     834:	638010ef          	jal	ra,1e6c <puts>
     838:	319d                	jal	49e <Display_Control_Turn_Off>
     83a:	bff1                	j	816 <main+0xd6>

0000083c <PWM_GPIO_Config>:
     83c:	865ff2ef          	jal	t0,a0 <__riscv_save_0>
     840:	1151                	addi	sp,sp,-12
     842:	4585                	li	a1,1
     844:	02000513          	li	a0,32
     848:	c002                	sw	zero,0(sp)
     84a:	c202                	sw	zero,4(sp)
     84c:	c402                	sw	zero,8(sp)
     84e:	497000ef          	jal	ra,14e4 <RCC_APB2PeriphClockCmd>
     852:	4791                	li	a5,4
     854:	807c                	sh	a5,0(sp)
     856:	40011537          	lui	a0,0x40011
     85a:	47e1                	li	a5,24
     85c:	c43e                	sw	a5,8(sp)
     85e:	858a                	mv	a1,sp
     860:	478d                	li	a5,3
     862:	40050513          	addi	a0,a0,1024 # 40011400 <__global_pointer$+0x20010bc0>
     866:	c23e                	sw	a5,4(sp)
     868:	2b9000ef          	jal	ra,1320 <GPIO_Init>
     86c:	0131                	addi	sp,sp,12
     86e:	83dff06f          	j	aa <__riscv_restore_0>

00000872 <PWM_Timer_Config>:
     872:	82fff2ef          	jal	t0,a0 <__riscv_save_0>
     876:	6505                	lui	a0,0x1
     878:	1111                	addi	sp,sp,-28
     87a:	4585                	li	a1,1
     87c:	80050513          	addi	a0,a0,-2048 # 800 <main+0xc0>
     880:	c002                	sw	zero,0(sp)
     882:	c202                	sw	zero,4(sp)
     884:	00011423          	sh	zero,8(sp)
     888:	c602                	sw	zero,12(sp)
     88a:	c802                	sw	zero,16(sp)
     88c:	ca02                	sw	zero,20(sp)
     88e:	cc02                	sw	zero,24(sp)
     890:	455000ef          	jal	ra,14e4 <RCC_APB2PeriphClockCmd>
     894:	200007b7          	lui	a5,0x20000
     898:	0147a503          	lw	a0,20(a5) # 20000014 <SystemCoreClock>
     89c:	000f45b7          	lui	a1,0xf4
     8a0:	24058593          	addi	a1,a1,576 # f4240 <_data_lma+0xf1b78>
     8a4:	831ff0ef          	jal	ra,d4 <__udivsi3>
     8a8:	40013437          	lui	s0,0x40013
     8ac:	157d                	addi	a0,a0,-1
     8ae:	8068                	sh	a0,0(sp)
     8b0:	3e700793          	li	a5,999
     8b4:	858a                	mv	a1,sp
     8b6:	c0040513          	addi	a0,s0,-1024 # 40012c00 <__global_pointer$+0x200123c0>
     8ba:	c23e                	sw	a5,4(sp)
     8bc:	00011123          	sh	zero,2(sp)
     8c0:	461000ef          	jal	ra,1520 <TIM_TimeBaseInit>
     8c4:	67c1                	lui	a5,0x10
     8c6:	06078793          	addi	a5,a5,96 # 10060 <_data_lma+0xd998>
     8ca:	006c                	addi	a1,sp,12
     8cc:	c0040513          	addi	a0,s0,-1024
     8d0:	c63e                	sw	a5,12(sp)
     8d2:	00011923          	sh	zero,18(sp)
     8d6:	00011a23          	sh	zero,20(sp)
     8da:	495000ef          	jal	ra,156e <TIM_OC1Init>
     8de:	c0040513          	addi	a0,s0,-1024
     8e2:	45a1                	li	a1,8
     8e4:	54f000ef          	jal	ra,1632 <TIM_OC1PreloadConfig>
     8e8:	c0040513          	addi	a0,s0,-1024
     8ec:	4585                	li	a1,1
     8ee:	52b000ef          	jal	ra,1618 <TIM_ARRPreloadConfig>
     8f2:	c0040513          	addi	a0,s0,-1024
     8f6:	4585                	li	a1,1
     8f8:	4e1000ef          	jal	ra,15d8 <TIM_Cmd>
     8fc:	4585                	li	a1,1
     8fe:	c0040513          	addi	a0,s0,-1024
     902:	4ef000ef          	jal	ra,15f0 <TIM_CtrlPWMOutputs>
     906:	0171                	addi	sp,sp,28
     908:	fa2ff06f          	j	aa <__riscv_restore_0>

0000090c <PWM_Set_Brightness>:
     90c:	f94ff2ef          	jal	t0,a0 <__riscv_save_0>
     910:	3e800793          	li	a5,1000
     914:	3e800413          	li	s0,1000
     918:	00a7e363          	bltu	a5,a0,91e <PWM_Set_Brightness+0x12>
     91c:	842a                	mv	s0,a0
     91e:	01041593          	slli	a1,s0,0x10
     922:	40013537          	lui	a0,0x40013
     926:	81c1                	srli	a1,a1,0x10
     928:	c0050513          	addi	a0,a0,-1024 # 40012c00 <__global_pointer$+0x200123c0>
     92c:	515000ef          	jal	ra,1640 <TIM_SetCompare1>
     930:	80819e23          	sh	s0,-2020(gp) # 2000005c <pwm_control>
     934:	f76ff06f          	j	aa <__riscv_restore_0>

00000938 <PWM_Config_Init>:
     938:	f68ff2ef          	jal	t0,a0 <__riscv_save_0>
     93c:	81c18793          	addi	a5,gp,-2020 # 2000005c <pwm_control>
     940:	01f40737          	lui	a4,0x1f40
     944:	c398                	sw	a4,0(a5)
     946:	6705                	lui	a4,0x1
     948:	a0070713          	addi	a4,a4,-1536 # a00 <SPI_send_DMA+0x30>
     94c:	a3da                	sh	a4,4(a5)
     94e:	35fd                	jal	83c <PWM_GPIO_Config>
     950:	370d                	jal	872 <PWM_Timer_Config>
     952:	4501                	li	a0,0
     954:	3f65                	jal	90c <PWM_Set_Brightness>
     956:	f54ff06f          	j	aa <__riscv_restore_0>

0000095a <PWM_Update_Fade>:
     95a:	81c18793          	addi	a5,gp,-2020 # 2000005c <pwm_control>
     95e:	23d8                	lbu	a4,4(a5)
     960:	c729                	beqz	a4,9aa <PWM_Update_Fade+0x50>
     962:	f3eff2ef          	jal	t0,a0 <__riscv_save_0>
     966:	238a                	lhu	a0,0(a5)
     968:	23ba                	lhu	a4,2(a5)
     96a:	00e57e63          	bgeu	a0,a4,986 <PWM_Update_Fade+0x2c>
     96e:	33d4                	lbu	a3,5(a5)
     970:	9536                	add	a0,a0,a3
     972:	0542                	slli	a0,a0,0x10
     974:	8141                	srli	a0,a0,0x10
     976:	00e56563          	bltu	a0,a4,980 <PWM_Update_Fade+0x26>
     97a:	00078223          	sb	zero,4(a5)
     97e:	853a                	mv	a0,a4
     980:	3771                	jal	90c <PWM_Set_Brightness>
     982:	f28ff06f          	j	aa <__riscv_restore_0>
     986:	00a77f63          	bgeu	a4,a0,9a4 <PWM_Update_Fade+0x4a>
     98a:	81c18693          	addi	a3,gp,-2020 # 2000005c <pwm_control>
     98e:	32dc                	lbu	a5,5(a3)
     990:	00f56763          	bltu	a0,a5,99e <PWM_Update_Fade+0x44>
     994:	8d1d                	sub	a0,a0,a5
     996:	0542                	slli	a0,a0,0x10
     998:	8141                	srli	a0,a0,0x10
     99a:	fea763e3          	bltu	a4,a0,980 <PWM_Update_Fade+0x26>
     99e:	00068223          	sb	zero,4(a3)
     9a2:	bff1                	j	97e <PWM_Update_Fade+0x24>
     9a4:	00078223          	sb	zero,4(a5)
     9a8:	bfe9                	j	982 <PWM_Update_Fade+0x28>
     9aa:	8082                	ret

000009ac <PWM_Turn_On>:
     9ac:	81c18793          	addi	a5,gp,-2020 # 2000005c <pwm_control>
     9b0:	1f400713          	li	a4,500
     9b4:	a3ba                	sh	a4,2(a5)
     9b6:	4705                	li	a4,1
     9b8:	a3d8                	sb	a4,4(a5)
     9ba:	8082                	ret

000009bc <PWM_Turn_Off>:
     9bc:	81c18793          	addi	a5,gp,-2020 # 2000005c <pwm_control>
     9c0:	4705                	li	a4,1
     9c2:	00079123          	sh	zero,2(a5)
     9c6:	a3d8                	sb	a4,4(a5)
     9c8:	8082                	ret

000009ca <PWM_Get_Brightness>:
     9ca:	81c1d503          	lhu	a0,-2020(gp) # 2000005c <pwm_control>
     9ce:	8082                	ret

000009d0 <SPI_send_DMA>:
     9d0:	400207b7          	lui	a5,0x40020
     9d4:	dfc8                	sw	a0,60(a5)
     9d6:	dbcc                	sw	a1,52(a5)
     9d8:	5b98                	lw	a4,48(a5)
     9da:	400206b7          	lui	a3,0x40020
     9de:	20000593          	li	a1,512
     9e2:	00176713          	ori	a4,a4,1
     9e6:	db98                	sw	a4,48(a5)
     9e8:	67c1                	lui	a5,0x10
     9ea:	17fd                	addi	a5,a5,-1
     9ec:	167d                	addi	a2,a2,-1
     9ee:	0642                	slli	a2,a2,0x10
     9f0:	8241                	srli	a2,a2,0x10
     9f2:	00f61863          	bne	a2,a5,a02 <SPI_send_DMA+0x32>
     9f6:	40020737          	lui	a4,0x40020
     9fa:	5b1c                	lw	a5,48(a4)
     9fc:	9bf9                	andi	a5,a5,-2
     9fe:	db1c                	sw	a5,48(a4)
     a00:	8082                	ret
     a02:	c2cc                	sw	a1,4(a3)
     a04:	4298                	lw	a4,0(a3)
     a06:	20077713          	andi	a4,a4,512
     a0a:	df6d                	beqz	a4,a04 <SPI_send_DMA+0x34>
     a0c:	b7c5                	j	9ec <SPI_send_DMA+0x1c>

00000a0e <SPI_send>:
     a0e:	400137b7          	lui	a5,0x40013
     a12:	a7ca                	sh	a0,12(a5)
     a14:	40013737          	lui	a4,0x40013
     a18:	271e                	lhu	a5,8(a4)
     a1a:	8b89                	andi	a5,a5,2
     a1c:	dff5                	beqz	a5,a18 <SPI_send+0xa>
     a1e:	8082                	ret

00000a20 <write_command_8>:
     a20:	e80ff2ef          	jal	t0,a0 <__riscv_save_0>
     a24:	40011737          	lui	a4,0x40011
     a28:	4b5c                	lw	a5,20(a4)
     a2a:	0087e793          	ori	a5,a5,8
     a2e:	cb5c                	sw	a5,20(a4)
     a30:	3ff9                	jal	a0e <SPI_send>
     a32:	e78ff06f          	j	aa <__riscv_restore_0>

00000a36 <write_data_16>:
     a36:	e6aff2ef          	jal	t0,a0 <__riscv_save_0>
     a3a:	40011737          	lui	a4,0x40011
     a3e:	4b1c                	lw	a5,16(a4)
     a40:	842a                	mv	s0,a0
     a42:	8121                	srli	a0,a0,0x8
     a44:	0087e793          	ori	a5,a5,8
     a48:	cb1c                	sw	a5,16(a4)
     a4a:	37d1                	jal	a0e <SPI_send>
     a4c:	0ff47513          	andi	a0,s0,255
     a50:	3f7d                	jal	a0e <SPI_send>
     a52:	e58ff06f          	j	aa <__riscv_restore_0>

00000a56 <tft_set_window>:
     a56:	e4aff2ef          	jal	t0,a0 <__riscv_save_0>
     a5a:	1151                	addi	sp,sp,-12
     a5c:	842a                	mv	s0,a0
     a5e:	02a00513          	li	a0,42
     a62:	c036                	sw	a3,0(sp)
     a64:	c42e                	sw	a1,8(sp)
     a66:	c232                	sw	a2,4(sp)
     a68:	3f65                	jal	a20 <write_command_8>
     a6a:	8522                	mv	a0,s0
     a6c:	37e9                	jal	a36 <write_data_16>
     a6e:	4612                	lw	a2,4(sp)
     a70:	8532                	mv	a0,a2
     a72:	37d1                	jal	a36 <write_data_16>
     a74:	02b00513          	li	a0,43
     a78:	3765                	jal	a20 <write_command_8>
     a7a:	45a2                	lw	a1,8(sp)
     a7c:	852e                	mv	a0,a1
     a7e:	3f65                	jal	a36 <write_data_16>
     a80:	4682                	lw	a3,0(sp)
     a82:	8536                	mv	a0,a3
     a84:	3f4d                	jal	a36 <write_data_16>
     a86:	02c00513          	li	a0,44
     a8a:	3f59                	jal	a20 <write_command_8>
     a8c:	0131                	addi	sp,sp,12
     a8e:	e1cff06f          	j	aa <__riscv_restore_0>

00000a92 <tft_init>:
     a92:	e0eff2ef          	jal	t0,a0 <__riscv_save_0>
     a96:	400216b7          	lui	a3,0x40021
     a9a:	4e9c                	lw	a5,24(a3)
     a9c:	6705                	lui	a4,0x1
     a9e:	0741                	addi	a4,a4,16
     aa0:	8fd9                	or	a5,a5,a4
     aa2:	40011437          	lui	s0,0x40011
     aa6:	ce9c                	sw	a5,24(a3)
     aa8:	401c                	lw	a5,0(s0)
     aaa:	777d                	lui	a4,0xfffff
     aac:	0ff70713          	addi	a4,a4,255 # fffff0ff <__global_pointer$+0xdfffe8bf>
     ab0:	8ff9                	and	a5,a5,a4
     ab2:	c01c                	sw	a5,0(s0)
     ab4:	401c                	lw	a5,0(s0)
     ab6:	7745                	lui	a4,0xffff1
     ab8:	177d                	addi	a4,a4,-1
     aba:	3007e793          	ori	a5,a5,768
     abe:	c01c                	sw	a5,0(s0)
     ac0:	401c                	lw	a5,0(s0)
     ac2:	fff10637          	lui	a2,0xfff10
     ac6:	167d                	addi	a2,a2,-1
     ac8:	8ff9                	and	a5,a5,a4
     aca:	c01c                	sw	a5,0(s0)
     acc:	401c                	lw	a5,0(s0)
     ace:	670d                	lui	a4,0x3
     ad0:	1101                	addi	sp,sp,-32
     ad2:	8fd9                	or	a5,a5,a4
     ad4:	c01c                	sw	a5,0(s0)
     ad6:	401c                	lw	a5,0(s0)
     ad8:	0b070713          	addi	a4,a4,176 # 30b0 <_data_lma+0x9e8>
     adc:	03200513          	li	a0,50
     ae0:	8ff1                	and	a5,a5,a2
     ae2:	c01c                	sw	a5,0(s0)
     ae4:	401c                	lw	a5,0(s0)
     ae6:	00030637          	lui	a2,0x30
     aea:	8fd1                	or	a5,a5,a2
     aec:	c01c                	sw	a5,0(s0)
     aee:	401c                	lw	a5,0(s0)
     af0:	ff100637          	lui	a2,0xff100
     af4:	167d                	addi	a2,a2,-1
     af6:	8ff1                	and	a5,a5,a2
     af8:	c01c                	sw	a5,0(s0)
     afa:	401c                	lw	a5,0(s0)
     afc:	00b00637          	lui	a2,0xb00
     b00:	8fd1                	or	a5,a5,a2
     b02:	c01c                	sw	a5,0(s0)
     b04:	401c                	lw	a5,0(s0)
     b06:	f1000637          	lui	a2,0xf1000
     b0a:	167d                	addi	a2,a2,-1
     b0c:	8ff1                	and	a5,a5,a2
     b0e:	c01c                	sw	a5,0(s0)
     b10:	401c                	lw	a5,0(s0)
     b12:	0b000637          	lui	a2,0xb000
     b16:	8fd1                	or	a5,a5,a2
     b18:	7671                	lui	a2,0xffffc
     b1a:	c01c                	sw	a5,0(s0)
     b1c:	30460613          	addi	a2,a2,772 # ffffc304 <__global_pointer$+0xdfffbac4>
     b20:	400137b7          	lui	a5,0x40013
     b24:	a392                	sh	a2,0(a5)
     b26:	461d                	li	a2,7
     b28:	ab92                	sh	a2,16(a5)
     b2a:	23d2                	lhu	a2,4(a5)
     b2c:	07b1                	addi	a5,a5,12
     b2e:	00266613          	ori	a2,a2,2
     b32:	fec79c23          	sh	a2,-8(a5) # 40012ff8 <__global_pointer$+0x200127b8>
     b36:	ff47d603          	lhu	a2,-12(a5)
     b3a:	04066613          	ori	a2,a2,64
     b3e:	fec79a23          	sh	a2,-12(a5)
     b42:	4ad0                	lw	a2,20(a3)
     b44:	00166613          	ori	a2,a2,1
     b48:	cad0                	sw	a2,20(a3)
     b4a:	400206b7          	lui	a3,0x40020
     b4e:	da98                	sw	a4,48(a3)
     b50:	de9c                	sw	a5,56(a3)
     b52:	485c                	lw	a5,20(s0)
     b54:	0047e793          	ori	a5,a5,4
     b58:	c85c                	sw	a5,20(s0)
     b5a:	439000ef          	jal	ra,1792 <Delay_Ms>
     b5e:	481c                	lw	a5,16(s0)
     b60:	03200513          	li	a0,50
     b64:	0047e793          	ori	a5,a5,4
     b68:	c81c                	sw	a5,16(s0)
     b6a:	429000ef          	jal	ra,1792 <Delay_Ms>
     b6e:	485c                	lw	a5,20(s0)
     b70:	4545                	li	a0,17
     b72:	0107e793          	ori	a5,a5,16
     b76:	c85c                	sw	a5,20(s0)
     b78:	3565                	jal	a20 <write_command_8>
     b7a:	07800513          	li	a0,120
     b7e:	415000ef          	jal	ra,1792 <Delay_Ms>
     b82:	03600513          	li	a0,54
     b86:	3d69                	jal	a20 <write_command_8>
     b88:	481c                	lw	a5,16(s0)
     b8a:	0a800513          	li	a0,168
     b8e:	0087e793          	ori	a5,a5,8
     b92:	c81c                	sw	a5,16(s0)
     b94:	3dad                	jal	a0e <SPI_send>
     b96:	03a00513          	li	a0,58
     b9a:	3559                	jal	a20 <write_command_8>
     b9c:	481c                	lw	a5,16(s0)
     b9e:	4515                	li	a0,5
     ba0:	0087e793          	ori	a5,a5,8
     ba4:	c81c                	sw	a5,16(s0)
     ba6:	35a5                	jal	a0e <SPI_send>
     ba8:	6589                	lui	a1,0x2
     baa:	f8858493          	addi	s1,a1,-120 # 1f88 <memcpy+0xd2>
     bae:	4641                	li	a2,16
     bb0:	f8858593          	addi	a1,a1,-120
     bb4:	850a                	mv	a0,sp
     bb6:	300010ef          	jal	ra,1eb6 <memcpy>
     bba:	0e000513          	li	a0,224
     bbe:	358d                	jal	a20 <write_command_8>
     bc0:	481c                	lw	a5,16(s0)
     bc2:	850a                	mv	a0,sp
     bc4:	4605                	li	a2,1
     bc6:	0087e793          	ori	a5,a5,8
     bca:	c81c                	sw	a5,16(s0)
     bcc:	45c1                	li	a1,16
     bce:	3509                	jal	9d0 <SPI_send_DMA>
     bd0:	01048593          	addi	a1,s1,16
     bd4:	4641                	li	a2,16
     bd6:	0808                	addi	a0,sp,16
     bd8:	2de010ef          	jal	ra,1eb6 <memcpy>
     bdc:	0e100513          	li	a0,225
     be0:	3581                	jal	a20 <write_command_8>
     be2:	481c                	lw	a5,16(s0)
     be4:	4605                	li	a2,1
     be6:	45c1                	li	a1,16
     be8:	0087e793          	ori	a5,a5,8
     bec:	c81c                	sw	a5,16(s0)
     bee:	0808                	addi	a0,sp,16
     bf0:	33c5                	jal	9d0 <SPI_send_DMA>
     bf2:	4529                	li	a0,10
     bf4:	39f000ef          	jal	ra,1792 <Delay_Ms>
     bf8:	02100513          	li	a0,33
     bfc:	3515                	jal	a20 <write_command_8>
     bfe:	454d                	li	a0,19
     c00:	3505                	jal	a20 <write_command_8>
     c02:	4529                	li	a0,10
     c04:	38f000ef          	jal	ra,1792 <Delay_Ms>
     c08:	02900513          	li	a0,41
     c0c:	3d11                	jal	a20 <write_command_8>
     c0e:	4529                	li	a0,10
     c10:	383000ef          	jal	ra,1792 <Delay_Ms>
     c14:	481c                	lw	a5,16(s0)
     c16:	0107e793          	ori	a5,a5,16
     c1a:	c81c                	sw	a5,16(s0)
     c1c:	6105                	addi	sp,sp,32
     c1e:	c8cff06f          	j	aa <__riscv_restore_0>

00000c22 <tft_set_cursor>:
     c22:	0505                	addi	a0,a0,1
     c24:	96a19223          	sh	a0,-1692(gp) # 200001a4 <_cursor_x>
     c28:	05e9                	addi	a1,a1,26
     c2a:	96b19323          	sh	a1,-1690(gp) # 200001a6 <_cursor_y>
     c2e:	8082                	ret

00000c30 <tft_set_color>:
     c30:	200007b7          	lui	a5,0x20000
     c34:	00a79023          	sh	a0,0(a5) # 20000000 <_highcode_vma_end>
     c38:	8082                	ret

00000c3a <tft_set_background_color>:
     c3a:	82a19123          	sh	a0,-2014(gp) # 20000062 <_bg_color>
     c3e:	8082                	ret

00000c40 <tft_print_char>:
     c40:	c60ff2ef          	jal	t0,a0 <__riscv_save_0>
     c44:	00251793          	slli	a5,a0,0x2
     c48:	953e                	add	a0,a0,a5
     c4a:	8221d783          	lhu	a5,-2014(gp) # 20000062 <_bg_color>
     c4e:	1131                	addi	sp,sp,-20
     c50:	0087d713          	srli	a4,a5,0x8
     c54:	0ff7f793          	andi	a5,a5,255
     c58:	c63e                	sw	a5,12(sp)
     c5a:	200007b7          	lui	a5,0x20000
     c5e:	0007d783          	lhu	a5,0(a5) # 20000000 <_highcode_vma_end>
     c62:	c43a                	sw	a4,8(sp)
     c64:	4281                	li	t0,0
     c66:	0087d713          	srli	a4,a5,0x8
     c6a:	0ff7f793          	andi	a5,a5,255
     c6e:	c23e                	sw	a5,4(sp)
     c70:	6789                	lui	a5,0x2
     c72:	1c078793          	addi	a5,a5,448 # 21c0 <font>
     c76:	c03a                	sw	a4,0(sp)
     c78:	4681                	li	a3,0
     c7a:	c83e                	sw	a5,16(sp)
     c7c:	82418313          	addi	t1,gp,-2012 # 20000064 <_buffer>
     c80:	4785                	li	a5,1
     c82:	005790b3          	sll	ra,a5,t0
     c86:	85b6                	mv	a1,a3
     c88:	4601                	li	a2,0
     c8a:	44c2                	lw	s1,16(sp)
     c8c:	00c503b3          	add	t2,a0,a2
     c90:	872e                	mv	a4,a1
     c92:	93a6                	add	t2,t2,s1
     c94:	0003c383          	lbu	t2,0(t2)
     c98:	00158793          	addi	a5,a1,1
     c9c:	0589                	addi	a1,a1,2
     c9e:	07c2                	slli	a5,a5,0x10
     ca0:	05c2                	slli	a1,a1,0x10
     ca2:	0013f3b3          	and	t2,t2,ra
     ca6:	83c1                	srli	a5,a5,0x10
     ca8:	81c1                	srli	a1,a1,0x10
     caa:	971a                	add	a4,a4,t1
     cac:	06038763          	beqz	t2,d1a <tft_print_char+0xda>
     cb0:	4482                	lw	s1,0(sp)
     cb2:	979a                	add	a5,a5,t1
     cb4:	a304                	sb	s1,0(a4)
     cb6:	4712                	lw	a4,4(sp)
     cb8:	a398                	sb	a4,0(a5)
     cba:	0605                	addi	a2,a2,1
     cbc:	4795                	li	a5,5
     cbe:	fcf616e3          	bne	a2,a5,c8a <tft_print_char+0x4a>
     cc2:	06a9                	addi	a3,a3,10
     cc4:	06c2                	slli	a3,a3,0x10
     cc6:	82c1                	srli	a3,a3,0x10
     cc8:	04600793          	li	a5,70
     ccc:	0285                	addi	t0,t0,1
     cce:	faf699e3          	bne	a3,a5,c80 <tft_print_char+0x40>
     cd2:	400114b7          	lui	s1,0x40011
     cd6:	48dc                	lw	a5,20(s1)
     cd8:	0107e793          	ori	a5,a5,16
     cdc:	c8dc                	sw	a5,20(s1)
     cde:	9641d503          	lhu	a0,-1692(gp) # 200001a4 <_cursor_x>
     ce2:	9661d583          	lhu	a1,-1690(gp) # 200001a6 <_cursor_y>
     ce6:	00450613          	addi	a2,a0,4
     cea:	0642                	slli	a2,a2,0x10
     cec:	00658693          	addi	a3,a1,6
     cf0:	06c2                	slli	a3,a3,0x10
     cf2:	82c1                	srli	a3,a3,0x10
     cf4:	8241                	srli	a2,a2,0x10
     cf6:	3385                	jal	a56 <tft_set_window>
     cf8:	489c                	lw	a5,16(s1)
     cfa:	4605                	li	a2,1
     cfc:	04600593          	li	a1,70
     d00:	0087e793          	ori	a5,a5,8
     d04:	c89c                	sw	a5,16(s1)
     d06:	82418513          	addi	a0,gp,-2012 # 20000064 <_buffer>
     d0a:	31d9                	jal	9d0 <SPI_send_DMA>
     d0c:	489c                	lw	a5,16(s1)
     d0e:	0107e793          	ori	a5,a5,16
     d12:	c89c                	sw	a5,16(s1)
     d14:	0151                	addi	sp,sp,20
     d16:	b94ff06f          	j	aa <__riscv_restore_0>
     d1a:	44a2                	lw	s1,8(sp)
     d1c:	979a                	add	a5,a5,t1
     d1e:	a304                	sb	s1,0(a4)
     d20:	4732                	lw	a4,12(sp)
     d22:	bf59                	j	cb8 <tft_print_char+0x78>

00000d24 <tft_print>:
     d24:	b7cff2ef          	jal	t0,a0 <__riscv_save_0>
     d28:	842a                	mv	s0,a0
     d2a:	00040503          	lb	a0,0(s0) # 40011000 <__global_pointer$+0x200107c0>
     d2e:	e119                	bnez	a0,d34 <tft_print+0x10>
     d30:	b7aff06f          	j	aa <__riscv_restore_0>
     d34:	3731                	jal	c40 <tft_print_char>
     d36:	96418713          	addi	a4,gp,-1692 # 200001a4 <_cursor_x>
     d3a:	231e                	lhu	a5,0(a4)
     d3c:	0405                	addi	s0,s0,1
     d3e:	0799                	addi	a5,a5,6
     d40:	a31e                	sh	a5,0(a4)
     d42:	b7e5                	j	d2a <tft_print+0x6>

00000d44 <tft_print_number>:
     d44:	b5cff2ef          	jal	t0,a0 <__riscv_save_0>
     d48:	1141                	addi	sp,sp,-16
     d4a:	87aa                	mv	a5,a0
     d4c:	86ae                	mv	a3,a1
     d4e:	4701                	li	a4,0
     d50:	00055563          	bgez	a0,d5a <tft_print_number+0x16>
     d54:	40a007b3          	neg	a5,a0
     d58:	4705                	li	a4,1
     d5a:	96818613          	addi	a2,gp,-1688 # 200001a8 <str.4169>
     d5e:	000605a3          	sb	zero,11(a2)
     d62:	442d                	li	s0,11
     d64:	96818493          	addi	s1,gp,-1688 # 200001a8 <str.4169>
     d68:	eba9                	bnez	a5,dba <tft_print_number+0x76>
     d6a:	47ad                	li	a5,11
     d6c:	00f41663          	bne	s0,a5,d78 <tft_print_number+0x34>
     d70:	03000793          	li	a5,48
     d74:	a4bc                	sb	a5,10(s1)
     d76:	4429                	li	s0,10
     d78:	cb09                	beqz	a4,d8a <tft_print_number+0x46>
     d7a:	147d                	addi	s0,s0,-1
     d7c:	0ff47413          	andi	s0,s0,255
     d80:	008487b3          	add	a5,s1,s0
     d84:	02d00713          	li	a4,45
     d88:	a398                	sb	a4,0(a5)
     d8a:	472d                	li	a4,11
     d8c:	8f01                	sub	a4,a4,s0
     d8e:	00171793          	slli	a5,a4,0x1
     d92:	97ba                	add	a5,a5,a4
     d94:	0786                	slli	a5,a5,0x1
     d96:	17fd                	addi	a5,a5,-1
     d98:	07c2                	slli	a5,a5,0x10
     d9a:	83c1                	srli	a5,a5,0x10
     d9c:	00d7f963          	bgeu	a5,a3,dae <tft_print_number+0x6a>
     da0:	96418713          	addi	a4,gp,-1692 # 200001a4 <_cursor_x>
     da4:	2312                	lhu	a2,0(a4)
     da6:	96b2                	add	a3,a3,a2
     da8:	40f687b3          	sub	a5,a3,a5
     dac:	a31e                	sh	a5,0(a4)
     dae:	00848533          	add	a0,s1,s0
     db2:	3f8d                	jal	d24 <tft_print>
     db4:	0141                	addi	sp,sp,16
     db6:	af4ff06f          	j	aa <__riscv_restore_0>
     dba:	147d                	addi	s0,s0,-1
     dbc:	0ff47413          	andi	s0,s0,255
     dc0:	00848633          	add	a2,s1,s0
     dc4:	45a9                	li	a1,10
     dc6:	853e                	mv	a0,a5
     dc8:	c636                	sw	a3,12(sp)
     dca:	c43a                	sw	a4,8(sp)
     dcc:	c232                	sw	a2,4(sp)
     dce:	c03e                	sw	a5,0(sp)
     dd0:	b54ff0ef          	jal	ra,124 <__modsi3>
     dd4:	4782                	lw	a5,0(sp)
     dd6:	4612                	lw	a2,4(sp)
     dd8:	03050513          	addi	a0,a0,48
     ddc:	45a9                	li	a1,10
     dde:	a208                	sb	a0,0(a2)
     de0:	853e                	mv	a0,a5
     de2:	aeaff0ef          	jal	ra,cc <__divsi3>
     de6:	87aa                	mv	a5,a0
     de8:	46b2                	lw	a3,12(sp)
     dea:	4722                	lw	a4,8(sp)
     dec:	bfb5                	j	d68 <tft_print_number+0x24>

00000dee <tft_fill_rect>:
     dee:	ab2ff2ef          	jal	t0,a0 <__riscv_save_0>
     df2:	0505                	addi	a0,a0,1
     df4:	05e9                	addi	a1,a1,26
     df6:	0542                	slli	a0,a0,0x10
     df8:	05c2                	slli	a1,a1,0x10
     dfa:	8336                	mv	t1,a3
     dfc:	1171                	addi	sp,sp,-4
     dfe:	8141                	srli	a0,a0,0x10
     e00:	81c1                	srli	a1,a1,0x10
     e02:	00875393          	srli	t2,a4,0x8
     e06:	4781                	li	a5,0
     e08:	82418693          	addi	a3,gp,-2012 # 20000064 <_buffer>
     e0c:	00179413          	slli	s0,a5,0x1
     e10:	0442                	slli	s0,s0,0x10
     e12:	8041                	srli	s0,s0,0x10
     e14:	04c79763          	bne	a5,a2,e62 <tft_fill_rect+0x74>
     e18:	400114b7          	lui	s1,0x40011
     e1c:	48d8                	lw	a4,20(s1)
     e1e:	fff30693          	addi	a3,t1,-1
     e22:	fff78613          	addi	a2,a5,-1
     e26:	96ae                	add	a3,a3,a1
     e28:	962a                	add	a2,a2,a0
     e2a:	01076713          	ori	a4,a4,16
     e2e:	06c2                	slli	a3,a3,0x10
     e30:	0642                	slli	a2,a2,0x10
     e32:	c8d8                	sw	a4,20(s1)
     e34:	82c1                	srli	a3,a3,0x10
     e36:	8241                	srli	a2,a2,0x10
     e38:	c01a                	sw	t1,0(sp)
     e3a:	c1dff0ef          	jal	ra,a56 <tft_set_window>
     e3e:	489c                	lw	a5,16(s1)
     e40:	4302                	lw	t1,0(sp)
     e42:	0087e793          	ori	a5,a5,8
     e46:	c89c                	sw	a5,16(s1)
     e48:	861a                	mv	a2,t1
     e4a:	85a2                	mv	a1,s0
     e4c:	82418513          	addi	a0,gp,-2012 # 20000064 <_buffer>
     e50:	b81ff0ef          	jal	ra,9d0 <SPI_send_DMA>
     e54:	489c                	lw	a5,16(s1)
     e56:	0107e793          	ori	a5,a5,16
     e5a:	c89c                	sw	a5,16(s1)
     e5c:	0111                	addi	sp,sp,4
     e5e:	a4cff06f          	j	aa <__riscv_restore_0>
     e62:	008684b3          	add	s1,a3,s0
     e66:	0405                	addi	s0,s0,1
     e68:	0442                	slli	s0,s0,0x10
     e6a:	8041                	srli	s0,s0,0x10
     e6c:	0785                	addi	a5,a5,1
     e6e:	00748023          	sb	t2,0(s1) # 40011000 <__global_pointer$+0x200107c0>
     e72:	9436                	add	s0,s0,a3
     e74:	07c2                	slli	a5,a5,0x10
     e76:	a018                	sb	a4,0(s0)
     e78:	83c1                	srli	a5,a5,0x10
     e7a:	bf49                	j	e0c <tft_fill_rect+0x1e>

00000e7c <SystemInit>:
     e7c:	a24ff2ef          	jal	t0,a0 <__riscv_save_0>
     e80:	40021437          	lui	s0,0x40021
     e84:	401c                	lw	a5,0(s0)
     e86:	f8ff0737          	lui	a4,0xf8ff0
     e8a:	1161                	addi	sp,sp,-8
     e8c:	0017e793          	ori	a5,a5,1
     e90:	c01c                	sw	a5,0(s0)
     e92:	405c                	lw	a5,4(s0)
     e94:	4541                	li	a0,16
     e96:	8ff9                	and	a5,a5,a4
     e98:	c05c                	sw	a5,4(s0)
     e9a:	401c                	lw	a5,0(s0)
     e9c:	fef70737          	lui	a4,0xfef70
     ea0:	177d                	addi	a4,a4,-1
     ea2:	8ff9                	and	a5,a5,a4
     ea4:	c01c                	sw	a5,0(s0)
     ea6:	401c                	lw	a5,0(s0)
     ea8:	fffc0737          	lui	a4,0xfffc0
     eac:	177d                	addi	a4,a4,-1
     eae:	8ff9                	and	a5,a5,a4
     eb0:	c01c                	sw	a5,0(s0)
     eb2:	405c                	lw	a5,4(s0)
     eb4:	7741                	lui	a4,0xffff0
     eb6:	177d                	addi	a4,a4,-1
     eb8:	8ff9                	and	a5,a5,a4
     eba:	c05c                	sw	a5,4(s0)
     ebc:	009f07b7          	lui	a5,0x9f0
     ec0:	c41c                	sw	a5,8(s0)
     ec2:	23bd                	jal	1430 <RCC_AdjustHSICalibrationValue>
     ec4:	4c1c                	lw	a5,24(s0)
     ec6:	00020637          	lui	a2,0x20
     eca:	0207e793          	ori	a5,a5,32
     ece:	cc1c                	sw	a5,24(s0)
     ed0:	400117b7          	lui	a5,0x40011
     ed4:	4007a703          	lw	a4,1024(a5) # 40011400 <__global_pointer$+0x20010bc0>
     ed8:	40078693          	addi	a3,a5,1024
     edc:	f0f77713          	andi	a4,a4,-241
     ee0:	40e7a023          	sw	a4,1024(a5)
     ee4:	4007a703          	lw	a4,1024(a5)
     ee8:	08076713          	ori	a4,a4,128
     eec:	40e7a023          	sw	a4,1024(a5)
     ef0:	4789                	li	a5,2
     ef2:	ca9c                	sw	a5,16(a3)
     ef4:	c002                	sw	zero,0(sp)
     ef6:	c202                	sw	zero,4(sp)
     ef8:	4c1c                	lw	a5,24(s0)
     efa:	40010737          	lui	a4,0x40010
     efe:	66a1                	lui	a3,0x8
     f00:	0017e793          	ori	a5,a5,1
     f04:	cc1c                	sw	a5,24(s0)
     f06:	435c                	lw	a5,4(a4)
     f08:	8fd5                	or	a5,a5,a3
     f0a:	c35c                	sw	a5,4(a4)
     f0c:	401c                	lw	a5,0(s0)
     f0e:	6741                	lui	a4,0x10
     f10:	400216b7          	lui	a3,0x40021
     f14:	8fd9                	or	a5,a5,a4
     f16:	c01c                	sw	a5,0(s0)
     f18:	6709                	lui	a4,0x2
     f1a:	429c                	lw	a5,0(a3)
     f1c:	8ff1                	and	a5,a5,a2
     f1e:	c23e                	sw	a5,4(sp)
     f20:	4782                	lw	a5,0(sp)
     f22:	0785                	addi	a5,a5,1
     f24:	c03e                	sw	a5,0(sp)
     f26:	4792                	lw	a5,4(sp)
     f28:	e781                	bnez	a5,f30 <SystemInit+0xb4>
     f2a:	4782                	lw	a5,0(sp)
     f2c:	fee797e3          	bne	a5,a4,f1a <SystemInit+0x9e>
     f30:	400217b7          	lui	a5,0x40021
     f34:	439c                	lw	a5,0(a5)
     f36:	00e79713          	slli	a4,a5,0xe
     f3a:	06075963          	bgez	a4,fac <SystemInit+0x130>
     f3e:	4785                	li	a5,1
     f40:	c23e                	sw	a5,4(sp)
     f42:	4712                	lw	a4,4(sp)
     f44:	4785                	li	a5,1
     f46:	06f71063          	bne	a4,a5,fa6 <SystemInit+0x12a>
     f4a:	400227b7          	lui	a5,0x40022
     f4e:	4398                	lw	a4,0(a5)
     f50:	76c1                	lui	a3,0xffff0
     f52:	16fd                	addi	a3,a3,-1
     f54:	9b71                	andi	a4,a4,-4
     f56:	c398                	sw	a4,0(a5)
     f58:	4398                	lw	a4,0(a5)
     f5a:	00176713          	ori	a4,a4,1
     f5e:	c398                	sw	a4,0(a5)
     f60:	400217b7          	lui	a5,0x40021
     f64:	43d8                	lw	a4,4(a5)
     f66:	c3d8                	sw	a4,4(a5)
     f68:	43d8                	lw	a4,4(a5)
     f6a:	8f75                	and	a4,a4,a3
     f6c:	c3d8                	sw	a4,4(a5)
     f6e:	43d8                	lw	a4,4(a5)
     f70:	66c1                	lui	a3,0x10
     f72:	8f55                	or	a4,a4,a3
     f74:	c3d8                	sw	a4,4(a5)
     f76:	4398                	lw	a4,0(a5)
     f78:	010006b7          	lui	a3,0x1000
     f7c:	8f55                	or	a4,a4,a3
     f7e:	c398                	sw	a4,0(a5)
     f80:	4398                	lw	a4,0(a5)
     f82:	00671693          	slli	a3,a4,0x6
     f86:	fe06dde3          	bgez	a3,f80 <SystemInit+0x104>
     f8a:	43d8                	lw	a4,4(a5)
     f8c:	400216b7          	lui	a3,0x40021
     f90:	9b71                	andi	a4,a4,-4
     f92:	c3d8                	sw	a4,4(a5)
     f94:	43d8                	lw	a4,4(a5)
     f96:	00276713          	ori	a4,a4,2
     f9a:	c3d8                	sw	a4,4(a5)
     f9c:	4721                	li	a4,8
     f9e:	42dc                	lw	a5,4(a3)
     fa0:	8bb1                	andi	a5,a5,12
     fa2:	fee79ee3          	bne	a5,a4,f9e <SystemInit+0x122>
     fa6:	0121                	addi	sp,sp,8
     fa8:	902ff06f          	j	aa <__riscv_restore_0>
     fac:	c202                	sw	zero,4(sp)
     fae:	bf51                	j	f42 <SystemInit+0xc6>

00000fb0 <SystemCoreClockUpdate>:
     fb0:	8f0ff2ef          	jal	t0,a0 <__riscv_save_0>
     fb4:	40021737          	lui	a4,0x40021
     fb8:	435c                	lw	a5,4(a4)
     fba:	20000437          	lui	s0,0x20000
     fbe:	4691                	li	a3,4
     fc0:	8bb1                	andi	a5,a5,12
     fc2:	01440413          	addi	s0,s0,20 # 20000014 <SystemCoreClock>
     fc6:	00d78563          	beq	a5,a3,fd0 <SystemCoreClockUpdate+0x20>
     fca:	46a1                	li	a3,8
     fcc:	04d78263          	beq	a5,a3,1010 <SystemCoreClockUpdate+0x60>
     fd0:	016e37b7          	lui	a5,0x16e3
     fd4:	60078793          	addi	a5,a5,1536 # 16e3600 <_data_lma+0x16e0f38>
     fd8:	c01c                	sw	a5,0(s0)
     fda:	400216b7          	lui	a3,0x40021
     fde:	42dc                	lw	a5,4(a3)
     fe0:	4008                	lw	a0,0(s0)
     fe2:	8391                	srli	a5,a5,0x4
     fe4:	00f7f713          	andi	a4,a5,15
     fe8:	200007b7          	lui	a5,0x20000
     fec:	00478793          	addi	a5,a5,4 # 20000004 <AHBPrescTable>
     ff0:	97ba                	add	a5,a5,a4
     ff2:	238c                	lbu	a1,0(a5)
     ff4:	42dc                	lw	a5,4(a3)
     ff6:	0ff5f593          	andi	a1,a1,255
     ffa:	0807f793          	andi	a5,a5,128
     ffe:	00b55733          	srl	a4,a0,a1
    1002:	e781                	bnez	a5,100a <SystemCoreClockUpdate+0x5a>
    1004:	8d0ff0ef          	jal	ra,d4 <__udivsi3>
    1008:	872a                	mv	a4,a0
    100a:	c018                	sw	a4,0(s0)
    100c:	89eff06f          	j	aa <__riscv_restore_0>
    1010:	435c                	lw	a5,4(a4)
    1012:	02dc77b7          	lui	a5,0x2dc7
    1016:	c0078793          	addi	a5,a5,-1024 # 2dc6c00 <_data_lma+0x2dc4538>
    101a:	bf7d                	j	fd8 <SystemCoreClockUpdate+0x28>

0000101c <Touch_Button_GPIO_Config>:
    101c:	884ff2ef          	jal	t0,a0 <__riscv_save_0>
    1020:	1151                	addi	sp,sp,-12
    1022:	4585                	li	a1,1
    1024:	02000513          	li	a0,32
    1028:	c002                	sw	zero,0(sp)
    102a:	c202                	sw	zero,4(sp)
    102c:	c402                	sw	zero,8(sp)
    102e:	295d                	jal	14e4 <RCC_APB2PeriphClockCmd>
    1030:	4785                	li	a5,1
    1032:	40011537          	lui	a0,0x40011
    1036:	807c                	sh	a5,0(sp)
    1038:	858a                	mv	a1,sp
    103a:	04800793          	li	a5,72
    103e:	40050513          	addi	a0,a0,1024 # 40011400 <__global_pointer$+0x20010bc0>
    1042:	c43e                	sw	a5,8(sp)
    1044:	2cf1                	jal	1320 <GPIO_Init>
    1046:	0131                	addi	sp,sp,12
    1048:	862ff06f          	j	aa <__riscv_restore_0>

0000104c <Touch_Button_Timer_Init>:
    104c:	854ff2ef          	jal	t0,a0 <__riscv_save_0>
    1050:	1131                	addi	sp,sp,-20
    1052:	4585                	li	a1,1
    1054:	4505                	li	a0,1
    1056:	c402                	sw	zero,8(sp)
    1058:	c602                	sw	zero,12(sp)
    105a:	00011823          	sh	zero,16(sp)
    105e:	c002                	sw	zero,0(sp)
    1060:	c202                	sw	zero,4(sp)
    1062:	2145                	jal	1502 <RCC_APB1PeriphClockCmd>
    1064:	02f00793          	li	a5,47
    1068:	c43e                	sw	a5,8(sp)
    106a:	002c                	addi	a1,sp,8
    106c:	3e700793          	li	a5,999
    1070:	40000537          	lui	a0,0x40000
    1074:	c63e                	sw	a5,12(sp)
    1076:	216d                	jal	1520 <TIM_TimeBaseInit>
    1078:	4605                	li	a2,1
    107a:	4585                	li	a1,1
    107c:	40000537          	lui	a0,0x40000
    1080:	2359                	jal	1606 <TIM_ITConfig>
    1082:	22600793          	li	a5,550
    1086:	807c                	sh	a5,0(sp)
    1088:	850a                	mv	a0,sp
    108a:	4785                	li	a5,1
    108c:	c23e                	sw	a5,4(sp)
    108e:	00010123          	sb	zero,2(sp)
    1092:	2e35                	jal	13ce <NVIC_Init>
    1094:	4585                	li	a1,1
    1096:	40000537          	lui	a0,0x40000
    109a:	2b3d                	jal	15d8 <TIM_Cmd>
    109c:	0151                	addi	sp,sp,20
    109e:	80cff06f          	j	aa <__riscv_restore_0>

000010a2 <Touch_Button_EXTI_Config>:
    10a2:	ffffe2ef          	jal	t0,a0 <__riscv_save_0>
    10a6:	1121                	addi	sp,sp,-24
    10a8:	4585                	li	a1,1
    10aa:	4505                	li	a0,1
    10ac:	c402                	sw	zero,8(sp)
    10ae:	c602                	sw	zero,12(sp)
    10b0:	c802                	sw	zero,16(sp)
    10b2:	ca02                	sw	zero,20(sp)
    10b4:	c002                	sw	zero,0(sp)
    10b6:	c202                	sw	zero,4(sp)
    10b8:	2135                	jal	14e4 <RCC_APB2PeriphClockCmd>
    10ba:	4581                	li	a1,0
    10bc:	450d                	li	a0,3
    10be:	24e5                	jal	13a6 <GPIO_EXTILineConfig>
    10c0:	4405                	li	s0,1
    10c2:	47c1                	li	a5,16
    10c4:	0028                	addi	a0,sp,8
    10c6:	c422                	sw	s0,8(sp)
    10c8:	c83e                	sw	a5,16(sp)
    10ca:	ca22                	sw	s0,20(sp)
    10cc:	c602                	sw	zero,12(sp)
    10ce:	22c1                	jal	128e <EXTI_Init>
    10d0:	11400793          	li	a5,276
    10d4:	850a                	mv	a0,sp
    10d6:	807c                	sh	a5,0(sp)
    10d8:	8140                	sb	s0,2(sp)
    10da:	c222                	sw	s0,4(sp)
    10dc:	2ccd                	jal	13ce <NVIC_Init>
    10de:	0161                	addi	sp,sp,24
    10e0:	fcbfe06f          	j	aa <__riscv_restore_0>

000010e4 <Touch_Button_Init>:
    10e4:	fbdfe2ef          	jal	t0,a0 <__riscv_save_0>
    10e8:	97818793          	addi	a5,gp,-1672 # 200001b8 <touch_button>
    10ec:	00079823          	sh	zero,16(a5)
    10f0:	0007a023          	sw	zero,0(a5)
    10f4:	0007a223          	sw	zero,4(a5)
    10f8:	0007a423          	sw	zero,8(a5)
    10fc:	0007a623          	sw	zero,12(a5)
    1100:	00078923          	sb	zero,18(a5)
    1104:	3f21                	jal	101c <Touch_Button_GPIO_Config>
    1106:	3f71                	jal	10a2 <Touch_Button_EXTI_Config>
    1108:	3791                	jal	104c <Touch_Button_Timer_Init>
    110a:	fa1fe06f          	j	aa <__riscv_restore_0>

0000110e <Touch_Button_Update>:
    110e:	9741a683          	lw	a3,-1676(gp) # 200001b4 <system_tick_ms>
    1112:	97818793          	addi	a5,gp,-1672 # 200001b8 <touch_button>
    1116:	4790                	lw	a2,8(a5)
    1118:	438c                	lw	a1,0(a5)
    111a:	4505                	li	a0,1
    111c:	40c68633          	sub	a2,a3,a2
    1120:	97818713          	addi	a4,gp,-1672 # 200001b8 <touch_button>
    1124:	02a58663          	beq	a1,a0,1150 <Touch_Button_Update+0x42>
    1128:	c589                	beqz	a1,1132 <Touch_Button_Update+0x24>
    112a:	478d                	li	a5,3
    112c:	04f58063          	beq	a1,a5,116c <Touch_Button_Update+0x5e>
    1130:	8082                	ret
    1132:	3b98                	lbu	a4,17(a5)
    1134:	c729                	beqz	a4,117e <Touch_Button_Update+0x70>
    1136:	2bb8                	lbu	a4,18(a5)
    1138:	e339                	bnez	a4,117e <Touch_Button_Update+0x70>
    113a:	47d8                	lw	a4,12(a5)
    113c:	8e99                	sub	a3,a3,a4
    113e:	7cf00713          	li	a4,1999
    1142:	02d77e63          	bgeu	a4,a3,117e <Touch_Button_Update+0x70>
    1146:	470d                	li	a4,3
    1148:	000788a3          	sb	zero,17(a5)
    114c:	c3d8                	sw	a4,4(a5)
    114e:	8082                	ret
    1150:	3e700713          	li	a4,999
    1154:	02c77563          	bgeu	a4,a2,117e <Touch_Button_Update+0x70>
    1158:	4709                	li	a4,2
    115a:	c398                	sw	a4,0(a5)
    115c:	c3d8                	sw	a4,4(a5)
    115e:	2bb8                	lbu	a4,18(a5)
    1160:	bb8c                	sb	a1,17(a5)
    1162:	c7d4                	sw	a3,12(a5)
    1164:	00173713          	seqz	a4,a4
    1168:	abb8                	sb	a4,18(a5)
    116a:	8082                	ret
    116c:	3e700793          	li	a5,999
    1170:	00c7e563          	bltu	a5,a2,117a <Touch_Button_Update+0x6c>
    1174:	c348                	sw	a0,4(a4)
    1176:	bb08                	sb	a0,17(a4)
    1178:	c754                	sw	a3,12(a4)
    117a:	00072023          	sw	zero,0(a4) # 40021000 <__global_pointer$+0x200207c0>
    117e:	8082                	ret

00001180 <Touch_Button_Get_Event>:
    1180:	97818793          	addi	a5,gp,-1672 # 200001b8 <touch_button>
    1184:	43c8                	lw	a0,4(a5)
    1186:	0007a223          	sw	zero,4(a5)
    118a:	8082                	ret

0000118c <Touch_Button_IRQ_Handler>:
    118c:	f15fe2ef          	jal	t0,a0 <__riscv_save_0>
    1190:	4505                	li	a0,1
    1192:	229d                	jal	12f8 <EXTI_GetITStatus>
    1194:	c505                	beqz	a0,11bc <Touch_Button_IRQ_Handler+0x30>
    1196:	40011537          	lui	a0,0x40011
    119a:	4585                	li	a1,1
    119c:	40050513          	addi	a0,a0,1024 # 40011400 <__global_pointer$+0x20010bc0>
    11a0:	9741a403          	lw	s0,-1676(gp) # 200001b4 <system_tick_ms>
    11a4:	2ae5                	jal	139c <GPIO_ReadInputDataBit>
    11a6:	97818793          	addi	a5,gp,-1672 # 200001b8 <touch_button>
    11aa:	4398                	lw	a4,0(a5)
    11ac:	c911                	beqz	a0,11c0 <Touch_Button_IRQ_Handler+0x34>
    11ae:	e709                	bnez	a4,11b8 <Touch_Button_IRQ_Handler+0x2c>
    11b0:	4705                	li	a4,1
    11b2:	c398                	sw	a4,0(a5)
    11b4:	c780                	sw	s0,8(a5)
    11b6:	ab98                	sb	a4,16(a5)
    11b8:	4505                	li	a0,1
    11ba:	2ab1                	jal	1316 <EXTI_ClearITPendingBit>
    11bc:	eeffe06f          	j	aa <__riscv_restore_0>
    11c0:	177d                	addi	a4,a4,-1
    11c2:	4685                	li	a3,1
    11c4:	fee6eae3          	bltu	a3,a4,11b8 <Touch_Button_IRQ_Handler+0x2c>
    11c8:	470d                	li	a4,3
    11ca:	c398                	sw	a4,0(a5)
    11cc:	00078823          	sb	zero,16(a5)
    11d0:	b7e5                	j	11b8 <Touch_Button_IRQ_Handler+0x2c>

000011d2 <ADC1_IRQHandler>:
    11d2:	a001                	j	11d2 <ADC1_IRQHandler>

000011d4 <handle_reset>:
    11d4:	1ffff197          	auipc	gp,0x1ffff
    11d8:	66c18193          	addi	gp,gp,1644 # 20000840 <__global_pointer$>
    11dc:	fc018113          	addi	sp,gp,-64 # 20000800 <_eusrstack>
    11e0:	0a000513          	li	a0,160
    11e4:	1ffff597          	auipc	a1,0x1ffff
    11e8:	e1c58593          	addi	a1,a1,-484 # 20000000 <_highcode_vma_end>
    11ec:	1ffff617          	auipc	a2,0x1ffff
    11f0:	e1460613          	addi	a2,a2,-492 # 20000000 <_highcode_vma_end>
    11f4:	00c5fa63          	bgeu	a1,a2,1208 <handle_reset+0x34>
    11f8:	00052283          	lw	t0,0(a0)
    11fc:	0055a023          	sw	t0,0(a1)
    1200:	0511                	addi	a0,a0,4
    1202:	0591                	addi	a1,a1,4
    1204:	fec5eae3          	bltu	a1,a2,11f8 <handle_reset+0x24>
    1208:	00001517          	auipc	a0,0x1
    120c:	4c050513          	addi	a0,a0,1216 # 26c8 <_data_lma>
    1210:	1ffff597          	auipc	a1,0x1ffff
    1214:	df058593          	addi	a1,a1,-528 # 20000000 <_highcode_vma_end>
    1218:	1ffff617          	auipc	a2,0x1ffff
    121c:	e2860613          	addi	a2,a2,-472 # 20000040 <_edata>
    1220:	00c5fa63          	bgeu	a1,a2,1234 <handle_reset+0x60>
    1224:	00052283          	lw	t0,0(a0)
    1228:	0055a023          	sw	t0,0(a1)
    122c:	0511                	addi	a0,a0,4
    122e:	0591                	addi	a1,a1,4
    1230:	fec5eae3          	bltu	a1,a2,1224 <handle_reset+0x50>
    1234:	1ffff517          	auipc	a0,0x1ffff
    1238:	e0c50513          	addi	a0,a0,-500 # 20000040 <_edata>
    123c:	99418593          	addi	a1,gp,-1644 # 200001d4 <_ebss>
    1240:	00b57763          	bgeu	a0,a1,124e <handle_reset+0x7a>
    1244:	00052023          	sw	zero,0(a0)
    1248:	0511                	addi	a0,a0,4
    124a:	feb56de3          	bltu	a0,a1,1244 <handle_reset+0x70>
    124e:	000022b7          	lui	t0,0x2
    1252:	88028293          	addi	t0,t0,-1920 # 1880 <printchar+0x10>
    1256:	30029073          	csrw	mstatus,t0
    125a:	428d                	li	t0,3
    125c:	80429073          	csrw	0x804,t0
    1260:	fffff297          	auipc	t0,0xfffff
    1264:	da028293          	addi	t0,t0,-608 # 0 <_sinit>
    1268:	0032e293          	ori	t0,t0,3
    126c:	30529073          	csrw	mtvec,t0
    1270:	c0dff0ef          	jal	ra,e7c <SystemInit>
    1274:	fffff297          	auipc	t0,0xfffff
    1278:	4cc28293          	addi	t0,t0,1228 # 740 <main>
    127c:	34129073          	csrw	mepc,t0
    1280:	30200073          	mret

00001284 <DBGMCU_GetCHIPID>:
    1284:	1ffff7b7          	lui	a5,0x1ffff
    1288:	7c47a503          	lw	a0,1988(a5) # 1ffff7c4 <_data_lma+0x1fffd0fc>
    128c:	8082                	ret

0000128e <EXTI_Init>:
    128e:	4158                	lw	a4,4(a0)
    1290:	00052303          	lw	t1,0(a0)
    1294:	454c                	lw	a1,12(a0)
    1296:	40010637          	lui	a2,0x40010
    129a:	40060793          	addi	a5,a2,1024 # 40010400 <__global_pointer$+0x2000fbc0>
    129e:	973e                	add	a4,a4,a5
    12a0:	fff34693          	not	a3,t1
    12a4:	c5b1                	beqz	a1,12f0 <EXTI_Init+0x62>
    12a6:	40062583          	lw	a1,1024(a2)
    12aa:	8df5                	and	a1,a1,a3
    12ac:	40b62023          	sw	a1,1024(a2)
    12b0:	43d0                	lw	a2,4(a5)
    12b2:	8ef1                	and	a3,a3,a2
    12b4:	c3d4                	sw	a3,4(a5)
    12b6:	4314                	lw	a3,0(a4)
    12b8:	0066e6b3          	or	a3,a3,t1
    12bc:	c314                	sw	a3,0(a4)
    12be:	4118                	lw	a4,0(a0)
    12c0:	4790                	lw	a2,8(a5)
    12c2:	fff74693          	not	a3,a4
    12c6:	8e75                	and	a2,a2,a3
    12c8:	c790                	sw	a2,8(a5)
    12ca:	47d0                	lw	a2,12(a5)
    12cc:	8ef1                	and	a3,a3,a2
    12ce:	c7d4                	sw	a3,12(a5)
    12d0:	4514                	lw	a3,8(a0)
    12d2:	4641                	li	a2,16
    12d4:	00c69963          	bne	a3,a2,12e6 <EXTI_Init+0x58>
    12d8:	4794                	lw	a3,8(a5)
    12da:	8ed9                	or	a3,a3,a4
    12dc:	c794                	sw	a3,8(a5)
    12de:	47d4                	lw	a3,12(a5)
    12e0:	8f55                	or	a4,a4,a3
    12e2:	c7d8                	sw	a4,12(a5)
    12e4:	8082                	ret
    12e6:	97b6                	add	a5,a5,a3
    12e8:	4394                	lw	a3,0(a5)
    12ea:	8f55                	or	a4,a4,a3
    12ec:	c398                	sw	a4,0(a5)
    12ee:	8082                	ret
    12f0:	431c                	lw	a5,0(a4)
    12f2:	8ff5                	and	a5,a5,a3
    12f4:	c31c                	sw	a5,0(a4)
    12f6:	8082                	ret

000012f8 <EXTI_GetITStatus>:
    12f8:	400107b7          	lui	a5,0x40010
    12fc:	40078713          	addi	a4,a5,1024 # 40010400 <__global_pointer$+0x2000fbc0>
    1300:	4007a783          	lw	a5,1024(a5)
    1304:	4b58                	lw	a4,20(a4)
    1306:	8f69                	and	a4,a4,a0
    1308:	c709                	beqz	a4,1312 <EXTI_GetITStatus+0x1a>
    130a:	8d7d                	and	a0,a0,a5
    130c:	00a03533          	snez	a0,a0
    1310:	8082                	ret
    1312:	4501                	li	a0,0
    1314:	8082                	ret

00001316 <EXTI_ClearITPendingBit>:
    1316:	400107b7          	lui	a5,0x40010
    131a:	40a7aa23          	sw	a0,1044(a5) # 40010414 <__global_pointer$+0x2000fbd4>
    131e:	8082                	ret

00001320 <GPIO_Init>:
    1320:	4594                	lw	a3,8(a1)
    1322:	0106f793          	andi	a5,a3,16
    1326:	00f6f293          	andi	t0,a3,15
    132a:	c781                	beqz	a5,1332 <GPIO_Init+0x12>
    132c:	41dc                	lw	a5,4(a1)
    132e:	00f2e2b3          	or	t0,t0,a5
    1332:	0005d383          	lhu	t2,0(a1)
    1336:	0ff3f793          	andi	a5,t2,255
    133a:	c3a5                	beqz	a5,139a <GPIO_Init+0x7a>
    133c:	00052303          	lw	t1,0(a0)
    1340:	1161                	addi	sp,sp,-8
    1342:	c222                	sw	s0,4(sp)
    1344:	c026                	sw	s1,0(sp)
    1346:	4781                	li	a5,0
    1348:	02800413          	li	s0,40
    134c:	04800493          	li	s1,72
    1350:	4705                	li	a4,1
    1352:	00f71633          	sll	a2,a4,a5
    1356:	00c3f733          	and	a4,t2,a2
    135a:	02e61263          	bne	a2,a4,137e <GPIO_Init+0x5e>
    135e:	00279593          	slli	a1,a5,0x2
    1362:	473d                	li	a4,15
    1364:	00b71733          	sll	a4,a4,a1
    1368:	fff74713          	not	a4,a4
    136c:	00677333          	and	t1,a4,t1
    1370:	00b295b3          	sll	a1,t0,a1
    1374:	0065e333          	or	t1,a1,t1
    1378:	00869d63          	bne	a3,s0,1392 <GPIO_Init+0x72>
    137c:	c950                	sw	a2,20(a0)
    137e:	0785                	addi	a5,a5,1
    1380:	4721                	li	a4,8
    1382:	fce797e3          	bne	a5,a4,1350 <GPIO_Init+0x30>
    1386:	4412                	lw	s0,4(sp)
    1388:	00652023          	sw	t1,0(a0)
    138c:	4482                	lw	s1,0(sp)
    138e:	0121                	addi	sp,sp,8
    1390:	8082                	ret
    1392:	fe9696e3          	bne	a3,s1,137e <GPIO_Init+0x5e>
    1396:	c910                	sw	a2,16(a0)
    1398:	b7dd                	j	137e <GPIO_Init+0x5e>
    139a:	8082                	ret

0000139c <GPIO_ReadInputDataBit>:
    139c:	4508                	lw	a0,8(a0)
    139e:	8d6d                	and	a0,a0,a1
    13a0:	00a03533          	snez	a0,a0
    13a4:	8082                	ret

000013a6 <GPIO_EXTILineConfig>:
    13a6:	40010737          	lui	a4,0x40010
    13aa:	4714                	lw	a3,8(a4)
    13ac:	0586                	slli	a1,a1,0x1
    13ae:	478d                	li	a5,3
    13b0:	00b797b3          	sll	a5,a5,a1
    13b4:	fff7c793          	not	a5,a5
    13b8:	8ff5                	and	a5,a5,a3
    13ba:	c71c                	sw	a5,8(a4)
    13bc:	471c                	lw	a5,8(a4)
    13be:	00b515b3          	sll	a1,a0,a1
    13c2:	8ddd                	or	a1,a1,a5
    13c4:	c70c                	sw	a1,8(a4)
    13c6:	8082                	ret

000013c8 <NVIC_PriorityGroupConfig>:
    13c8:	98a1a623          	sw	a0,-1652(gp) # 200001cc <NVIC_Priority_Group>
    13cc:	8082                	ret

000013ce <NVIC_Init>:
    13ce:	98c1a683          	lw	a3,-1652(gp) # 200001cc <NVIC_Priority_Group>
    13d2:	4785                	li	a5,1
    13d4:	2118                	lbu	a4,0(a0)
    13d6:	02f69063          	bne	a3,a5,13f6 <NVIC_Init+0x28>
    13da:	311c                	lbu	a5,1(a0)
    13dc:	02d79c63          	bne	a5,a3,1414 <NVIC_Init+0x46>
    13e0:	213c                	lbu	a5,2(a0)
    13e2:	079a                	slli	a5,a5,0x6
    13e4:	f807e793          	ori	a5,a5,-128
    13e8:	e000e6b7          	lui	a3,0xe000e
    13ec:	0ff7f793          	andi	a5,a5,255
    13f0:	96ba                	add	a3,a3,a4
    13f2:	40f68023          	sb	a5,1024(a3) # e000e400 <__global_pointer$+0xc000dbc0>
    13f6:	4685                	li	a3,1
    13f8:	00575793          	srli	a5,a4,0x5
    13fc:	00e69733          	sll	a4,a3,a4
    1400:	4154                	lw	a3,4(a0)
    1402:	ce89                	beqz	a3,141c <NVIC_Init+0x4e>
    1404:	04078793          	addi	a5,a5,64
    1408:	078a                	slli	a5,a5,0x2
    140a:	e000e6b7          	lui	a3,0xe000e
    140e:	97b6                	add	a5,a5,a3
    1410:	c398                	sw	a4,0(a5)
    1412:	8082                	ret
    1414:	f3ed                	bnez	a5,13f6 <NVIC_Init+0x28>
    1416:	213c                	lbu	a5,2(a0)
    1418:	079a                	slli	a5,a5,0x6
    141a:	b7f9                	j	13e8 <NVIC_Init+0x1a>
    141c:	06078793          	addi	a5,a5,96
    1420:	e000e6b7          	lui	a3,0xe000e
    1424:	078a                	slli	a5,a5,0x2
    1426:	97b6                	add	a5,a5,a3
    1428:	c398                	sw	a4,0(a5)
    142a:	0000100f          	fence.i
    142e:	8082                	ret

00001430 <RCC_AdjustHSICalibrationValue>:
    1430:	40021737          	lui	a4,0x40021
    1434:	431c                	lw	a5,0(a4)
    1436:	050e                	slli	a0,a0,0x3
    1438:	f077f793          	andi	a5,a5,-249
    143c:	8d5d                	or	a0,a0,a5
    143e:	c308                	sw	a0,0(a4)
    1440:	8082                	ret

00001442 <RCC_GetClocksFreq>:
    1442:	c5ffe2ef          	jal	t0,a0 <__riscv_save_0>
    1446:	40021737          	lui	a4,0x40021
    144a:	435c                	lw	a5,4(a4)
    144c:	4691                	li	a3,4
    144e:	842a                	mv	s0,a0
    1450:	8bb1                	andi	a5,a5,12
    1452:	00d78563          	beq	a5,a3,145c <RCC_GetClocksFreq+0x1a>
    1456:	46a1                	li	a3,8
    1458:	08d78063          	beq	a5,a3,14d8 <RCC_GetClocksFreq+0x96>
    145c:	016e37b7          	lui	a5,0x16e3
    1460:	60078793          	addi	a5,a5,1536 # 16e3600 <_data_lma+0x16e0f38>
    1464:	c01c                	sw	a5,0(s0)
    1466:	400216b7          	lui	a3,0x40021
    146a:	42dc                	lw	a5,4(a3)
    146c:	8391                	srli	a5,a5,0x4
    146e:	00f7f713          	andi	a4,a5,15
    1472:	200007b7          	lui	a5,0x20000
    1476:	02c78793          	addi	a5,a5,44 # 2000002c <APBAHBPrescTable>
    147a:	97ba                	add	a5,a5,a4
    147c:	238c                	lbu	a1,0(a5)
    147e:	42dc                	lw	a5,4(a3)
    1480:	4018                	lw	a4,0(s0)
    1482:	0ff5f593          	andi	a1,a1,255
    1486:	0807f793          	andi	a5,a5,128
    148a:	00b75533          	srl	a0,a4,a1
    148e:	e781                	bnez	a5,1496 <RCC_GetClocksFreq+0x54>
    1490:	853a                	mv	a0,a4
    1492:	c43fe0ef          	jal	ra,d4 <__udivsi3>
    1496:	c048                	sw	a0,4(s0)
    1498:	c408                	sw	a0,8(s0)
    149a:	c448                	sw	a0,12(s0)
    149c:	400217b7          	lui	a5,0x40021
    14a0:	43dc                	lw	a5,4(a5)
    14a2:	468d                	li	a3,3
    14a4:	83ad                	srli	a5,a5,0xb
    14a6:	8bfd                	andi	a5,a5,31
    14a8:	0037d713          	srli	a4,a5,0x3
    14ac:	078a                	slli	a5,a5,0x2
    14ae:	8bf1                	andi	a5,a5,28
    14b0:	8fd9                	or	a5,a5,a4
    14b2:	0137f613          	andi	a2,a5,19
    14b6:	0037f713          	andi	a4,a5,3
    14ba:	00c6f463          	bgeu	a3,a2,14c2 <RCC_GetClocksFreq+0x80>
    14be:	ff478713          	addi	a4,a5,-12 # 40020ff4 <__global_pointer$+0x200207b4>
    14c2:	200007b7          	lui	a5,0x20000
    14c6:	01878793          	addi	a5,a5,24 # 20000018 <ADCPrescTable>
    14ca:	97ba                	add	a5,a5,a4
    14cc:	238c                	lbu	a1,0(a5)
    14ce:	c07fe0ef          	jal	ra,d4 <__udivsi3>
    14d2:	c808                	sw	a0,16(s0)
    14d4:	bd7fe06f          	j	aa <__riscv_restore_0>
    14d8:	435c                	lw	a5,4(a4)
    14da:	02dc77b7          	lui	a5,0x2dc7
    14de:	c0078793          	addi	a5,a5,-1024 # 2dc6c00 <_data_lma+0x2dc4538>
    14e2:	b749                	j	1464 <RCC_GetClocksFreq+0x22>

000014e4 <RCC_APB2PeriphClockCmd>:
    14e4:	c599                	beqz	a1,14f2 <RCC_APB2PeriphClockCmd+0xe>
    14e6:	40021737          	lui	a4,0x40021
    14ea:	4f1c                	lw	a5,24(a4)
    14ec:	8d5d                	or	a0,a0,a5
    14ee:	cf08                	sw	a0,24(a4)
    14f0:	8082                	ret
    14f2:	400217b7          	lui	a5,0x40021
    14f6:	4f98                	lw	a4,24(a5)
    14f8:	fff54513          	not	a0,a0
    14fc:	8d79                	and	a0,a0,a4
    14fe:	cf88                	sw	a0,24(a5)
    1500:	8082                	ret

00001502 <RCC_APB1PeriphClockCmd>:
    1502:	c599                	beqz	a1,1510 <RCC_APB1PeriphClockCmd+0xe>
    1504:	40021737          	lui	a4,0x40021
    1508:	4f5c                	lw	a5,28(a4)
    150a:	8d5d                	or	a0,a0,a5
    150c:	cf48                	sw	a0,28(a4)
    150e:	8082                	ret
    1510:	400217b7          	lui	a5,0x40021
    1514:	4fd8                	lw	a4,28(a5)
    1516:	fff54513          	not	a0,a0
    151a:	8d79                	and	a0,a0,a4
    151c:	cfc8                	sw	a0,28(a5)
    151e:	8082                	ret

00001520 <TIM_TimeBaseInit>:
    1520:	211e                	lhu	a5,0(a0)
    1522:	40013737          	lui	a4,0x40013
    1526:	c0070713          	addi	a4,a4,-1024 # 40012c00 <__global_pointer$+0x200123c0>
    152a:	07c2                	slli	a5,a5,0x10
    152c:	83c1                	srli	a5,a5,0x10
    152e:	00e50663          	beq	a0,a4,153a <TIM_TimeBaseInit+0x1a>
    1532:	40000737          	lui	a4,0x40000
    1536:	00e51663          	bne	a0,a4,1542 <TIM_TimeBaseInit+0x22>
    153a:	21ba                	lhu	a4,2(a1)
    153c:	f8f7f793          	andi	a5,a5,-113
    1540:	8fd9                	or	a5,a5,a4
    1542:	21fa                	lhu	a4,6(a1)
    1544:	cff7f793          	andi	a5,a5,-769
    1548:	07c2                	slli	a5,a5,0x10
    154a:	83c1                	srli	a5,a5,0x10
    154c:	8fd9                	or	a5,a5,a4
    154e:	a11e                	sh	a5,0(a0)
    1550:	21de                	lhu	a5,4(a1)
    1552:	b55e                	sh	a5,44(a0)
    1554:	219e                	lhu	a5,0(a1)
    1556:	b51e                	sh	a5,40(a0)
    1558:	400137b7          	lui	a5,0x40013
    155c:	c0078793          	addi	a5,a5,-1024 # 40012c00 <__global_pointer$+0x200123c0>
    1560:	00f51463          	bne	a0,a5,1568 <TIM_TimeBaseInit+0x48>
    1564:	259c                	lbu	a5,8(a1)
    1566:	b91e                	sh	a5,48(a0)
    1568:	4785                	li	a5,1
    156a:	a95e                	sh	a5,20(a0)
    156c:	8082                	ret

0000156e <TIM_OC1Init>:
    156e:	311e                	lhu	a5,32(a0)
    1570:	2192                	lhu	a2,0(a1)
    1572:	0025d303          	lhu	t1,2(a1)
    1576:	07c2                	slli	a5,a5,0x10
    1578:	83c1                	srli	a5,a5,0x10
    157a:	9bf9                	andi	a5,a5,-2
    157c:	07c2                	slli	a5,a5,0x10
    157e:	83c1                	srli	a5,a5,0x10
    1580:	b11e                	sh	a5,32(a0)
    1582:	311e                	lhu	a5,32(a0)
    1584:	2156                	lhu	a3,4(a0)
    1586:	2d1a                	lhu	a4,24(a0)
    1588:	07c2                	slli	a5,a5,0x10
    158a:	83c1                	srli	a5,a5,0x10
    158c:	0742                	slli	a4,a4,0x10
    158e:	8341                	srli	a4,a4,0x10
    1590:	f8c77713          	andi	a4,a4,-116
    1594:	8f51                	or	a4,a4,a2
    1596:	2592                	lhu	a2,8(a1)
    1598:	9bf5                	andi	a5,a5,-3
    159a:	06c2                	slli	a3,a3,0x10
    159c:	00666633          	or	a2,a2,t1
    15a0:	8fd1                	or	a5,a5,a2
    15a2:	40013637          	lui	a2,0x40013
    15a6:	c0060613          	addi	a2,a2,-1024 # 40012c00 <__global_pointer$+0x200123c0>
    15aa:	82c1                	srli	a3,a3,0x10
    15ac:	02c51063          	bne	a0,a2,15cc <TIM_OC1Init+0x5e>
    15b0:	25b2                	lhu	a2,10(a1)
    15b2:	9bdd                	andi	a5,a5,-9
    15b4:	00e5d303          	lhu	t1,14(a1)
    15b8:	8fd1                	or	a5,a5,a2
    15ba:	21d2                	lhu	a2,4(a1)
    15bc:	9bed                	andi	a5,a5,-5
    15be:	cff6f693          	andi	a3,a3,-769
    15c2:	8fd1                	or	a5,a5,a2
    15c4:	25d2                	lhu	a2,12(a1)
    15c6:	00666633          	or	a2,a2,t1
    15ca:	8ed1                	or	a3,a3,a2
    15cc:	a156                	sh	a3,4(a0)
    15ce:	ad1a                	sh	a4,24(a0)
    15d0:	21fa                	lhu	a4,6(a1)
    15d2:	d958                	sw	a4,52(a0)
    15d4:	b11e                	sh	a5,32(a0)
    15d6:	8082                	ret

000015d8 <TIM_Cmd>:
    15d8:	211e                	lhu	a5,0(a0)
    15da:	c589                	beqz	a1,15e4 <TIM_Cmd+0xc>
    15dc:	0017e793          	ori	a5,a5,1
    15e0:	a11e                	sh	a5,0(a0)
    15e2:	8082                	ret
    15e4:	07c2                	slli	a5,a5,0x10
    15e6:	83c1                	srli	a5,a5,0x10
    15e8:	9bf9                	andi	a5,a5,-2
    15ea:	07c2                	slli	a5,a5,0x10
    15ec:	83c1                	srli	a5,a5,0x10
    15ee:	bfcd                	j	15e0 <TIM_Cmd+0x8>

000015f0 <TIM_CtrlPWMOutputs>:
    15f0:	04455783          	lhu	a5,68(a0)
    15f4:	c591                	beqz	a1,1600 <TIM_CtrlPWMOutputs+0x10>
    15f6:	6721                	lui	a4,0x8
    15f8:	8fd9                	or	a5,a5,a4
    15fa:	04f51223          	sh	a5,68(a0)
    15fe:	8082                	ret
    1600:	07c6                	slli	a5,a5,0x11
    1602:	83c5                	srli	a5,a5,0x11
    1604:	bfdd                	j	15fa <TIM_CtrlPWMOutputs+0xa>

00001606 <TIM_ITConfig>:
    1606:	255e                	lhu	a5,12(a0)
    1608:	c601                	beqz	a2,1610 <TIM_ITConfig+0xa>
    160a:	8ddd                	or	a1,a1,a5
    160c:	a54e                	sh	a1,12(a0)
    160e:	8082                	ret
    1610:	fff5c593          	not	a1,a1
    1614:	8dfd                	and	a1,a1,a5
    1616:	bfdd                	j	160c <TIM_ITConfig+0x6>

00001618 <TIM_ARRPreloadConfig>:
    1618:	211e                	lhu	a5,0(a0)
    161a:	c589                	beqz	a1,1624 <TIM_ARRPreloadConfig+0xc>
    161c:	0807e793          	ori	a5,a5,128
    1620:	a11e                	sh	a5,0(a0)
    1622:	8082                	ret
    1624:	07c2                	slli	a5,a5,0x10
    1626:	83c1                	srli	a5,a5,0x10
    1628:	f7f7f793          	andi	a5,a5,-129
    162c:	07c2                	slli	a5,a5,0x10
    162e:	83c1                	srli	a5,a5,0x10
    1630:	bfc5                	j	1620 <TIM_ARRPreloadConfig+0x8>

00001632 <TIM_OC1PreloadConfig>:
    1632:	2d1e                	lhu	a5,24(a0)
    1634:	07c2                	slli	a5,a5,0x10
    1636:	83c1                	srli	a5,a5,0x10
    1638:	9bdd                	andi	a5,a5,-9
    163a:	8ddd                	or	a1,a1,a5
    163c:	ad0e                	sh	a1,24(a0)
    163e:	8082                	ret

00001640 <TIM_SetCompare1>:
    1640:	d94c                	sw	a1,52(a0)
    1642:	8082                	ret

00001644 <TIM_GetITStatus>:
    1644:	291e                	lhu	a5,16(a0)
    1646:	254a                	lhu	a0,12(a0)
    1648:	8fed                	and	a5,a5,a1
    164a:	0542                	slli	a0,a0,0x10
    164c:	8141                	srli	a0,a0,0x10
    164e:	c789                	beqz	a5,1658 <TIM_GetITStatus+0x14>
    1650:	8d6d                	and	a0,a0,a1
    1652:	00a03533          	snez	a0,a0
    1656:	8082                	ret
    1658:	4501                	li	a0,0
    165a:	8082                	ret

0000165c <TIM_ClearITPendingBit>:
    165c:	fff5c593          	not	a1,a1
    1660:	05c2                	slli	a1,a1,0x10
    1662:	81c1                	srli	a1,a1,0x10
    1664:	a90e                	sh	a1,16(a0)
    1666:	8082                	ret

00001668 <USART_Init>:
    1668:	a39fe2ef          	jal	t0,a0 <__riscv_save_0>
    166c:	2916                	lhu	a3,16(a0)
    166e:	77f5                	lui	a5,0xffffd
    1670:	17fd                	addi	a5,a5,-1
    1672:	8ff5                	and	a5,a5,a3
    1674:	21f6                	lhu	a3,6(a1)
    1676:	25da                	lhu	a4,12(a1)
    1678:	1121                	addi	sp,sp,-24
    167a:	8fd5                	or	a5,a5,a3
    167c:	a91e                	sh	a5,16(a0)
    167e:	2556                	lhu	a3,12(a0)
    1680:	77fd                	lui	a5,0xfffff
    1682:	9f378793          	addi	a5,a5,-1549 # ffffe9f3 <__global_pointer$+0xdfffe1b3>
    1686:	8ff5                	and	a5,a5,a3
    1688:	21d6                	lhu	a3,4(a1)
    168a:	842a                	mv	s0,a0
    168c:	c02e                	sw	a1,0(sp)
    168e:	8fd5                	or	a5,a5,a3
    1690:	2596                	lhu	a3,8(a1)
    1692:	8fd5                	or	a5,a5,a3
    1694:	25b6                	lhu	a3,10(a1)
    1696:	8fd5                	or	a5,a5,a3
    1698:	a55e                	sh	a5,12(a0)
    169a:	295e                	lhu	a5,20(a0)
    169c:	07c2                	slli	a5,a5,0x10
    169e:	83c1                	srli	a5,a5,0x10
    16a0:	cff7f793          	andi	a5,a5,-769
    16a4:	8f5d                	or	a4,a4,a5
    16a6:	a95a                	sh	a4,20(a0)
    16a8:	0048                	addi	a0,sp,4
    16aa:	d99ff0ef          	jal	ra,1442 <RCC_GetClocksFreq>
    16ae:	400147b7          	lui	a5,0x40014
    16b2:	80078793          	addi	a5,a5,-2048 # 40013800 <__global_pointer$+0x20012fc0>
    16b6:	4582                	lw	a1,0(sp)
    16b8:	06f41263          	bne	s0,a5,171c <USART_Init+0xb4>
    16bc:	47c2                	lw	a5,16(sp)
    16be:	245a                	lhu	a4,12(s0)
    16c0:	00179513          	slli	a0,a5,0x1
    16c4:	953e                	add	a0,a0,a5
    16c6:	0742                	slli	a4,a4,0x10
    16c8:	050e                	slli	a0,a0,0x3
    16ca:	8741                	srai	a4,a4,0x10
    16cc:	953e                	add	a0,a0,a5
    16ce:	418c                	lw	a1,0(a1)
    16d0:	04075863          	bgez	a4,1720 <USART_Init+0xb8>
    16d4:	0586                	slli	a1,a1,0x1
    16d6:	9fffe0ef          	jal	ra,d4 <__udivsi3>
    16da:	06400593          	li	a1,100
    16de:	c02a                	sw	a0,0(sp)
    16e0:	9f5fe0ef          	jal	ra,d4 <__udivsi3>
    16e4:	4782                	lw	a5,0(sp)
    16e6:	00451493          	slli	s1,a0,0x4
    16ea:	06400593          	li	a1,100
    16ee:	853e                	mv	a0,a5
    16f0:	a11fe0ef          	jal	ra,100 <__umodsi3>
    16f4:	245e                	lhu	a5,12(s0)
    16f6:	07c2                	slli	a5,a5,0x10
    16f8:	87c1                	srai	a5,a5,0x10
    16fa:	0207d563          	bgez	a5,1724 <USART_Init+0xbc>
    16fe:	050e                	slli	a0,a0,0x3
    1700:	06400593          	li	a1,100
    1704:	03250513          	addi	a0,a0,50
    1708:	9cdfe0ef          	jal	ra,d4 <__udivsi3>
    170c:	891d                	andi	a0,a0,7
    170e:	8cc9                	or	s1,s1,a0
    1710:	04c2                	slli	s1,s1,0x10
    1712:	80c1                	srli	s1,s1,0x10
    1714:	a406                	sh	s1,8(s0)
    1716:	0161                	addi	sp,sp,24
    1718:	993fe06f          	j	aa <__riscv_restore_0>
    171c:	47b2                	lw	a5,12(sp)
    171e:	b745                	j	16be <USART_Init+0x56>
    1720:	058a                	slli	a1,a1,0x2
    1722:	bf55                	j	16d6 <USART_Init+0x6e>
    1724:	0512                	slli	a0,a0,0x4
    1726:	06400593          	li	a1,100
    172a:	03250513          	addi	a0,a0,50
    172e:	9a7fe0ef          	jal	ra,d4 <__udivsi3>
    1732:	893d                	andi	a0,a0,15
    1734:	bfe9                	j	170e <USART_Init+0xa6>

00001736 <USART_Cmd>:
    1736:	c591                	beqz	a1,1742 <USART_Cmd+0xc>
    1738:	255e                	lhu	a5,12(a0)
    173a:	6709                	lui	a4,0x2
    173c:	8fd9                	or	a5,a5,a4
    173e:	a55e                	sh	a5,12(a0)
    1740:	8082                	ret
    1742:	255a                	lhu	a4,12(a0)
    1744:	77f9                	lui	a5,0xffffe
    1746:	17fd                	addi	a5,a5,-1
    1748:	8ff9                	and	a5,a5,a4
    174a:	bfd5                	j	173e <USART_Cmd+0x8>

0000174c <USART_SendData>:
    174c:	1ff5f593          	andi	a1,a1,511
    1750:	a14e                	sh	a1,4(a0)
    1752:	8082                	ret

00001754 <USART_GetFlagStatus>:
    1754:	210a                	lhu	a0,0(a0)
    1756:	8d6d                	and	a0,a0,a1
    1758:	00a03533          	snez	a0,a0
    175c:	8082                	ret

0000175e <Delay_Init>:
    175e:	943fe2ef          	jal	t0,a0 <__riscv_save_0>
    1762:	200007b7          	lui	a5,0x20000
    1766:	0147a503          	lw	a0,20(a5) # 20000014 <SystemCoreClock>
    176a:	007a15b7          	lui	a1,0x7a1
    176e:	20058593          	addi	a1,a1,512 # 7a1200 <_data_lma+0x79eb38>
    1772:	963fe0ef          	jal	ra,d4 <__udivsi3>
    1776:	0ff57513          	andi	a0,a0,255
    177a:	98a18923          	sb	a0,-1646(gp) # 200001d2 <p_us>
    177e:	00551793          	slli	a5,a0,0x5
    1782:	8f89                	sub	a5,a5,a0
    1784:	078a                	slli	a5,a5,0x2
    1786:	953e                	add	a0,a0,a5
    1788:	050e                	slli	a0,a0,0x3
    178a:	98a19823          	sh	a0,-1648(gp) # 200001d0 <p_ms>
    178e:	91dfe06f          	j	aa <__riscv_restore_0>

00001792 <Delay_Ms>:
    1792:	90ffe2ef          	jal	t0,a0 <__riscv_save_0>
    1796:	e000f437          	lui	s0,0xe000f
    179a:	405c                	lw	a5,4(s0)
    179c:	85aa                	mv	a1,a0
    179e:	9bf9                	andi	a5,a5,-2
    17a0:	c05c                	sw	a5,4(s0)
    17a2:	9901d503          	lhu	a0,-1648(gp) # 200001d0 <p_ms>
    17a6:	90ffe0ef          	jal	ra,b4 <__mulsi3>
    17aa:	c808                	sw	a0,16(s0)
    17ac:	00042423          	sw	zero,8(s0) # e000f008 <__global_pointer$+0xc000e7c8>
    17b0:	401c                	lw	a5,0(s0)
    17b2:	0017e793          	ori	a5,a5,1
    17b6:	c01c                	sw	a5,0(s0)
    17b8:	e000f7b7          	lui	a5,0xe000f
    17bc:	43d8                	lw	a4,4(a5)
    17be:	8b05                	andi	a4,a4,1
    17c0:	df75                	beqz	a4,17bc <Delay_Ms+0x2a>
    17c2:	4398                	lw	a4,0(a5)
    17c4:	9b79                	andi	a4,a4,-2
    17c6:	c398                	sw	a4,0(a5)
    17c8:	8e3fe06f          	j	aa <__riscv_restore_0>

000017cc <USART_Printf_Init>:
    17cc:	8d5fe2ef          	jal	t0,a0 <__riscv_save_0>
    17d0:	842a                	mv	s0,a0
    17d2:	6511                	lui	a0,0x4
    17d4:	1111                	addi	sp,sp,-28
    17d6:	4585                	li	a1,1
    17d8:	02050513          	addi	a0,a0,32 # 4020 <_data_lma+0x1958>
    17dc:	d09ff0ef          	jal	ra,14e4 <RCC_APB2PeriphClockCmd>
    17e0:	02000793          	li	a5,32
    17e4:	807c                	sh	a5,0(sp)
    17e6:	40011537          	lui	a0,0x40011
    17ea:	478d                	li	a5,3
    17ec:	c23e                	sw	a5,4(sp)
    17ee:	858a                	mv	a1,sp
    17f0:	47e1                	li	a5,24
    17f2:	40050513          	addi	a0,a0,1024 # 40011400 <__global_pointer$+0x20010bc0>
    17f6:	c43e                	sw	a5,8(sp)
    17f8:	b29ff0ef          	jal	ra,1320 <GPIO_Init>
    17fc:	c622                	sw	s0,12(sp)
    17fe:	40014437          	lui	s0,0x40014
    1802:	000807b7          	lui	a5,0x80
    1806:	006c                	addi	a1,sp,12
    1808:	80040513          	addi	a0,s0,-2048 # 40013800 <__global_pointer$+0x20012fc0>
    180c:	ca3e                	sw	a5,20(sp)
    180e:	c802                	sw	zero,16(sp)
    1810:	00011c23          	sh	zero,24(sp)
    1814:	3d91                	jal	1668 <USART_Init>
    1816:	4585                	li	a1,1
    1818:	80040513          	addi	a0,s0,-2048
    181c:	3f29                	jal	1736 <USART_Cmd>
    181e:	0171                	addi	sp,sp,28
    1820:	88bfe06f          	j	aa <__riscv_restore_0>

00001824 <_write>:
    1824:	87dfe2ef          	jal	t0,a0 <__riscv_save_0>
    1828:	1171                	addi	sp,sp,-4
    182a:	84ae                	mv	s1,a1
    182c:	4401                	li	s0,0
    182e:	02c45d63          	bge	s0,a2,1868 <_write+0x44>
    1832:	400147b7          	lui	a5,0x40014
    1836:	80078713          	addi	a4,a5,-2048 # 40013800 <__global_pointer$+0x20012fc0>
    183a:	853a                	mv	a0,a4
    183c:	04000593          	li	a1,64
    1840:	c032                	sw	a2,0(sp)
    1842:	3f09                	jal	1754 <USART_GetFlagStatus>
    1844:	400147b7          	lui	a5,0x40014
    1848:	80078713          	addi	a4,a5,-2048 # 40013800 <__global_pointer$+0x20012fc0>
    184c:	4602                	lw	a2,0(sp)
    184e:	d575                	beqz	a0,183a <_write+0x16>
    1850:	00848733          	add	a4,s1,s0
    1854:	00070583          	lb	a1,0(a4) # 2000 <CSWTCH.3+0xc>
    1858:	80078513          	addi	a0,a5,-2048
    185c:	0405                	addi	s0,s0,1
    185e:	05c2                	slli	a1,a1,0x10
    1860:	81c1                	srli	a1,a1,0x10
    1862:	35ed                	jal	174c <USART_SendData>
    1864:	4602                	lw	a2,0(sp)
    1866:	b7e1                	j	182e <_write+0xa>
    1868:	8532                	mv	a0,a2
    186a:	0111                	addi	sp,sp,4
    186c:	83ffe06f          	j	aa <__riscv_restore_0>

00001870 <printchar>:
    1870:	1141                	addi	sp,sp,-16
    1872:	c606                	sw	ra,12(sp)
    1874:	c02e                	sw	a1,0(sp)
    1876:	cd0d                	beqz	a0,18b0 <printchar+0x40>
    1878:	4118                	lw	a4,0(a0)
    187a:	87aa                	mv	a5,a0
    187c:	c305                	beqz	a4,189c <printchar+0x2c>
    187e:	4158                	lw	a4,4(a0)
    1880:	557d                	li	a0,-1
    1882:	cb11                	beqz	a4,1896 <printchar+0x26>
    1884:	4685                	li	a3,1
    1886:	00d71b63          	bne	a4,a3,189c <printchar+0x2c>
    188a:	4798                	lw	a4,8(a5)
    188c:	00070023          	sb	zero,0(a4)
    1890:	0007a223          	sw	zero,4(a5)
    1894:	4505                	li	a0,1
    1896:	40b2                	lw	ra,12(sp)
    1898:	0141                	addi	sp,sp,16
    189a:	8082                	ret
    189c:	4798                	lw	a4,8(a5)
    189e:	4682                	lw	a3,0(sp)
    18a0:	a314                	sb	a3,0(a4)
    18a2:	4798                	lw	a4,8(a5)
    18a4:	0705                	addi	a4,a4,1
    18a6:	c798                	sw	a4,8(a5)
    18a8:	43d8                	lw	a4,4(a5)
    18aa:	177d                	addi	a4,a4,-1
    18ac:	c3d8                	sw	a4,4(a5)
    18ae:	b7dd                	j	1894 <printchar+0x24>
    18b0:	4605                	li	a2,1
    18b2:	858a                	mv	a1,sp
    18b4:	3f85                	jal	1824 <_write>
    18b6:	bff9                	j	1894 <printchar+0x24>

000018b8 <prints>:
    18b8:	1101                	addi	sp,sp,-32
    18ba:	cc22                	sw	s0,24(sp)
    18bc:	c22e                	sw	a1,4(sp)
    18be:	ce06                	sw	ra,28(sp)
    18c0:	ca26                	sw	s1,20(sp)
    18c2:	842a                	mv	s0,a0
    18c4:	4781                	li	a5,0
    18c6:	02000593          	li	a1,32
    18ca:	02064563          	bltz	a2,18f4 <prints+0x3c>
    18ce:	4592                	lw	a1,4(sp)
    18d0:	95be                	add	a1,a1,a5
    18d2:	00058583          	lb	a1,0(a1)
    18d6:	e58d                	bnez	a1,1900 <prints+0x48>
    18d8:	02c7d863          	bge	a5,a2,1908 <prints+0x50>
    18dc:	02e7d463          	bge	a5,a4,1904 <prints+0x4c>
    18e0:	8e19                	sub	a2,a2,a4
    18e2:	02000513          	li	a0,32
    18e6:	0026f593          	andi	a1,a3,2
    18ea:	c02a                	sw	a0,0(sp)
    18ec:	c589                	beqz	a1,18f6 <prints+0x3e>
    18ee:	e701                	bnez	a4,18f6 <prints+0x3e>
    18f0:	03000593          	li	a1,48
    18f4:	c02e                	sw	a1,0(sp)
    18f6:	8a85                	andi	a3,a3,1
    18f8:	4481                	li	s1,0
    18fa:	ea95                	bnez	a3,192e <prints+0x76>
    18fc:	84b2                	mv	s1,a2
    18fe:	a00d                	j	1920 <prints+0x68>
    1900:	0785                	addi	a5,a5,1
    1902:	b7f1                	j	18ce <prints+0x16>
    1904:	8e1d                	sub	a2,a2,a5
    1906:	bff1                	j	18e2 <prints+0x2a>
    1908:	4601                	li	a2,0
    190a:	bfe1                	j	18e2 <prints+0x2a>
    190c:	4582                	lw	a1,0(sp)
    190e:	8522                	mv	a0,s0
    1910:	c83a                	sw	a4,16(sp)
    1912:	c632                	sw	a2,12(sp)
    1914:	c43e                	sw	a5,8(sp)
    1916:	3fa9                	jal	1870 <printchar>
    1918:	47a2                	lw	a5,8(sp)
    191a:	4632                	lw	a2,12(sp)
    191c:	4742                	lw	a4,16(sp)
    191e:	14fd                	addi	s1,s1,-1
    1920:	fe9046e3          	bgtz	s1,190c <prints+0x54>
    1924:	84b2                	mv	s1,a2
    1926:	00065363          	bgez	a2,192c <prints+0x74>
    192a:	4481                	li	s1,0
    192c:	8e05                	sub	a2,a2,s1
    192e:	02e7c763          	blt	a5,a4,195c <prints+0xa4>
    1932:	87a6                	mv	a5,s1
    1934:	4692                	lw	a3,4(sp)
    1936:	40978733          	sub	a4,a5,s1
    193a:	9736                	add	a4,a4,a3
    193c:	00070583          	lb	a1,0(a4)
    1940:	ed95                	bnez	a1,197c <prints+0xc4>
    1942:	84b2                	mv	s1,a2
    1944:	04904463          	bgtz	s1,198c <prints+0xd4>
    1948:	00065363          	bgez	a2,194e <prints+0x96>
    194c:	4601                	li	a2,0
    194e:	40f2                	lw	ra,28(sp)
    1950:	4462                	lw	s0,24(sp)
    1952:	44d2                	lw	s1,20(sp)
    1954:	00f60533          	add	a0,a2,a5
    1958:	6105                	addi	sp,sp,32
    195a:	8082                	ret
    195c:	8f1d                	sub	a4,a4,a5
    195e:	87ba                	mv	a5,a4
    1960:	03000593          	li	a1,48
    1964:	8522                	mv	a0,s0
    1966:	c832                	sw	a2,16(sp)
    1968:	c63e                	sw	a5,12(sp)
    196a:	c43a                	sw	a4,8(sp)
    196c:	3711                	jal	1870 <printchar>
    196e:	47b2                	lw	a5,12(sp)
    1970:	4722                	lw	a4,8(sp)
    1972:	4642                	lw	a2,16(sp)
    1974:	17fd                	addi	a5,a5,-1
    1976:	f7ed                	bnez	a5,1960 <prints+0xa8>
    1978:	94ba                	add	s1,s1,a4
    197a:	bf65                	j	1932 <prints+0x7a>
    197c:	8522                	mv	a0,s0
    197e:	c632                	sw	a2,12(sp)
    1980:	c43e                	sw	a5,8(sp)
    1982:	35fd                	jal	1870 <printchar>
    1984:	47a2                	lw	a5,8(sp)
    1986:	4632                	lw	a2,12(sp)
    1988:	0785                	addi	a5,a5,1
    198a:	b76d                	j	1934 <prints+0x7c>
    198c:	4582                	lw	a1,0(sp)
    198e:	8522                	mv	a0,s0
    1990:	c432                	sw	a2,8(sp)
    1992:	c23e                	sw	a5,4(sp)
    1994:	3df1                	jal	1870 <printchar>
    1996:	14fd                	addi	s1,s1,-1
    1998:	4622                	lw	a2,8(sp)
    199a:	4792                	lw	a5,4(sp)
    199c:	b765                	j	1944 <prints+0x8c>

0000199e <printInt>:
    199e:	7139                	addi	sp,sp,-64
    19a0:	de06                	sw	ra,60(sp)
    19a2:	dc22                	sw	s0,56(sp)
    19a4:	da26                	sw	s1,52(sp)
    19a6:	c23e                	sw	a5,4(sp)
    19a8:	8332                	mv	t1,a2
    19aa:	863a                	mv	a2,a4
    19ac:	ed89                	bnez	a1,19c6 <printInt+0x28>
    19ae:	4692                	lw	a3,4(sp)
    19b0:	03000793          	li	a5,48
    19b4:	4701                	li	a4,0
    19b6:	086c                	addi	a1,sp,28
    19b8:	86fc                	sh	a5,28(sp)
    19ba:	3dfd                	jal	18b8 <prints>
    19bc:	50f2                	lw	ra,60(sp)
    19be:	5462                	lw	s0,56(sp)
    19c0:	54d2                	lw	s1,52(sp)
    19c2:	6121                	addi	sp,sp,64
    19c4:	8082                	ret
    19c6:	84aa                	mv	s1,a0
    19c8:	8436                	mv	s0,a3
    19ca:	87ae                	mv	a5,a1
    19cc:	ca91                	beqz	a3,19e0 <printInt+0x42>
    19ce:	4729                	li	a4,10
    19d0:	4401                	li	s0,0
    19d2:	00e31763          	bne	t1,a4,19e0 <printInt+0x42>
    19d6:	0005d563          	bgez	a1,19e0 <printInt+0x42>
    19da:	40b007b3          	neg	a5,a1
    19de:	4405                	li	s0,1
    19e0:	4686                	lw	a3,64(sp)
    19e2:	020109a3          	sb	zero,51(sp)
    19e6:	03310713          	addi	a4,sp,51
    19ea:	fc668693          	addi	a3,a3,-58 # 40020fc6 <__global_pointer$+0x20020786>
    19ee:	c436                	sw	a3,8(sp)
    19f0:	859a                	mv	a1,t1
    19f2:	853e                	mv	a0,a5
    19f4:	ca32                	sw	a2,20(sp)
    19f6:	c83a                	sw	a4,16(sp)
    19f8:	c61a                	sw	t1,12(sp)
    19fa:	c03e                	sw	a5,0(sp)
    19fc:	f04fe0ef          	jal	ra,100 <__umodsi3>
    1a00:	46a5                	li	a3,9
    1a02:	4782                	lw	a5,0(sp)
    1a04:	4332                	lw	t1,12(sp)
    1a06:	4742                	lw	a4,16(sp)
    1a08:	4652                	lw	a2,20(sp)
    1a0a:	00a6d463          	bge	a3,a0,1a12 <printInt+0x74>
    1a0e:	46a2                	lw	a3,8(sp)
    1a10:	9536                	add	a0,a0,a3
    1a12:	03050513          	addi	a0,a0,48
    1a16:	fff70693          	addi	a3,a4,-1
    1a1a:	fea70fa3          	sb	a0,-1(a4)
    1a1e:	859a                	mv	a1,t1
    1a20:	853e                	mv	a0,a5
    1a22:	cc32                	sw	a2,24(sp)
    1a24:	ca3a                	sw	a4,20(sp)
    1a26:	c81a                	sw	t1,16(sp)
    1a28:	c63e                	sw	a5,12(sp)
    1a2a:	c036                	sw	a3,0(sp)
    1a2c:	ea8fe0ef          	jal	ra,d4 <__udivsi3>
    1a30:	47b2                	lw	a5,12(sp)
    1a32:	4342                	lw	t1,16(sp)
    1a34:	4752                	lw	a4,20(sp)
    1a36:	4662                	lw	a2,24(sp)
    1a38:	0467fd63          	bgeu	a5,t1,1a92 <printInt+0xf4>
    1a3c:	cc09                	beqz	s0,1a56 <printInt+0xb8>
    1a3e:	ce29                	beqz	a2,1a98 <printInt+0xfa>
    1a40:	4792                	lw	a5,4(sp)
    1a42:	8b89                	andi	a5,a5,2
    1a44:	cbb1                	beqz	a5,1a98 <printInt+0xfa>
    1a46:	02d00593          	li	a1,45
    1a4a:	8526                	mv	a0,s1
    1a4c:	c432                	sw	a2,8(sp)
    1a4e:	e23ff0ef          	jal	ra,1870 <printchar>
    1a52:	4622                	lw	a2,8(sp)
    1a54:	167d                	addi	a2,a2,-1
    1a56:	4792                	lw	a5,4(sp)
    1a58:	8b91                	andi	a5,a5,4
    1a5a:	c785                	beqz	a5,1a82 <printInt+0xe4>
    1a5c:	4706                	lw	a4,64(sp)
    1a5e:	06100793          	li	a5,97
    1a62:	c432                	sw	a2,8(sp)
    1a64:	03000593          	li	a1,48
    1a68:	8526                	mv	a0,s1
    1a6a:	04f71163          	bne	a4,a5,1aac <printInt+0x10e>
    1a6e:	e03ff0ef          	jal	ra,1870 <printchar>
    1a72:	07800593          	li	a1,120
    1a76:	8526                	mv	a0,s1
    1a78:	df9ff0ef          	jal	ra,1870 <printchar>
    1a7c:	4622                	lw	a2,8(sp)
    1a7e:	0409                	addi	s0,s0,2
    1a80:	1679                	addi	a2,a2,-2
    1a82:	4716                	lw	a4,68(sp)
    1a84:	4692                	lw	a3,4(sp)
    1a86:	4582                	lw	a1,0(sp)
    1a88:	8526                	mv	a0,s1
    1a8a:	e2fff0ef          	jal	ra,18b8 <prints>
    1a8e:	9522                	add	a0,a0,s0
    1a90:	b735                	j	19bc <printInt+0x1e>
    1a92:	87aa                	mv	a5,a0
    1a94:	4702                	lw	a4,0(sp)
    1a96:	bfa9                	j	19f0 <printInt+0x52>
    1a98:	4682                	lw	a3,0(sp)
    1a9a:	02d00793          	li	a5,45
    1a9e:	4401                	li	s0,0
    1aa0:	fef68fa3          	sb	a5,-1(a3)
    1aa4:	ffe70793          	addi	a5,a4,-2
    1aa8:	c03e                	sw	a5,0(sp)
    1aaa:	b775                	j	1a56 <printInt+0xb8>
    1aac:	dc5ff0ef          	jal	ra,1870 <printchar>
    1ab0:	05800593          	li	a1,88
    1ab4:	b7c9                	j	1a76 <printInt+0xd8>

00001ab6 <printLongLongInt>:
    1ab6:	4501                	li	a0,0
    1ab8:	8082                	ret

00001aba <printDouble>:
    1aba:	4501                	li	a0,0
    1abc:	8082                	ret

00001abe <print>:
    1abe:	fd810113          	addi	sp,sp,-40
    1ac2:	d022                	sw	s0,32(sp)
    1ac4:	ce26                	sw	s1,28(sp)
    1ac6:	d206                	sw	ra,36(sp)
    1ac8:	c42a                	sw	a0,8(sp)
    1aca:	82ae                	mv	t0,a1
    1acc:	8432                	mv	s0,a2
    1ace:	c602                	sw	zero,12(sp)
    1ad0:	4481                	li	s1,0
    1ad2:	00028583          	lb	a1,0(t0)
    1ad6:	ed91                	bnez	a1,1af2 <print+0x34>
    1ad8:	47a2                	lw	a5,8(sp)
    1ada:	c789                	beqz	a5,1ae4 <print+0x26>
    1adc:	4581                	li	a1,0
    1ade:	853e                	mv	a0,a5
    1ae0:	d91ff0ef          	jal	ra,1870 <printchar>
    1ae4:	5092                	lw	ra,36(sp)
    1ae6:	5402                	lw	s0,32(sp)
    1ae8:	8526                	mv	a0,s1
    1aea:	44f2                	lw	s1,28(sp)
    1aec:	02810113          	addi	sp,sp,40
    1af0:	8082                	ret
    1af2:	02500793          	li	a5,37
    1af6:	00f58963          	beq	a1,a5,1b08 <print+0x4a>
    1afa:	4522                	lw	a0,8(sp)
    1afc:	c816                	sw	t0,16(sp)
    1afe:	0485                	addi	s1,s1,1
    1b00:	d71ff0ef          	jal	ra,1870 <printchar>
    1b04:	42c2                	lw	t0,16(sp)
    1b06:	a005                	j	1b26 <print+0x68>
    1b08:	00128783          	lb	a5,1(t0)
    1b0c:	00128713          	addi	a4,t0,1
    1b10:	00b79d63          	bne	a5,a1,1b2a <print+0x6c>
    1b14:	4522                	lw	a0,8(sp)
    1b16:	02500593          	li	a1,37
    1b1a:	c83a                	sw	a4,16(sp)
    1b1c:	d55ff0ef          	jal	ra,1870 <printchar>
    1b20:	4742                	lw	a4,16(sp)
    1b22:	0485                	addi	s1,s1,1
    1b24:	82ba                	mv	t0,a4
    1b26:	0285                	addi	t0,t0,1
    1b28:	b76d                	j	1ad2 <print+0x14>
    1b2a:	d7dd                	beqz	a5,1ad8 <print+0x1a>
    1b2c:	02b00693          	li	a3,43
    1b30:	04d78963          	beq	a5,a3,1b82 <print+0xc4>
    1b34:	00f6c863          	blt	a3,a5,1b44 <print+0x86>
    1b38:	02300693          	li	a3,35
    1b3c:	04d78663          	beq	a5,a3,1b88 <print+0xca>
    1b40:	4781                	li	a5,0
    1b42:	a005                	j	1b62 <print+0xa4>
    1b44:	02d00693          	li	a3,45
    1b48:	00d78a63          	beq	a5,a3,1b5c <print+0x9e>
    1b4c:	03000693          	li	a3,48
    1b50:	fed798e3          	bne	a5,a3,1b40 <print+0x82>
    1b54:	00228713          	addi	a4,t0,2
    1b58:	4789                	li	a5,2
    1b5a:	a021                	j	1b62 <print+0xa4>
    1b5c:	00228713          	addi	a4,t0,2
    1b60:	4785                	li	a5,1
    1b62:	00070683          	lb	a3,0(a4)
    1b66:	02b00613          	li	a2,43
    1b6a:	04c68363          	beq	a3,a2,1bb0 <print+0xf2>
    1b6e:	02d64163          	blt	a2,a3,1b90 <print+0xd2>
    1b72:	02300613          	li	a2,35
    1b76:	02c68b63          	beq	a3,a2,1bac <print+0xee>
    1b7a:	82ba                	mv	t0,a4
    1b7c:	4501                	li	a0,0
    1b7e:	46a5                	li	a3,9
    1b80:	a081                	j	1bc0 <print+0x102>
    1b82:	00228713          	addi	a4,t0,2
    1b86:	bf6d                	j	1b40 <print+0x82>
    1b88:	00228713          	addi	a4,t0,2
    1b8c:	4791                	li	a5,4
    1b8e:	bfd1                	j	1b62 <print+0xa4>
    1b90:	02d00613          	li	a2,45
    1b94:	00c68963          	beq	a3,a2,1ba6 <print+0xe8>
    1b98:	03000613          	li	a2,48
    1b9c:	fcc69fe3          	bne	a3,a2,1b7a <print+0xbc>
    1ba0:	0027e793          	ori	a5,a5,2
    1ba4:	a031                	j	1bb0 <print+0xf2>
    1ba6:	0705                	addi	a4,a4,1
    1ba8:	4785                	li	a5,1
    1baa:	bfc1                	j	1b7a <print+0xbc>
    1bac:	0047e793          	ori	a5,a5,4
    1bb0:	0705                	addi	a4,a4,1
    1bb2:	b7e1                	j	1b7a <print+0xbc>
    1bb4:	00251613          	slli	a2,a0,0x2
    1bb8:	9532                	add	a0,a0,a2
    1bba:	0506                	slli	a0,a0,0x1
    1bbc:	953a                	add	a0,a0,a4
    1bbe:	0285                	addi	t0,t0,1
    1bc0:	00028603          	lb	a2,0(t0)
    1bc4:	fd060713          	addi	a4,a2,-48
    1bc8:	0ff77593          	andi	a1,a4,255
    1bcc:	feb6f4e3          	bgeu	a3,a1,1bb4 <print+0xf6>
    1bd0:	02e00713          	li	a4,46
    1bd4:	4699                	li	a3,6
    1bd6:	00e61e63          	bne	a2,a4,1bf2 <print+0x134>
    1bda:	0285                	addi	t0,t0,1
    1bdc:	4681                	li	a3,0
    1bde:	45a5                	li	a1,9
    1be0:	00028603          	lb	a2,0(t0)
    1be4:	fd060613          	addi	a2,a2,-48
    1be8:	0ff67713          	andi	a4,a2,255
    1bec:	02e5f563          	bgeu	a1,a4,1c16 <print+0x158>
    1bf0:	c636                	sw	a3,12(sp)
    1bf2:	00028703          	lb	a4,0(t0)
    1bf6:	06a00613          	li	a2,106
    1bfa:	0ac70d63          	beq	a4,a2,1cb4 <print+0x1f6>
    1bfe:	02e64363          	blt	a2,a4,1c24 <print+0x166>
    1c02:	04c00613          	li	a2,76
    1c06:	0ac70763          	beq	a4,a2,1cb4 <print+0x1f6>
    1c0a:	06800613          	li	a2,104
    1c0e:	08c70c63          	beq	a4,a2,1ca6 <print+0x1e8>
    1c12:	4581                	li	a1,0
    1c14:	a82d                	j	1c4e <print+0x190>
    1c16:	00269713          	slli	a4,a3,0x2
    1c1a:	96ba                	add	a3,a3,a4
    1c1c:	0686                	slli	a3,a3,0x1
    1c1e:	96b2                	add	a3,a3,a2
    1c20:	0285                	addi	t0,t0,1
    1c22:	bf7d                	j	1be0 <print+0x122>
    1c24:	07400613          	li	a2,116
    1c28:	08c70663          	beq	a4,a2,1cb4 <print+0x1f6>
    1c2c:	07a00613          	li	a2,122
    1c30:	08c70263          	beq	a4,a2,1cb4 <print+0x1f6>
    1c34:	06c00613          	li	a2,108
    1c38:	4581                	li	a1,0
    1c3a:	00c71a63          	bne	a4,a2,1c4e <print+0x190>
    1c3e:	00128603          	lb	a2,1(t0)
    1c42:	458d                	li	a1,3
    1c44:	00e61463          	bne	a2,a4,1c4c <print+0x18e>
    1c48:	0285                	addi	t0,t0,1
    1c4a:	4591                	li	a1,4
    1c4c:	0285                	addi	t0,t0,1
    1c4e:	00028603          	lb	a2,0(t0)
    1c52:	06000393          	li	t2,96
    1c56:	06100713          	li	a4,97
    1c5a:	00c3c463          	blt	t2,a2,1c62 <print+0x1a4>
    1c5e:	04100713          	li	a4,65
    1c62:	06700393          	li	t2,103
    1c66:	06c3c463          	blt	t2,a2,1cce <print+0x210>
    1c6a:	06500393          	li	t2,101
    1c6e:	18765663          	bge	a2,t2,1dfa <print+0x33c>
    1c72:	04700393          	li	t2,71
    1c76:	04c3c163          	blt	t2,a2,1cb8 <print+0x1fa>
    1c7a:	04500593          	li	a1,69
    1c7e:	16b65e63          	bge	a2,a1,1dfa <print+0x33c>
    1c82:	04300713          	li	a4,67
    1c86:	eae610e3          	bne	a2,a4,1b26 <print+0x68>
    1c8a:	4018                	lw	a4,0(s0)
    1c8c:	00440393          	addi	t2,s0,4
    1c90:	ca16                	sw	t0,20(sp)
    1c92:	00e10c23          	sb	a4,24(sp)
    1c96:	c81e                	sw	t2,16(sp)
    1c98:	00010ca3          	sb	zero,25(sp)
    1c9c:	4701                	li	a4,0
    1c9e:	86be                	mv	a3,a5
    1ca0:	862a                	mv	a2,a0
    1ca2:	082c                	addi	a1,sp,24
    1ca4:	a849                	j	1d36 <print+0x278>
    1ca6:	00128603          	lb	a2,1(t0)
    1caa:	4581                	li	a1,0
    1cac:	fae611e3          	bne	a2,a4,1c4e <print+0x190>
    1cb0:	0289                	addi	t0,t0,2
    1cb2:	bf71                	j	1c4e <print+0x190>
    1cb4:	0285                	addi	t0,t0,1
    1cb6:	bfb1                	j	1c12 <print+0x154>
    1cb8:	06300693          	li	a3,99
    1cbc:	fcd607e3          	beq	a2,a3,1c8a <print+0x1cc>
    1cc0:	06c6cf63          	blt	a3,a2,1d3e <print+0x280>
    1cc4:	05800693          	li	a3,88
    1cc8:	02d60363          	beq	a2,a3,1cee <print+0x230>
    1ccc:	bda9                	j	1b26 <print+0x68>
    1cce:	07300693          	li	a3,115
    1cd2:	04d60463          	beq	a2,a3,1d1a <print+0x25c>
    1cd6:	02c6cb63          	blt	a3,a2,1d0c <print+0x24e>
    1cda:	06f00693          	li	a3,111
    1cde:	0ed60563          	beq	a2,a3,1dc8 <print+0x30a>
    1ce2:	07000693          	li	a3,112
    1ce6:	0047e793          	ori	a5,a5,4
    1cea:	e2d61ee3          	bne	a2,a3,1b26 <print+0x68>
    1cee:	4691                	li	a3,4
    1cf0:	0cd59263          	bne	a1,a3,1db4 <print+0x2f6>
    1cf4:	00840393          	addi	t2,s0,8
    1cf8:	400c                	lw	a1,0(s0)
    1cfa:	4050                	lw	a2,4(s0)
    1cfc:	ca16                	sw	t0,20(sp)
    1cfe:	c23a                	sw	a4,4(sp)
    1d00:	c03e                	sw	a5,0(sp)
    1d02:	c81e                	sw	t2,16(sp)
    1d04:	87aa                	mv	a5,a0
    1d06:	4701                	li	a4,0
    1d08:	46c1                	li	a3,16
    1d0a:	a881                	j	1d5a <print+0x29c>
    1d0c:	07500693          	li	a3,117
    1d10:	06d60b63          	beq	a2,a3,1d86 <print+0x2c8>
    1d14:	07800693          	li	a3,120
    1d18:	bf45                	j	1cc8 <print+0x20a>
    1d1a:	4018                	lw	a4,0(s0)
    1d1c:	000026b7          	lui	a3,0x2
    1d20:	00440393          	addi	t2,s0,4
    1d24:	6c068593          	addi	a1,a3,1728 # 26c0 <font+0x500>
    1d28:	c311                	beqz	a4,1d2c <print+0x26e>
    1d2a:	85ba                	mv	a1,a4
    1d2c:	4732                	lw	a4,12(sp)
    1d2e:	ca16                	sw	t0,20(sp)
    1d30:	c81e                	sw	t2,16(sp)
    1d32:	86be                	mv	a3,a5
    1d34:	862a                	mv	a2,a0
    1d36:	4522                	lw	a0,8(sp)
    1d38:	b81ff0ef          	jal	ra,18b8 <prints>
    1d3c:	a015                	j	1d60 <print+0x2a2>
    1d3e:	4691                	li	a3,4
    1d40:	02d59563          	bne	a1,a3,1d6a <print+0x2ac>
    1d44:	00840393          	addi	t2,s0,8
    1d48:	400c                	lw	a1,0(s0)
    1d4a:	4050                	lw	a2,4(s0)
    1d4c:	ca16                	sw	t0,20(sp)
    1d4e:	c23a                	sw	a4,4(sp)
    1d50:	c03e                	sw	a5,0(sp)
    1d52:	c81e                	sw	t2,16(sp)
    1d54:	87aa                	mv	a5,a0
    1d56:	4705                	li	a4,1
    1d58:	46a9                	li	a3,10
    1d5a:	4522                	lw	a0,8(sp)
    1d5c:	d5bff0ef          	jal	ra,1ab6 <printLongLongInt>
    1d60:	43c2                	lw	t2,16(sp)
    1d62:	94aa                	add	s1,s1,a0
    1d64:	841e                	mv	s0,t2
    1d66:	42d2                	lw	t0,20(sp)
    1d68:	bb7d                	j	1b26 <print+0x68>
    1d6a:	46b2                	lw	a3,12(sp)
    1d6c:	400c                	lw	a1,0(s0)
    1d6e:	c816                	sw	t0,16(sp)
    1d70:	c236                	sw	a3,4(sp)
    1d72:	c03a                	sw	a4,0(sp)
    1d74:	0411                	addi	s0,s0,4
    1d76:	872a                	mv	a4,a0
    1d78:	4685                	li	a3,1
    1d7a:	4629                	li	a2,10
    1d7c:	4522                	lw	a0,8(sp)
    1d7e:	c21ff0ef          	jal	ra,199e <printInt>
    1d82:	94aa                	add	s1,s1,a0
    1d84:	b341                	j	1b04 <print+0x46>
    1d86:	4691                	li	a3,4
    1d88:	00d59d63          	bne	a1,a3,1da2 <print+0x2e4>
    1d8c:	00840393          	addi	t2,s0,8
    1d90:	400c                	lw	a1,0(s0)
    1d92:	4050                	lw	a2,4(s0)
    1d94:	ca16                	sw	t0,20(sp)
    1d96:	c23a                	sw	a4,4(sp)
    1d98:	c03e                	sw	a5,0(sp)
    1d9a:	c81e                	sw	t2,16(sp)
    1d9c:	87aa                	mv	a5,a0
    1d9e:	4701                	li	a4,0
    1da0:	bf65                	j	1d58 <print+0x29a>
    1da2:	46b2                	lw	a3,12(sp)
    1da4:	400c                	lw	a1,0(s0)
    1da6:	c816                	sw	t0,16(sp)
    1da8:	c236                	sw	a3,4(sp)
    1daa:	c03a                	sw	a4,0(sp)
    1dac:	0411                	addi	s0,s0,4
    1dae:	872a                	mv	a4,a0
    1db0:	4681                	li	a3,0
    1db2:	b7e1                	j	1d7a <print+0x2bc>
    1db4:	46b2                	lw	a3,12(sp)
    1db6:	c816                	sw	t0,16(sp)
    1db8:	400c                	lw	a1,0(s0)
    1dba:	4641                	li	a2,16
    1dbc:	c236                	sw	a3,4(sp)
    1dbe:	c03a                	sw	a4,0(sp)
    1dc0:	0411                	addi	s0,s0,4
    1dc2:	872a                	mv	a4,a0
    1dc4:	4681                	li	a3,0
    1dc6:	bf5d                	j	1d7c <print+0x2be>
    1dc8:	4691                	li	a3,4
    1dca:	00d59e63          	bne	a1,a3,1de6 <print+0x328>
    1dce:	00840393          	addi	t2,s0,8
    1dd2:	400c                	lw	a1,0(s0)
    1dd4:	4050                	lw	a2,4(s0)
    1dd6:	ca16                	sw	t0,20(sp)
    1dd8:	c23a                	sw	a4,4(sp)
    1dda:	c03e                	sw	a5,0(sp)
    1ddc:	c81e                	sw	t2,16(sp)
    1dde:	87aa                	mv	a5,a0
    1de0:	4701                	li	a4,0
    1de2:	46a1                	li	a3,8
    1de4:	bf9d                	j	1d5a <print+0x29c>
    1de6:	46b2                	lw	a3,12(sp)
    1de8:	400c                	lw	a1,0(s0)
    1dea:	c816                	sw	t0,16(sp)
    1dec:	c236                	sw	a3,4(sp)
    1dee:	c03a                	sw	a4,0(sp)
    1df0:	0411                	addi	s0,s0,4
    1df2:	872a                	mv	a4,a0
    1df4:	4681                	li	a3,0
    1df6:	4621                	li	a2,8
    1df8:	b751                	j	1d7c <print+0x2be>
    1dfa:	400c                	lw	a1,0(s0)
    1dfc:	00840613          	addi	a2,s0,8
    1e00:	4040                	lw	s0,4(s0)
    1e02:	c23a                	sw	a4,4(sp)
    1e04:	872a                	mv	a4,a0
    1e06:	4522                	lw	a0,8(sp)
    1e08:	c832                	sw	a2,16(sp)
    1e0a:	c03e                	sw	a5,0(sp)
    1e0c:	8622                	mv	a2,s0
    1e0e:	87b6                	mv	a5,a3
    1e10:	46a9                	li	a3,10
    1e12:	ca16                	sw	t0,20(sp)
    1e14:	ca7ff0ef          	jal	ra,1aba <printDouble>
    1e18:	94aa                	add	s1,s1,a0
    1e1a:	4442                	lw	s0,16(sp)
    1e1c:	b7a9                	j	1d66 <print+0x2a8>

00001e1e <printf>:
    1e1e:	fdc10113          	addi	sp,sp,-36
    1e22:	c82e                	sw	a1,16(sp)
    1e24:	ca32                	sw	a2,20(sp)
    1e26:	85aa                	mv	a1,a0
    1e28:	0810                	addi	a2,sp,16
    1e2a:	4501                	li	a0,0
    1e2c:	c606                	sw	ra,12(sp)
    1e2e:	cc36                	sw	a3,24(sp)
    1e30:	ce3a                	sw	a4,28(sp)
    1e32:	d03e                	sw	a5,32(sp)
    1e34:	c032                	sw	a2,0(sp)
    1e36:	c89ff0ef          	jal	ra,1abe <print>
    1e3a:	40b2                	lw	ra,12(sp)
    1e3c:	02410113          	addi	sp,sp,36
    1e40:	8082                	ret

00001e42 <snprintf>:
    1e42:	fd810113          	addi	sp,sp,-40
    1e46:	8332                	mv	t1,a2
    1e48:	d23e                	sw	a5,36(sp)
    1e4a:	c42e                	sw	a1,8(sp)
    1e4c:	c62a                	sw	a0,12(sp)
    1e4e:	0870                	addi	a2,sp,28
    1e50:	4785                	li	a5,1
    1e52:	0048                	addi	a0,sp,4
    1e54:	859a                	mv	a1,t1
    1e56:	cc06                	sw	ra,24(sp)
    1e58:	ce36                	sw	a3,28(sp)
    1e5a:	d03a                	sw	a4,32(sp)
    1e5c:	c23e                	sw	a5,4(sp)
    1e5e:	c032                	sw	a2,0(sp)
    1e60:	c5fff0ef          	jal	ra,1abe <print>
    1e64:	40e2                	lw	ra,24(sp)
    1e66:	02810113          	addi	sp,sp,40
    1e6a:	8082                	ret

00001e6c <puts>:
    1e6c:	1141                	addi	sp,sp,-16
    1e6e:	c422                	sw	s0,8(sp)
    1e70:	c226                	sw	s1,4(sp)
    1e72:	c606                	sw	ra,12(sp)
    1e74:	211c                	lbu	a5,0(a0)
    1e76:	84aa                	mv	s1,a0
    1e78:	4401                	li	s0,0
    1e7a:	81dc                	sb	a5,3(sp)
    1e7c:	00310783          	lb	a5,3(sp)
    1e80:	0405                	addi	s0,s0,1
    1e82:	ef99                	bnez	a5,1ea0 <puts+0x34>
    1e84:	47a9                	li	a5,10
    1e86:	00310593          	addi	a1,sp,3
    1e8a:	4605                	li	a2,1
    1e8c:	4501                	li	a0,0
    1e8e:	81dc                	sb	a5,3(sp)
    1e90:	995ff0ef          	jal	ra,1824 <_write>
    1e94:	8522                	mv	a0,s0
    1e96:	40b2                	lw	ra,12(sp)
    1e98:	4422                	lw	s0,8(sp)
    1e9a:	4492                	lw	s1,4(sp)
    1e9c:	0141                	addi	sp,sp,16
    1e9e:	8082                	ret
    1ea0:	4605                	li	a2,1
    1ea2:	00310593          	addi	a1,sp,3
    1ea6:	4501                	li	a0,0
    1ea8:	97dff0ef          	jal	ra,1824 <_write>
    1eac:	008487b3          	add	a5,s1,s0
    1eb0:	239c                	lbu	a5,0(a5)
    1eb2:	81dc                	sb	a5,3(sp)
    1eb4:	b7e1                	j	1e7c <puts+0x10>

00001eb6 <memcpy>:
    1eb6:	00a5c7b3          	xor	a5,a1,a0
    1eba:	8b8d                	andi	a5,a5,3
    1ebc:	00c50733          	add	a4,a0,a2
    1ec0:	e781                	bnez	a5,1ec8 <memcpy+0x12>
    1ec2:	478d                	li	a5,3
    1ec4:	02c7e763          	bltu	a5,a2,1ef2 <memcpy+0x3c>
    1ec8:	87aa                	mv	a5,a0
    1eca:	0ae57e63          	bgeu	a0,a4,1f86 <memcpy+0xd0>
    1ece:	2194                	lbu	a3,0(a1)
    1ed0:	0785                	addi	a5,a5,1
    1ed2:	0585                	addi	a1,a1,1
    1ed4:	fed78fa3          	sb	a3,-1(a5)
    1ed8:	fee7ebe3          	bltu	a5,a4,1ece <memcpy+0x18>
    1edc:	8082                	ret
    1ede:	2194                	lbu	a3,0(a1)
    1ee0:	0785                	addi	a5,a5,1
    1ee2:	0585                	addi	a1,a1,1
    1ee4:	fed78fa3          	sb	a3,-1(a5)
    1ee8:	fee7ebe3          	bltu	a5,a4,1ede <memcpy+0x28>
    1eec:	4402                	lw	s0,0(sp)
    1eee:	0111                	addi	sp,sp,4
    1ef0:	8082                	ret
    1ef2:	00357693          	andi	a3,a0,3
    1ef6:	87aa                	mv	a5,a0
    1ef8:	ca89                	beqz	a3,1f0a <memcpy+0x54>
    1efa:	2194                	lbu	a3,0(a1)
    1efc:	0785                	addi	a5,a5,1
    1efe:	0585                	addi	a1,a1,1
    1f00:	fed78fa3          	sb	a3,-1(a5)
    1f04:	0037f693          	andi	a3,a5,3
    1f08:	bfc5                	j	1ef8 <memcpy+0x42>
    1f0a:	ffc77693          	andi	a3,a4,-4
    1f0e:	fe068613          	addi	a2,a3,-32
    1f12:	06c7f563          	bgeu	a5,a2,1f7c <memcpy+0xc6>
    1f16:	1171                	addi	sp,sp,-4
    1f18:	c022                	sw	s0,0(sp)
    1f1a:	49c0                	lw	s0,20(a1)
    1f1c:	0005a303          	lw	t1,0(a1)
    1f20:	0085a383          	lw	t2,8(a1)
    1f24:	cbc0                	sw	s0,20(a5)
    1f26:	4d80                	lw	s0,24(a1)
    1f28:	0067a023          	sw	t1,0(a5)
    1f2c:	0045a303          	lw	t1,4(a1)
    1f30:	cf80                	sw	s0,24(a5)
    1f32:	4dc0                	lw	s0,28(a1)
    1f34:	0067a223          	sw	t1,4(a5)
    1f38:	00c5a283          	lw	t0,12(a1)
    1f3c:	0105a303          	lw	t1,16(a1)
    1f40:	02458593          	addi	a1,a1,36
    1f44:	cfc0                	sw	s0,28(a5)
    1f46:	ffc5a403          	lw	s0,-4(a1)
    1f4a:	0077a423          	sw	t2,8(a5)
    1f4e:	0057a623          	sw	t0,12(a5)
    1f52:	0067a823          	sw	t1,16(a5)
    1f56:	02478793          	addi	a5,a5,36
    1f5a:	fe87ae23          	sw	s0,-4(a5)
    1f5e:	fac7eee3          	bltu	a5,a2,1f1a <memcpy+0x64>
    1f62:	f8d7f3e3          	bgeu	a5,a3,1ee8 <memcpy+0x32>
    1f66:	4190                	lw	a2,0(a1)
    1f68:	0791                	addi	a5,a5,4
    1f6a:	0591                	addi	a1,a1,4
    1f6c:	fec7ae23          	sw	a2,-4(a5)
    1f70:	bfcd                	j	1f62 <memcpy+0xac>
    1f72:	4190                	lw	a2,0(a1)
    1f74:	0791                	addi	a5,a5,4
    1f76:	0591                	addi	a1,a1,4
    1f78:	fec7ae23          	sw	a2,-4(a5)
    1f7c:	fed7ebe3          	bltu	a5,a3,1f72 <memcpy+0xbc>
    1f80:	f4e7e7e3          	bltu	a5,a4,1ece <memcpy+0x18>
    1f84:	8082                	ret
    1f86:	8082                	ret
    1f88:	1609                	addi	a2,a2,-30
    1f8a:	2009                	jal	1f8c <memcpy+0xd6>
    1f8c:	1b21                	addi	s6,s6,-24
    1f8e:	15171913          	0x15171913
    1f92:	2b1e                	lhu	a5,16(a4)
    1f94:	0504                	addi	s1,sp,640
    1f96:	0e02                	c.slli64	t3
    1f98:	1e08140b          	0x1e08140b
    1f9c:	1d22                	slli	s10,s10,0x28
    1f9e:	1e18                	addi	a4,sp,816
    1fa0:	2b241a1b          	0x2b241a1b
    1fa4:	0606                	slli	a2,a2,0x1
    1fa6:	0f02                	c.slli64	t5
    1fa8:	00004843          	fmadd.s	fa6,ft0,ft0,ft0,rmm
    1fac:	003a                	c.slli	zero,0xe
    1fae:	0000                	unimp
    1fb0:	4441                	li	s0,16
    1fb2:	6f4d2043          	fmadd.q	ft0,fs10,fs4,fa3,rdn
    1fb6:	696e                	flw	fs2,216(sp)
    1fb8:	6f74                	flw	fa3,92(a4)
    1fba:	0072                	c.slli	zero,0x1c
    1fbc:	64254843          	0x64254843
    1fc0:	0000                	unimp
    1fc2:	0000                	unimp
    1fc4:	2e2d                	jal	22fe <font+0x13e>
    1fc6:	2d2d                	jal	2600 <font+0x440>
    1fc8:	0056                	c.slli	zero,0x15
    1fca:	0000                	unimp
    1fcc:	32334843          	fmadd.d	fa6,ft6,ft3,ft6,rmm
    1fd0:	0056                	c.slli	zero,0x15
    1fd2:	0000                	unimp
    1fd4:	4f4c                	lw	a1,28(a4)
    1fd6:	00004f47          	fmsub.s	ft10,ft0,ft0,ft0,rmm
    1fda:	0000                	unimp
    1fdc:	6425                	lui	s0,0x9
    1fde:	252e                	lhu	a1,10(a0)
    1fe0:	3330                	lbu	a2,3(a4)
    1fe2:	5664                	lw	s1,108(a2)
    1fe4:	0000                	unimp
    1fe6:	0000                	unimp
    1fe8:	6425                	lui	s0,0x9
    1fea:	252e                	lhu	a1,10(a0)
    1fec:	3230                	lbu	a2,3(a2)
    1fee:	5664                	lw	s1,108(a2)
    1ff0:	0000                	unimp
	...

00001ff4 <CSWTCH.3>:
    1ff4:	07ff 07e0 ffe0 f81f 6944 7073 616c 2079     ........Display 
    2004:	664f 0066 4843 3233 3056 3330 4120 4344     Off.CH32V003 ADC
    2014:	0000 0000 6f4d 696e 6f74 2072 3176 302e     ....Monitor v1.0
    2024:	0000 0000 6f54 6375 3a68 5420 7275 206e     ....Touch: Turn 
    2034:	6e4f 0000 6f48 646c 203a 6f54 6767 656c     On..Hold: Toggle
    2044:	0000 0000 6568 6c6c 006f 0000 4843 3233     ....hello...CH32
    2054:	3056 3330 0000 0000 4441 2043 6e69 7469     V003....ADC init
    2064:	6169 696c 657a 0d64 0000 0000 5750 204d     ialized.....PWM 
    2074:	6e69 7469 6169 696c 657a 0d64 0000 0000     initialized.....
    2084:	6f54 6375 2068 7562 7474 6e6f 6920 696e     Touch button ini
    2094:	6974 6c61 7a69 6465 000d 0000 6944 7073     tialized....Disp
    20a4:	616c 2079 6f63 746e 6f72 206c 6e69 7469     lay control init
    20b4:	6169 696c 657a 0d64 0000 0000 4441 2043     ialized.....ADC 
    20c4:	6964 7073 616c 2079 6e69 7469 6169 696c     display initiali
    20d4:	657a 0d64 0000 0000 7953 7473 6d65 6920     zed.....System i
    20e4:	696e 6974 6c61 7a69 7461 6f69 206e 6f63     nitialization co
    20f4:	706d 656c 6574 000d 0a0d 3d3d 203d 4843     mplete....=== CH
    2104:	3233 3056 3330 4120 4344 4d20 6e6f 7469     32V003 ADC Monit
    2114:	726f 3d20 3d3d 000d 7953 7473 6d65 6c43     or ===..SystemCl
    2124:	3a6b 2520 2064 7a48 0a0d 0000 6843 7069     k: %d Hz....Chip
    2134:	4449 203a 3025 7838 0a0d 0000 6f54 6375     ID: %08x....Touc
    2144:	3a68 5320 6f68 7472 7020 6572 7373 2d20     h: Short press -
    2154:	7420 7275 696e 676e 6f20 206e 6964 7073      turning on disp
    2164:	616c 0d79 0000 0000 6f54 6375 3a68 4c20     lay.....Touch: L
    2174:	6e6f 2067 7270 7365 2073 202d 6f74 6767     ong press - togg
    2184:	696c 676e 6420 7369 6c70 7961 6d20 646f     ling display mod
    2194:	0d65 0000 6f54 6375 3a68 5420 6d69 6f65     e...Touch: Timeo
    21a4:	7475 2d20 7420 7275 696e 676e 6f20 6666     ut - turning off
    21b4:	6420 7369 6c70 7961 000d 0000                display....

000021c0 <font>:
    21c0:	0000 0000 3e00 4f5b 3e5b 6b3e 6b4f 1c3e     .....>[O[>>kOk>.
    21d0:	7c3e 1c3e 3c18 3c7e 1c18 7d57 1c57 5e1c     >|>..<~<..W}W..^
    21e0:	5e7f 001c 3c18 0018 e7ff e7c3 00ff 2418     .^...<.........$
    21f0:	0018 e7ff e7db 30ff 3a48 0e06 2926 2979     .......0H:..&)y)
    2200:	4026 057f 0705 7f40 2505 5a3f e73c 5a3c     &@....@..%?Z<.<Z
    2210:	3e7f 1c1c 0808 1c1c 7f3e 2214 227f 5f14     .>......>.."."._
    2220:	005f 5f5f 0906 017f 007f 8966 6a95 6060     _.__......f..j``
    2230:	6060 9460 ffa2 94a2 0408 047e 1008 7e20     ```.......~... ~
    2240:	1020 0808 1c2a 0808 2a1c 0808 101e 1010      ...*....*......
    2250:	0c10 0c1e 0c1e 3830 383e 0630 3e0e 060e     ......08>80..>..
    2260:	0000 0000 0000 5f00 0000 0700 0700 1400     ......._........
    2270:	147f 147f 2a24 2a7f 2312 0813 6264 4936     ....$*.*.#..db6I
    2280:	2056 0050 0708 0003 1c00 4122 0000 2241     V P......."A..A"
    2290:	001c 1c2a 1c7f 082a 3e08 0808 8000 3070     ..*...*..>....p0
    22a0:	0800 0808 0808 0000 6060 2000 0810 0204     ........``. ....
    22b0:	513e 4549 003e 7f42 0040 4972 4949 2146     >QIE>.B.@.rIIIF!
    22c0:	4941 334d 1418 7f12 2710 4545 3945 4a3c     AIM3.....'EEE9<J
    22d0:	4949 4131 1121 0709 4936 4949 4636 4949     II1A!...6III6FII
    22e0:	1e29 0000 0014 0000 3440 0000 0800 2214     ).......@4....."
    22f0:	1441 1414 1414 4100 1422 0208 5901 0609     A......A"....Y..
    2300:	413e 595d 7c4e 1112 7c12 497f 4949 3e36     >A]YN|...|.III6>
    2310:	4141 2241 417f 4141 7f3e 4949 4149 097f     AAA".AAA>.IIIA..
    2320:	0909 3e01 4141 7351 087f 0808 007f 7f41     ...>AAQs......A.
    2330:	0041 4020 3f41 7f01 1408 4122 407f 4040     A. @A?...."A.@@@
    2340:	7f40 1c02 7f02 047f 1008 3e7f 4141 3e41     @..........>AAA>
    2350:	097f 0909 3e06 5141 5e21 097f 2919 2646     .....>AQ!^...)F&
    2360:	4949 3249 0103 017f 3f03 4040 3f40 201f     III2.....?@@@?. 
    2370:	2040 3f1f 3840 3f40 1463 1408 0363 7804     @ .?@8@?c...c..x
    2380:	0304 5961 4d49 0043 417f 4141 0402 1008     ..aYIMC..AAA....
    2390:	0020 4141 7f41 0204 0201 4004 4040 4040      .AAA......@@@@@
    23a0:	0300 0807 2000 5454 4078 287f 4444 3838     ..... TTx@.(DD88
    23b0:	4444 2844 4438 2844 387f 5454 1854 0800     DDD(8DD(.8TTT...
    23c0:	097e 1802 a4a4 789c 087f 0404 0078 7d44     ~......x....x.D}
    23d0:	0040 4020 3d40 7f00 2810 0044 4100 407f     @. @@=...(D..A.@
    23e0:	7c00 7804 7804 087c 0404 3878 4444 3844     .|.x.x|...x8DDD8
    23f0:	18fc 2424 1818 2424 fc18 087c 0404 4808     ..$$..$$..|....H
    2400:	5454 2454 0404 443f 3c24 4040 7c20 201c     TTT$..?D$<@@ |. 
    2410:	2040 3c1c 3040 3c40 2844 2810 4c44 9090     @ .<@0@<D(.(DL..
    2420:	7c90 6444 4c54 0044 3608 0041 0000 0077     .|DdTLD..6A...w.
    2430:	0000 3641 0008 0102 0402 3c02 2326 3c26     ..A6.......<&#&<
    2440:	a11e 61a1 3a12 4040 7a20 5438 5554 2159     ...a.:@@ z8TTUY!
    2450:	5555 4179 5422 7854 2142 5455 4078 5420     UUyA"TTxB!UTx@ T
    2460:	7955 0c40 521e 1272 5539 5555 3959 5454     <EMAIL>.9UUUY9TT
    2470:	5954 5539 5454 0058 4500 417c 0200 7d45     TY9UTTX..E|A..E}
    2480:	0042 4501 407c 127d 1211 f07d 2528 f028     B..E|@}...}.(%(.
    2490:	547c 4555 2000 5454 547c 0a7c 7f09 3249     |TUE. TT|T|...I2
    24a0:	4949 3249 443a 4444 323a 484a 3048 413a     III2:DDD:2JHH0:A
    24b0:	2141 3a7a 4042 7820 9d00 a0a0 3d7d 4242     A!z:B@ x....}=BB
    24c0:	3d42 403d 4040 3c3d ff24 2424 7e48 4349     B==@@@=<$.$$H~IC
    24d0:	2b66 fc2f 2b2f 09ff f629 c020 7e88 0309     f+/./+..). ..~..
    24e0:	5420 7954 0041 4400 417d 4830 4a48 3832      TTyA..D}A0HHJ28
    24f0:	4040 7a22 7a00 0a0a 7d72 190d 7d31 2926     @@"z.z..r}..1}&)
    2500:	2f29 2628 2929 2629 4830 404d 3820 0808     )/(&)))&0HM@ 8..
    2510:	0808 0808 0808 2f38 c810 baac 102f 3428     ......8/..../.(4
    2520:	00fa 7b00 0000 1408 142a 2222 2a14 0814     ...{....*."".*..
    2530:	0055 0055 aa55 aa55 aa55 55ff 55ff 00ff     U.U.U.U.U..U.U..
    2540:	0000 00ff 1010 ff10 1400 1414 00ff 1010     ................
    2550:	00ff 10ff f010 f010 1414 fc14 1400 f714     ................
    2560:	ff00 0000 00ff 14ff f414 fc04 1414 1017     ................
    2570:	101f 1f10 1f10 1414 1f14 1000 1010 00f0     ................
    2580:	0000 1f00 1010 1010 101f 1010 f010 0010     ................
    2590:	0000 10ff 1010 1010 1010 1010 10ff 0000     ................
    25a0:	ff00 0014 ff00 ff00 0000 101f 0017 fc00     ................
    25b0:	f404 1414 1017 1417 f414 f404 0000 00ff     ................
    25c0:	14f7 1414 1414 1414 00f7 14f7 1414 1417     ................
    25d0:	1010 101f 141f 1414 14f4 1010 10f0 00f0     ................
    25e0:	1f00 1f10 0000 1f00 0014 0000 14fc 0000     ................
    25f0:	10f0 10f0 ff10 ff10 1414 ff14 1014 1010     ................
    2600:	001f 0000 f000 ff10 ffff ffff f0f0 f0f0     ................
    2610:	fff0 ffff 0000 0000 ff00 0fff 0f0f 0f0f     ................
    2620:	4438 3844 fc44 4a4a 344a 027e 0602 0206     8DD8D.JJJ4~.....
    2630:	027e 027e 5563 4149 3863 4444 043c 7e40     ~.~.cUIAc8DD<.@~
    2640:	1e20 0620 7e02 0202 a599 a5e7 1c99 492a      . ..~........*I
    2650:	1c2a 724c 7201 304c 4d4a 304d 4830 4878     *.Lr.rL0JMM00HxH
    2660:	bc30 5a62 3d46 493e 4949 7e00 0101 7e01     0.bZF=>III.~...~
    2670:	2a2a 2a2a 442a 5f44 4444 5140 444a 4040     *****DD_DD@QJD@@
    2680:	4a44 4051 0000 01ff e003 ff80 0000 0808     DJQ@............
    2690:	6b6b 3608 3612 3624 0600 0909 0006 1800     kk.6.6$6........
    26a0:	0018 0000 1010 3000 ff40 0101 1f00 0101     .......0@.......
    26b0:	001e 1d19 1217 3c00 3c3c 003c 0000 0000     .......<<<<.....
    26c0:	6e28 6c75 296c 0000                         (null)..
