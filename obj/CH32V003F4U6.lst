
CH32V003F4U6.elf:     file format elf32-littleriscv
CH32V003F4U6.elf
architecture: riscv:rv32, flags 0x00000112:
EXEC_P, HAS_SYMS, D_PAGED
start address 0x00000000

Program Header:
    LOAD off    0x00001000 vaddr 0x00000000 paddr 0x00000000 align 2**12
         filesz 0x00002850 memsz 0x00002850 flags r-x
    LOAD off    0x00004000 vaddr 0x20000000 paddr 0x00002850 align 2**12
         filesz 0x00000040 memsz 0x000001d4 flags rw-
    LOAD off    0x00004700 vaddr 0x20000700 paddr 0x20000700 align 2**12
         filesz 0x00000000 memsz 0x00000100 flags rw-

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .init         000000a0  00000000  00000000  00001000  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  1 .highcodelalign 00000000  000000a0  000000a0  00004040  2**0
                  CONTENTS
  2 .highcode     00000000  20000000  20000000  00004040  2**0
                  CONTENTS
  3 .text         000027b0  000000a0  000000a0  000010a0  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  4 .fini         00000000  00002850  00002850  00004040  2**0
                  CONTENTS, ALLOC, LOAD, CODE
  5 .dalign       00000000  20000000  20000000  00004040  2**0
                  CONTENTS
  6 .dlalign      00000000  00002850  00002850  00004040  2**0
                  CONTENTS
  7 .data         00000040  20000000  00002850  00004000  2**2
                  CONTENTS, ALLOC, LOAD, DATA
  8 .bss          00000194  20000040  00002890  00004040  2**2
                  ALLOC
  9 .stack        00000100  20000700  20000700  00004700  2**0
                  ALLOC
 10 .debug_info   00012954  00000000  00000000  00004040  2**0
                  CONTENTS, READONLY, DEBUGGING
 11 .debug_abbrev 000034b0  00000000  00000000  00016994  2**0
                  CONTENTS, READONLY, DEBUGGING
 12 .debug_loc    00004b3a  00000000  00000000  00019e44  2**0
                  CONTENTS, READONLY, DEBUGGING
 13 .debug_aranges 00000a00  00000000  00000000  0001e980  2**3
                  CONTENTS, READONLY, DEBUGGING
 14 .debug_ranges 00000bf0  00000000  00000000  0001f380  2**3
                  CONTENTS, READONLY, DEBUGGING
 15 .debug_line   0000c1d3  00000000  00000000  0001ff70  2**0
                  CONTENTS, READONLY, DEBUGGING
 16 .debug_str    00003092  00000000  00000000  0002c143  2**0
                  CONTENTS, READONLY, DEBUGGING
 17 .comment      00000033  00000000  00000000  0002f1d5  2**0
                  CONTENTS, READONLY
 18 .debug_frame  0000176c  00000000  00000000  0002f208  2**2
                  CONTENTS, READONLY, DEBUGGING
SYMBOL TABLE:
00000000 l    d  .init	00000000 .init
000000a0 l    d  .highcodelalign	00000000 .highcodelalign
20000000 l    d  .highcode	00000000 .highcode
000000a0 l    d  .text	00000000 .text
00002850 l    d  .fini	00000000 .fini
20000000 l    d  .dalign	00000000 .dalign
00002850 l    d  .dlalign	00000000 .dlalign
20000000 l    d  .data	00000000 .data
20000040 l    d  .bss	00000000 .bss
20000700 l    d  .stack	00000000 .stack
00000000 l    d  .debug_info	00000000 .debug_info
00000000 l    d  .debug_abbrev	00000000 .debug_abbrev
00000000 l    d  .debug_loc	00000000 .debug_loc
00000000 l    d  .debug_aranges	00000000 .debug_aranges
00000000 l    d  .debug_ranges	00000000 .debug_ranges
00000000 l    d  .debug_line	00000000 .debug_line
00000000 l    d  .debug_str	00000000 .debug_str
00000000 l    d  .comment	00000000 .comment
00000000 l    d  .debug_frame	00000000 .debug_frame
00000000 l    df *ABS*	00000000 adc_display.c
0000014a l     F .text	00000048 ADC_Display_Format_Voltage.part.0
00002188 l     O .text	00000008 CSWTCH.3
00000000 l    df *ABS*	00000000 ch32v00x_it.c
00000000 l    df *ABS*	00000000 display_control.c
00000000 l    df *ABS*	00000000 display_text.c
00000000 l    df *ABS*	00000000 main.c
00000000 l    df *ABS*	00000000 pwm_config.c
00000000 l    df *ABS*	00000000 st7735.c
00000b26 l     F .text	0000003e SPI_send_DMA
00000b64 l     F .text	00000012 SPI_send
00000b76 l     F .text	00000016 write_command_8
00000b8c l     F .text	00000020 write_data_16
00000bac l     F .text	0000003c tft_set_window
20000062 l     O .bss	00000002 _bg_color
20000064 l     O .bss	00000140 _buffer
200001a4 l     O .bss	00000002 _cursor_x
200001a6 l     O .bss	00000002 _cursor_y
200001a8 l     O .bss	0000000c str.4169
20000000 l     O .data	00000002 _color
00002348 l     O .text	00000500 font
00000000 l    df *ABS*	00000000 system_ch32v00x.c
00000000 l    df *ABS*	00000000 touch_button.c
00000000 l    df *ABS*	00000000 ch32v00x_dbgmcu.c
00000000 l    df *ABS*	00000000 ch32v00x_exti.c
00000000 l    df *ABS*	00000000 ch32v00x_gpio.c
00000000 l    df *ABS*	00000000 ch32v00x_misc.c
00000000 l    df *ABS*	00000000 ch32v00x_rcc.c
20000018 l     O .data	00000014 ADCPrescTable
2000002c l     O .data	00000010 APBAHBPrescTable
00000000 l    df *ABS*	00000000 ch32v00x_tim.c
00000000 l    df *ABS*	00000000 ch32v00x_usart.c
00000000 l    df *ABS*	00000000 debug.c
200001d0 l     O .bss	00000002 p_ms
200001d2 l     O .bss	00000001 p_us
00000000 l    df *ABS*	00000000 wchprintf.c
00000000 l    df *ABS*	00000000 memcpy.c
00001c4c  w    F .text	00000004 printDouble
00000216 g     F .text	00000026 ADC_Display_Init
20000040 g     O .bss	0000000c adc_display_config
00001c50  w    F .text	00000360 print
00001fb0  w    F .text	00000024 printf
20000840 g       .data	00000000 __global_pointer$
00000718 g     F .text	00000026 Display_Control_Init
00001364  w      .text	00000000 TIM1_CC_IRQHandler
000005ca g     F .text	00000010 HardFault_Handler
00001b30  w    F .text	00000118 printInt
000017c4 g     F .text	0000000e TIM_OC1PreloadConfig
00001276 g     F .text	0000002a Touch_Button_Init
00001364  w      .text	00000000 SysTick_Handler
00001560 g     F .text	00000062 NVIC_Init
00001364  w      .text	00000000 PVD_IRQHandler
00001fd4 g     F .text	0000002a snprintf
000005c8 g     F .text	00000002 NMI_Handler
00001416 g     F .text	0000000a DBGMCU_GetCHIPID
200001b4 g     O .bss	00000004 system_tick_ms
00000d78 g     F .text	0000000e tft_set_cursor
000018e6 g     F .text	0000000a USART_GetFlagStatus
20000040 g       .bss	00000000 _sbss
00000100 g       *ABS*	00000000 __stack_size
0000195e g     F .text	00000058 USART_Printf_Init
000007cc g     F .text	0000001a Display_Text_Clear_Screen
000000aa g     F .text	0000000a .hidden __riscv_restore_2
00001782 g     F .text	00000016 TIM_CtrlPWMOutputs
00002048 g     F .text	000000d2 memcpy
00001c48  w    F .text	00000004 printLongLongInt
000001ba g     F .text	0000005c ADC_Display_Draw_Channel_Labels
0000176a g     F .text	00000018 TIM_Cmd
00001ffe g     F .text	0000004a puts
00000192 g     F .text	00000028 ADC_Display_Draw_Header
20000014 g     O .data	00000004 SystemCoreClock
2000004c g     O .bss	0000000c display_control
00000642 g     F .text	00000016 Display_Control_Turn_On
000000d4 g     F .text	0000002c .hidden __udivsi3
000000a0 g       .init	00000000 _einit
000017ee g     F .text	0000000c TIM_ClearITPendingBit
00001676 g     F .text	0000001e RCC_APB2PeriphClockCmd
000014b2 g     F .text	0000007c GPIO_Init
200001cc g     O .bss	00000004 NVIC_Priority_Group
00001364  w      .text	00000000 SPI1_IRQHandler
000018c8 g     F .text	00000016 USART_Cmd
000000a0 g     F .text	0000000a .hidden __riscv_save_1
00000e7a g     F .text	00000020 tft_print
000000aa g     F .text	0000000a .hidden __riscv_restore_0
00001364  w      .text	00000000 AWU_IRQHandler
000002b4 g     F .text	00000086 ADC_Display_Draw_Logo_Channels
000005da g     F .text	00000008 EXTI7_0_IRQHandler
000007e6 g     F .text	0000004e Display_Text_Welcome
00001694 g     F .text	0000001e RCC_APB1PeriphClockCmd
20000700 g       .stack	00000000 _heap_end
00000606 g     F .text	0000003c Display_Control_Update
00001364  w      .text	00000000 DMA1_Channel4_IRQHandler
00001364  w      .text	00000000 ADC1_IRQHandler
000012a0 g     F .text	00000072 Touch_Button_Update
000014a8 g     F .text	0000000a EXTI_ClearITPendingBit
200001d4 g       .bss	00000000 _ebss
00001364  w      .text	00000000 DMA1_Channel7_IRQHandler
00001700 g     F .text	0000006a TIM_OC1Init
000018f0 g     F .text	00000034 Delay_Init
00001234 g     F .text	00000042 Touch_Button_EXTI_Config
000017aa g     F .text	0000001a TIM_ARRPreloadConfig
00000d90 g     F .text	00000006 tft_set_background_color
00000100 g     F .text	00000008 .hidden __umodsi3
000009c8 g     F .text	0000009a PWM_Timer_Config
00001364  w      .text	00000000 I2C1_EV_IRQHandler
0000051a g     F .text	00000080 ADC_Display_Draw_Logo_Header
000017d6 g     F .text	00000018 TIM_GetITStatus
000015d4 g     F .text	000000a2 RCC_GetClocksFreq
000011ae g     F .text	00000030 Touch_Button_GPIO_Config
00001364  w      .text	00000000 DMA1_Channel6_IRQHandler
000017fa g     F .text	000000ce USART_Init
00001364  w      .text	00000000 RCC_IRQHandler
00001364  w      .text	00000000 TIM1_TRG_COM_IRQHandler
00001364  w      .text	00000000 DMA1_Channel1_IRQHandler
00000000 g       .init	00000000 _start
20000004 g     O .data	00000010 AHBPrescTable
00000b12 g     F .text	0000000e PWM_Turn_Off
0000068a g     F .text	00000018 Display_Control_Clear_Screen
00001a4a  w    F .text	000000e6 prints
00001798 g     F .text	00000012 TIM_ITConfig
20000000 g       .highcode	00000000 _highcode_vma_start
00001420 g     F .text	0000006a EXTI_Init
000000b4 g     F .text	00000014 .hidden __mulsi3
00000672 g     F .text	00000018 Display_Control_Toggle
20000000 g       .dalign	00000000 _data_vma
00000a8e g     F .text	00000022 PWM_Config_Init
00001312 g     F .text	0000000c Touch_Button_Get_Event
00000786 g     F .text	00000046 Display_Text_Centered
0000059a g     F .text	0000002e ADC_Display_Draw_Logo_Style
00001538 g     F .text	00000022 GPIO_EXTILineConfig
00000658 g     F .text	0000001a Display_Control_Turn_Off
0000148a g     F .text	0000001e EXTI_GetITStatus
000015c2 g     F .text	00000012 RCC_AdjustHSICalibrationValue
00000896 g     F .text	000000fc main
00000834 g     F .text	00000062 System_Init
0000033a g     F .text	00000070 ADC_Display_Draw_Logo_Main_Voltage
00001364  w      .text	00000000 DMA1_Channel5_IRQHandler
0000073e g     F .text	00000048 Display_Text_Custom
000000cc g     F .text	00000058 .hidden __divsi3
00001924 g     F .text	0000003a Delay_Ms
00000e9a g     F .text	000000aa tft_print_number
00000be8 g     F .text	00000190 tft_init
000000a0 g       .highcodelalign	00000000 _highcode_lma
0000100e g     F .text	00000134 SystemInit
00000b20 g     F .text	00000006 PWM_Get_Brightness
00001a02  w    F .text	00000048 printchar
00000000 g       .init	00000000 _sinit
00000f80 g     F .text	0000008e tft_fill_rect
00001364  w      .text	00000000 DMA1_Channel3_IRQHandler
00000b02 g     F .text	00000010 PWM_Turn_On
00001364  w      .text	00000000 TIM1_UP_IRQHandler
20000058 g     O .bss	00000001 system_initialized
00000f44 g     F .text	0000003c tft_draw_pixel
00001364  w      .text	00000000 WWDG_IRQHandler
00000992 g     F .text	00000036 PWM_GPIO_Config
000005e2 g     F .text	00000024 TIM2_IRQHandler
20000800 g       .stack	00000000 _eusrstack
000000a0 g     F .text	0000000a .hidden __riscv_save_2
00001364  w      .text	00000000 SW_Handler
2000005c g     O .bss	00000006 pwm_control
00001364  w      .text	00000000 TIM1_BRK_IRQHandler
000018de g     F .text	00000008 USART_SendData
000019b6 g     F .text	0000004c _write
20000040 g       .data	00000000 _edata
200001d4 g       .bss	00000000 _end
20000000 g       .highcode	00000000 _highcode_vma_end
000016b2 g     F .text	0000004e TIM_TimeBaseInit
00002850 g       .dlalign	00000000 _data_lma
00001142 g     F .text	0000006c SystemCoreClockUpdate
0000131e g     F .text	00000046 Touch_Button_IRQ_Handler
00000124 g     F .text	00000024 .hidden __modsi3
00001364  w      .text	00000000 DMA1_Channel2_IRQHandler
00001366  w      .text	00000000 handle_reset
00001364  w      .text	00000000 FLASH_IRQHandler
000000a0 g     F .text	0000000a .hidden __riscv_save_0
00001364  w      .text	00000000 USART1_IRQHandler
000003aa g     F .text	00000170 ADC_Display_Draw_Vietnam_Flag
000011de g     F .text	00000056 Touch_Button_Timer_Init
00000d86 g     F .text	0000000a tft_set_color
00000a62 g     F .text	0000002c PWM_Set_Brightness
00001364  w      .text	00000000 I2C1_ER_IRQHandler
0000155a g     F .text	00000006 NVIC_PriorityGroupConfig
00000d96 g     F .text	000000e4 tft_print_char
00000ab0 g     F .text	00000052 PWM_Update_Fade
0000023c g     F .text	00000078 ADC_Display_Draw_Logo_Border
000006a2 g     F .text	00000076 Display_Control_Show_Startup_Message
000000aa g     F .text	0000000a .hidden __riscv_restore_1
000017d2 g     F .text	00000004 TIM_SetCompare1
0000152e g     F .text	0000000a GPIO_ReadInputDataBit
200001b8 g     O .bss	00000014 touch_button



Disassembly of section .init:

00000000 <_sinit>:
   0:	3660106f          	j	1366 <handle_reset>
   4:	0000                	unimp
   6:	0000                	unimp
   8:	05c8                	addi	a0,sp,708
   a:	0000                	unimp
   c:	05ca                	slli	a1,a1,0x12
	...
  2e:	0000                	unimp
  30:	1364                	addi	s1,sp,428
  32:	0000                	unimp
  34:	0000                	unimp
  36:	0000                	unimp
  38:	1364                	addi	s1,sp,428
  3a:	0000                	unimp
  3c:	0000                	unimp
  3e:	0000                	unimp
  40:	1364                	addi	s1,sp,428
  42:	0000                	unimp
  44:	1364                	addi	s1,sp,428
  46:	0000                	unimp
  48:	1364                	addi	s1,sp,428
  4a:	0000                	unimp
  4c:	1364                	addi	s1,sp,428
  4e:	0000                	unimp
  50:	05da                	slli	a1,a1,0x16
  52:	0000                	unimp
  54:	1364                	addi	s1,sp,428
  56:	0000                	unimp
  58:	1364                	addi	s1,sp,428
  5a:	0000                	unimp
  5c:	1364                	addi	s1,sp,428
  5e:	0000                	unimp
  60:	1364                	addi	s1,sp,428
  62:	0000                	unimp
  64:	1364                	addi	s1,sp,428
  66:	0000                	unimp
  68:	1364                	addi	s1,sp,428
  6a:	0000                	unimp
  6c:	1364                	addi	s1,sp,428
  6e:	0000                	unimp
  70:	1364                	addi	s1,sp,428
  72:	0000                	unimp
  74:	1364                	addi	s1,sp,428
  76:	0000                	unimp
  78:	1364                	addi	s1,sp,428
  7a:	0000                	unimp
  7c:	1364                	addi	s1,sp,428
  7e:	0000                	unimp
  80:	1364                	addi	s1,sp,428
  82:	0000                	unimp
  84:	1364                	addi	s1,sp,428
  86:	0000                	unimp
  88:	1364                	addi	s1,sp,428
  8a:	0000                	unimp
  8c:	1364                	addi	s1,sp,428
  8e:	0000                	unimp
  90:	1364                	addi	s1,sp,428
  92:	0000                	unimp
  94:	1364                	addi	s1,sp,428
  96:	0000                	unimp
  98:	05e2                	slli	a1,a1,0x18
  9a:	0000                	unimp
  9c:	0000                	unimp
	...

Disassembly of section .text:

000000a0 <__riscv_save_0>:
      a0:	1151                	addi	sp,sp,-12
      a2:	c026                	sw	s1,0(sp)
      a4:	c222                	sw	s0,4(sp)
      a6:	c406                	sw	ra,8(sp)
      a8:	8282                	jr	t0

000000aa <__riscv_restore_0>:
      aa:	4482                	lw	s1,0(sp)
      ac:	4412                	lw	s0,4(sp)
      ae:	40a2                	lw	ra,8(sp)
      b0:	0131                	addi	sp,sp,12
      b2:	8082                	ret

000000b4 <__mulsi3>:
      b4:	862a                	mv	a2,a0
      b6:	4501                	li	a0,0
      b8:	0015f693          	andi	a3,a1,1
      bc:	c291                	beqz	a3,c0 <__mulsi3+0xc>
      be:	9532                	add	a0,a0,a2
      c0:	8185                	srli	a1,a1,0x1
      c2:	0606                	slli	a2,a2,0x1
      c4:	f9f5                	bnez	a1,b8 <__mulsi3+0x4>
      c6:	8082                	ret
      c8:	0000                	unimp
	...

000000cc <__divsi3>:
      cc:	02054e63          	bltz	a0,108 <__umodsi3+0x8>
      d0:	0405c363          	bltz	a1,116 <__umodsi3+0x16>

000000d4 <__udivsi3>:
      d4:	862e                	mv	a2,a1
      d6:	85aa                	mv	a1,a0
      d8:	557d                	li	a0,-1
      da:	c215                	beqz	a2,fe <__udivsi3+0x2a>
      dc:	4685                	li	a3,1
      de:	00b67863          	bgeu	a2,a1,ee <__udivsi3+0x1a>
      e2:	00c05663          	blez	a2,ee <__udivsi3+0x1a>
      e6:	0606                	slli	a2,a2,0x1
      e8:	0686                	slli	a3,a3,0x1
      ea:	feb66ce3          	bltu	a2,a1,e2 <__udivsi3+0xe>
      ee:	4501                	li	a0,0
      f0:	00c5e463          	bltu	a1,a2,f8 <__udivsi3+0x24>
      f4:	8d91                	sub	a1,a1,a2
      f6:	8d55                	or	a0,a0,a3
      f8:	8285                	srli	a3,a3,0x1
      fa:	8205                	srli	a2,a2,0x1
      fc:	faf5                	bnez	a3,f0 <__udivsi3+0x1c>
      fe:	8082                	ret

00000100 <__umodsi3>:
     100:	8286                	mv	t0,ra
     102:	3fc9                	jal	d4 <__udivsi3>
     104:	852e                	mv	a0,a1
     106:	8282                	jr	t0
     108:	40a00533          	neg	a0,a0
     10c:	0005d763          	bgez	a1,11a <__umodsi3+0x1a>
     110:	40b005b3          	neg	a1,a1
     114:	b7c1                	j	d4 <__udivsi3>
     116:	40b005b3          	neg	a1,a1
     11a:	8286                	mv	t0,ra
     11c:	3f65                	jal	d4 <__udivsi3>
     11e:	40a00533          	neg	a0,a0
     122:	8282                	jr	t0

00000124 <__modsi3>:
     124:	8286                	mv	t0,ra
     126:	0005c763          	bltz	a1,134 <__modsi3+0x10>
     12a:	00054963          	bltz	a0,13c <__modsi3+0x18>
     12e:	375d                	jal	d4 <__udivsi3>
     130:	852e                	mv	a0,a1
     132:	8282                	jr	t0
     134:	40b005b3          	neg	a1,a1
     138:	fe055be3          	bgez	a0,12e <__modsi3+0xa>
     13c:	40a00533          	neg	a0,a0
     140:	3f51                	jal	d4 <__udivsi3>
     142:	40b00533          	neg	a0,a1
     146:	8282                	jr	t0
	...

0000014a <ADC_Display_Format_Voltage.part.0>:
     14a:	f57ff2ef          	jal	t0,a0 <__riscv_save_0>
     14e:	1161                	addi	sp,sp,-8
     150:	842e                	mv	s0,a1
     152:	3e800593          	li	a1,1000
     156:	84b2                	mv	s1,a2
     158:	c22a                	sw	a0,4(sp)
     15a:	375d                	jal	100 <__umodsi3>
     15c:	0542                	slli	a0,a0,0x10
     15e:	45a9                	li	a1,10
     160:	8141                	srli	a0,a0,0x10
     162:	3f8d                	jal	d4 <__udivsi3>
     164:	4792                	lw	a5,4(sp)
     166:	01051713          	slli	a4,a0,0x10
     16a:	8341                	srli	a4,a4,0x10
     16c:	853e                	mv	a0,a5
     16e:	3e800593          	li	a1,1000
     172:	c03a                	sw	a4,0(sp)
     174:	3785                	jal	d4 <__udivsi3>
     176:	4702                	lw	a4,0(sp)
     178:	01051693          	slli	a3,a0,0x10
     17c:	00002637          	lui	a2,0x2
     180:	82c1                	srli	a3,a3,0x10
     182:	17c60613          	addi	a2,a2,380 # 217c <memcpy+0x134>
     186:	85a6                	mv	a1,s1
     188:	8522                	mv	a0,s0
     18a:	64b010ef          	jal	ra,1fd4 <snprintf>
     18e:	0121                	addi	sp,sp,8
     190:	bf29                	j	aa <__riscv_restore_0>

00000192 <ADC_Display_Draw_Header>:
     192:	f0fff2ef          	jal	t0,a0 <__riscv_save_0>
     196:	6541                	lui	a0,0x10
     198:	157d                	addi	a0,a0,-1
     19a:	3ed000ef          	jal	ra,d86 <tft_set_color>
     19e:	4501                	li	a0,0
     1a0:	3f1000ef          	jal	ra,d90 <tft_set_background_color>
     1a4:	4581                	li	a1,0
     1a6:	4515                	li	a0,5
     1a8:	3d1000ef          	jal	ra,d78 <tft_set_cursor>
     1ac:	00002537          	lui	a0,0x2
     1b0:	14450513          	addi	a0,a0,324 # 2144 <memcpy+0xfc>
     1b4:	4c7000ef          	jal	ra,e7a <tft_print>
     1b8:	bdcd                	j	aa <__riscv_restore_0>

000001ba <ADC_Display_Draw_Channel_Labels>:
     1ba:	ee7ff2ef          	jal	t0,a0 <__riscv_save_0>
     1be:	1171                	addi	sp,sp,-4
     1c0:	4501                	li	a0,0
     1c2:	3cf000ef          	jal	ra,d90 <tft_set_background_color>
     1c6:	6789                	lui	a5,0x2
     1c8:	18878793          	addi	a5,a5,392 # 2188 <CSWTCH.3>
     1cc:	4451                	li	s0,20
     1ce:	4485                	li	s1,1
     1d0:	238a                	lhu	a0,0(a5)
     1d2:	c03e                	sw	a5,0(sp)
     1d4:	3b3000ef          	jal	ra,d86 <tft_set_color>
     1d8:	85a2                	mv	a1,s0
     1da:	4515                	li	a0,5
     1dc:	39d000ef          	jal	ra,d78 <tft_set_cursor>
     1e0:	000027b7          	lui	a5,0x2
     1e4:	13c78513          	addi	a0,a5,316 # 213c <memcpy+0xf4>
     1e8:	493000ef          	jal	ra,e7a <tft_print>
     1ec:	8526                	mv	a0,s1
     1ee:	4585                	li	a1,1
     1f0:	4ab000ef          	jal	ra,e9a <tft_print_number>
     1f4:	00002537          	lui	a0,0x2
     1f8:	14050513          	addi	a0,a0,320 # 2140 <memcpy+0xf8>
     1fc:	47f000ef          	jal	ra,e7a <tft_print>
     200:	4782                	lw	a5,0(sp)
     202:	0449                	addi	s0,s0,18
     204:	0442                	slli	s0,s0,0x10
     206:	0485                	addi	s1,s1,1
     208:	4695                	li	a3,5
     20a:	0789                	addi	a5,a5,2
     20c:	8041                	srli	s0,s0,0x10
     20e:	fcd491e3          	bne	s1,a3,1d0 <ADC_Display_Draw_Channel_Labels+0x16>
     212:	0111                	addi	sp,sp,4
     214:	bd59                	j	aa <__riscv_restore_0>

00000216 <ADC_Display_Init>:
     216:	e8bff2ef          	jal	t0,a0 <__riscv_save_0>
     21a:	200007b7          	lui	a5,0x20000
     21e:	02010737          	lui	a4,0x2010
     222:	04078793          	addi	a5,a5,64 # 20000040 <_edata>
     226:	10070713          	addi	a4,a4,256 # 2010100 <_data_lma+0x200d8b0>
     22a:	c398                	sw	a4,0(a5)
     22c:	06400713          	li	a4,100
     230:	a3da                	sh	a4,4(a5)
     232:	0007a423          	sw	zero,8(a5)
     236:	3fb1                	jal	192 <ADC_Display_Draw_Header>
     238:	3749                	jal	1ba <ADC_Display_Draw_Channel_Labels>
     23a:	bd85                	j	aa <__riscv_restore_0>

0000023c <ADC_Display_Draw_Logo_Border>:
     23c:	e65ff2ef          	jal	t0,a0 <__riscv_save_0>
     240:	4a000713          	li	a4,1184
     244:	05000693          	li	a3,80
     248:	0a000613          	li	a2,160
     24c:	4581                	li	a1,0
     24e:	4501                	li	a0,0
     250:	531000ef          	jal	ra,f80 <tft_fill_rect>
     254:	4701                	li	a4,0
     256:	04a00693          	li	a3,74
     25a:	09a00613          	li	a2,154
     25e:	458d                	li	a1,3
     260:	450d                	li	a0,3
     262:	51f000ef          	jal	ra,f80 <tft_fill_rect>
     266:	7ff00713          	li	a4,2047
     26a:	4685                	li	a3,1
     26c:	09600613          	li	a2,150
     270:	4595                	li	a1,5
     272:	4515                	li	a0,5
     274:	50d000ef          	jal	ra,f80 <tft_fill_rect>
     278:	7ff00713          	li	a4,2047
     27c:	4685                	li	a3,1
     27e:	09600613          	li	a2,150
     282:	04a00593          	li	a1,74
     286:	4515                	li	a0,5
     288:	4f9000ef          	jal	ra,f80 <tft_fill_rect>
     28c:	7ff00713          	li	a4,2047
     290:	04600693          	li	a3,70
     294:	4605                	li	a2,1
     296:	4595                	li	a1,5
     298:	4515                	li	a0,5
     29a:	4e7000ef          	jal	ra,f80 <tft_fill_rect>
     29e:	7ff00713          	li	a4,2047
     2a2:	04600693          	li	a3,70
     2a6:	4605                	li	a2,1
     2a8:	4595                	li	a1,5
     2aa:	09a00513          	li	a0,154
     2ae:	4d3000ef          	jal	ra,f80 <tft_fill_rect>
     2b2:	bbe5                	j	aa <__riscv_restore_0>

000002b4 <ADC_Display_Draw_Logo_Channels>:
     2b4:	c151                	beqz	a0,338 <ADC_Display_Draw_Logo_Channels+0x84>
     2b6:	debff2ef          	jal	t0,a0 <__riscv_save_0>
     2ba:	1141                	addi	sp,sp,-16
     2bc:	c02a                	sw	a0,0(sp)
     2be:	7ff00513          	li	a0,2047
     2c2:	2c5000ef          	jal	ra,d86 <tft_set_color>
     2c6:	4501                	li	a0,0
     2c8:	2c9000ef          	jal	ra,d90 <tft_set_background_color>
     2cc:	4405                	li	s0,1
     2ce:	000024b7          	lui	s1,0x2
     2d2:	00241793          	slli	a5,s0,0x2
     2d6:	97a2                	add	a5,a5,s0
     2d8:	0786                	slli	a5,a5,0x1
     2da:	07e5                	addi	a5,a5,25
     2dc:	07c2                	slli	a5,a5,0x10
     2de:	83c1                	srli	a5,a5,0x10
     2e0:	85be                	mv	a1,a5
     2e2:	4529                	li	a0,10
     2e4:	c23e                	sw	a5,4(sp)
     2e6:	293000ef          	jal	ra,d78 <tft_set_cursor>
     2ea:	86a2                	mv	a3,s0
     2ec:	15048613          	addi	a2,s1,336 # 2150 <memcpy+0x108>
     2f0:	45a1                	li	a1,8
     2f2:	0028                	addi	a0,sp,8
     2f4:	4e1010ef          	jal	ra,1fd4 <snprintf>
     2f8:	0028                	addi	a0,sp,8
     2fa:	381000ef          	jal	ra,e7a <tft_print>
     2fe:	4792                	lw	a5,4(sp)
     300:	02300513          	li	a0,35
     304:	85be                	mv	a1,a5
     306:	273000ef          	jal	ra,d78 <tft_set_cursor>
     30a:	4782                	lw	a5,0(sp)
     30c:	97a2                	add	a5,a5,s0
     30e:	37f4                	lbu	a3,15(a5)
     310:	ce99                	beqz	a3,32e <ADC_Display_Draw_Logo_Channels+0x7a>
     312:	97a2                	add	a5,a5,s0
     314:	23ea                	lhu	a0,6(a5)
     316:	4621                	li	a2,8
     318:	002c                	addi	a1,sp,8
     31a:	3d05                	jal	14a <ADC_Display_Format_Voltage.part.0>
     31c:	0028                	addi	a0,sp,8
     31e:	35d000ef          	jal	ra,e7a <tft_print>
     322:	0405                	addi	s0,s0,1
     324:	4795                	li	a5,5
     326:	faf416e3          	bne	s0,a5,2d2 <ADC_Display_Draw_Logo_Channels+0x1e>
     32a:	0141                	addi	sp,sp,16
     32c:	bbbd                	j	aa <__riscv_restore_0>
     32e:	00002537          	lui	a0,0x2
     332:	15850513          	addi	a0,a0,344 # 2158 <memcpy+0x110>
     336:	b7e5                	j	31e <ADC_Display_Draw_Logo_Channels+0x6a>
     338:	8082                	ret

0000033a <ADC_Display_Draw_Logo_Main_Voltage>:
     33a:	d67ff2ef          	jal	t0,a0 <__riscv_save_0>
     33e:	842a                	mv	s0,a0
     340:	6541                	lui	a0,0x10
     342:	1141                	addi	sp,sp,-16
     344:	157d                	addi	a0,a0,-1
     346:	241000ef          	jal	ra,d86 <tft_set_color>
     34a:	4501                	li	a0,0
     34c:	245000ef          	jal	ra,d90 <tft_set_background_color>
     350:	3e800593          	li	a1,1000
     354:	8522                	mv	a0,s0
     356:	336d                	jal	100 <__umodsi3>
     358:	01051713          	slli	a4,a0,0x10
     35c:	8341                	srli	a4,a4,0x10
     35e:	3e800593          	li	a1,1000
     362:	8522                	mv	a0,s0
     364:	c03a                	sw	a4,0(sp)
     366:	33bd                	jal	d4 <__udivsi3>
     368:	4702                	lw	a4,0(sp)
     36a:	01051693          	slli	a3,a0,0x10
     36e:	00002637          	lui	a2,0x2
     372:	82c1                	srli	a3,a3,0x10
     374:	17060613          	addi	a2,a2,368 # 2170 <memcpy+0x128>
     378:	45b1                	li	a1,12
     37a:	0048                	addi	a0,sp,4
     37c:	459010ef          	jal	ra,1fd4 <snprintf>
     380:	02d00593          	li	a1,45
     384:	06400513          	li	a0,100
     388:	1f1000ef          	jal	ra,d78 <tft_set_cursor>
     38c:	0048                	addi	a0,sp,4
     38e:	2ed000ef          	jal	ra,e7a <tft_print>
     392:	7ff00713          	li	a4,2047
     396:	4689                	li	a3,2
     398:	4609                	li	a2,2
     39a:	03200593          	li	a1,50
     39e:	09100513          	li	a0,145
     3a2:	3df000ef          	jal	ra,f80 <tft_fill_rect>
     3a6:	0141                	addi	sp,sp,16
     3a8:	b309                	j	aa <__riscv_restore_0>

000003aa <ADC_Display_Draw_Vietnam_Flag>:
     3aa:	cf7ff2ef          	jal	t0,a0 <__riscv_save_0>
     3ae:	6739                	lui	a4,0xe
     3b0:	1101                	addi	sp,sp,-32
     3b2:	92370713          	addi	a4,a4,-1757 # d923 <_data_lma+0xb0d3>
     3b6:	c836                	sw	a3,16(sp)
     3b8:	c22a                	sw	a0,4(sp)
     3ba:	c42e                	sw	a1,8(sp)
     3bc:	c632                	sw	a2,12(sp)
     3be:	3c3000ef          	jal	ra,f80 <tft_fill_rect>
     3c2:	47b2                	lw	a5,12(sp)
     3c4:	4712                	lw	a4,4(sp)
     3c6:	46c2                	lw	a3,16(sp)
     3c8:	8385                	srli	a5,a5,0x1
     3ca:	97ba                	add	a5,a5,a4
     3cc:	07c2                	slli	a5,a5,0x10
     3ce:	83c1                	srli	a5,a5,0x10
     3d0:	c03e                	sw	a5,0(sp)
     3d2:	47c2                	lw	a5,16(sp)
     3d4:	4732                	lw	a4,12(sp)
     3d6:	0017d413          	srli	s0,a5,0x1
     3da:	47a2                	lw	a5,8(sp)
     3dc:	943e                	add	s0,s0,a5
     3de:	0442                	slli	s0,s0,0x10
     3e0:	8041                	srli	s0,s0,0x10
     3e2:	863c                	lhu	a5,12(sp)
     3e4:	00e6f363          	bgeu	a3,a4,3ea <ADC_Display_Draw_Vietnam_Flag+0x40>
     3e8:	80bc                	lhu	a5,16(sp)
     3ea:	07c2                	slli	a5,a5,0x10
     3ec:	83c1                	srli	a5,a5,0x10
     3ee:	458d                	li	a1,3
     3f0:	853e                	mv	a0,a5
     3f2:	cc3e                	sw	a5,24(sp)
     3f4:	39e1                	jal	cc <__divsi3>
     3f6:	4782                	lw	a5,0(sp)
     3f8:	01051493          	slli	s1,a0,0x10
     3fc:	80c1                	srli	s1,s1,0x10
     3fe:	0014d313          	srli	t1,s1,0x1
     402:	406405b3          	sub	a1,s0,t1
     406:	fff78513          	addi	a0,a5,-1
     40a:	62c1                	lui	t0,0x10
     40c:	05c2                	slli	a1,a1,0x10
     40e:	0542                	slli	a0,a0,0x10
     410:	fe028713          	addi	a4,t0,-32 # ffe0 <_data_lma+0xd790>
     414:	86a6                	mv	a3,s1
     416:	4609                	li	a2,2
     418:	81c1                	srli	a1,a1,0x10
     41a:	8141                	srli	a0,a0,0x10
     41c:	ca1a                	sw	t1,20(sp)
     41e:	363000ef          	jal	ra,f80 <tft_fill_rect>
     422:	4782                	lw	a5,0(sp)
     424:	4352                	lw	t1,20(sp)
     426:	fff40593          	addi	a1,s0,-1
     42a:	62c1                	lui	t0,0x10
     42c:	40678533          	sub	a0,a5,t1
     430:	05c2                	slli	a1,a1,0x10
     432:	0542                	slli	a0,a0,0x10
     434:	fe028713          	addi	a4,t0,-32 # ffe0 <_data_lma+0xd790>
     438:	4689                	li	a3,2
     43a:	8626                	mv	a2,s1
     43c:	81c1                	srli	a1,a1,0x10
     43e:	8141                	srli	a0,a0,0x10
     440:	341000ef          	jal	ra,f80 <tft_fill_rect>
     444:	47e2                	lw	a5,24(sp)
     446:	472d                	li	a4,11
     448:	06f77f63          	bgeu	a4,a5,4c6 <ADC_Display_Draw_Vietnam_Flag+0x11c>
     44c:	853e                	mv	a0,a5
     44e:	55dd                	li	a1,-9
     450:	39b5                	jal	cc <__divsi3>
     452:	ca2a                	sw	a0,20(sp)
     454:	458d                	li	a1,3
     456:	8526                	mv	a0,s1
     458:	39b5                	jal	d4 <__udivsi3>
     45a:	47d2                	lw	a5,20(sp)
     45c:	46b2                	lw	a3,12(sp)
     45e:	4612                	lw	a2,4(sp)
     460:	01051493          	slli	s1,a0,0x10
     464:	80c1                	srli	s1,s1,0x10
     466:	873e                	mv	a4,a5
     468:	00c68333          	add	t1,a3,a2
     46c:	04e4df63          	bge	s1,a4,4ca <ADC_Display_Draw_Vietnam_Flag+0x120>
     470:	4732                	lw	a4,12(sp)
     472:	4692                	lw	a3,4(sp)
     474:	96ba                	add	a3,a3,a4
     476:	4702                	lw	a4,0(sp)
     478:	4612                	lw	a2,4(sp)
     47a:	8f1d                	sub	a4,a4,a5
     47c:	04c74263          	blt	a4,a2,4c0 <ADC_Display_Draw_Vietnam_Flag+0x116>
     480:	04d75063          	bge	a4,a3,4c0 <ADC_Display_Draw_Vietnam_Flag+0x116>
     484:	4622                	lw	a2,8(sp)
     486:	00f40733          	add	a4,s0,a5
     48a:	02c74b63          	blt	a4,a2,4c0 <ADC_Display_Draw_Vietnam_Flag+0x116>
     48e:	4642                	lw	a2,16(sp)
     490:	45a2                	lw	a1,8(sp)
     492:	962e                	add	a2,a2,a1
     494:	02c75663          	bge	a4,a2,4c0 <ADC_Display_Draw_Vietnam_Flag+0x116>
     498:	01079513          	slli	a0,a5,0x10
     49c:	c63e                	sw	a5,12(sp)
     49e:	4782                	lw	a5,0(sp)
     4a0:	8141                	srli	a0,a0,0x10
     4a2:	008505b3          	add	a1,a0,s0
     4a6:	40a78533          	sub	a0,a5,a0
     4aa:	6641                	lui	a2,0x10
     4ac:	05c2                	slli	a1,a1,0x10
     4ae:	0542                	slli	a0,a0,0x10
     4b0:	1601                	addi	a2,a2,-32
     4b2:	81c1                	srli	a1,a1,0x10
     4b4:	8141                	srli	a0,a0,0x10
     4b6:	ca36                	sw	a3,20(sp)
     4b8:	28d000ef          	jal	ra,f44 <tft_draw_pixel>
     4bc:	46d2                	lw	a3,20(sp)
     4be:	47b2                	lw	a5,12(sp)
     4c0:	0785                	addi	a5,a5,1
     4c2:	faf4dae3          	bge	s1,a5,476 <ADC_Display_Draw_Vietnam_Flag+0xcc>
     4c6:	6105                	addi	sp,sp,32
     4c8:	b6cd                	j	aa <__riscv_restore_0>
     4ca:	4682                	lw	a3,0(sp)
     4cc:	4612                	lw	a2,4(sp)
     4ce:	96ba                	add	a3,a3,a4
     4d0:	04c6c363          	blt	a3,a2,516 <ADC_Display_Draw_Vietnam_Flag+0x16c>
     4d4:	0466d163          	bge	a3,t1,516 <ADC_Display_Draw_Vietnam_Flag+0x16c>
     4d8:	4622                	lw	a2,8(sp)
     4da:	00e406b3          	add	a3,s0,a4
     4de:	02c6cc63          	blt	a3,a2,516 <ADC_Display_Draw_Vietnam_Flag+0x16c>
     4e2:	4642                	lw	a2,16(sp)
     4e4:	45a2                	lw	a1,8(sp)
     4e6:	962e                	add	a2,a2,a1
     4e8:	02c6d763          	bge	a3,a2,516 <ADC_Display_Draw_Vietnam_Flag+0x16c>
     4ec:	cc3e                	sw	a5,24(sp)
     4ee:	4782                	lw	a5,0(sp)
     4f0:	01071513          	slli	a0,a4,0x10
     4f4:	8141                	srli	a0,a0,0x10
     4f6:	00a405b3          	add	a1,s0,a0
     4fa:	953e                	add	a0,a0,a5
     4fc:	6641                	lui	a2,0x10
     4fe:	05c2                	slli	a1,a1,0x10
     500:	0542                	slli	a0,a0,0x10
     502:	1601                	addi	a2,a2,-32
     504:	81c1                	srli	a1,a1,0x10
     506:	8141                	srli	a0,a0,0x10
     508:	ce1a                	sw	t1,28(sp)
     50a:	ca3a                	sw	a4,20(sp)
     50c:	239000ef          	jal	ra,f44 <tft_draw_pixel>
     510:	4372                	lw	t1,28(sp)
     512:	47e2                	lw	a5,24(sp)
     514:	4752                	lw	a4,20(sp)
     516:	0705                	addi	a4,a4,1
     518:	bf91                	j	46c <ADC_Display_Draw_Vietnam_Flag+0xc2>

0000051a <ADC_Display_Draw_Logo_Header>:
     51a:	b87ff2ef          	jal	t0,a0 <__riscv_save_0>
     51e:	7ff00513          	li	a0,2047
     522:	065000ef          	jal	ra,d86 <tft_set_color>
     526:	4501                	li	a0,0
     528:	069000ef          	jal	ra,d90 <tft_set_background_color>
     52c:	46b1                	li	a3,12
     52e:	4651                	li	a2,20
     530:	45a9                	li	a1,10
     532:	4529                	li	a0,10
     534:	3d9d                	jal	3aa <ADC_Display_Draw_Vietnam_Flag>
     536:	45a9                	li	a1,10
     538:	02300513          	li	a0,35
     53c:	03d000ef          	jal	ra,d78 <tft_set_cursor>
     540:	00002537          	lui	a0,0x2
     544:	16050513          	addi	a0,a0,352 # 2160 <memcpy+0x118>
     548:	133000ef          	jal	ra,e7a <tft_print>
     54c:	45d1                	li	a1,20
     54e:	02300513          	li	a0,35
     552:	027000ef          	jal	ra,d78 <tft_set_cursor>
     556:	00002537          	lui	a0,0x2
     55a:	16850513          	addi	a0,a0,360 # 2168 <memcpy+0x120>
     55e:	11d000ef          	jal	ra,e7a <tft_print>
     562:	7ff00713          	li	a4,2047
     566:	4685                	li	a3,1
     568:	4679                	li	a2,30
     56a:	45b1                	li	a1,12
     56c:	05a00513          	li	a0,90
     570:	211000ef          	jal	ra,f80 <tft_fill_rect>
     574:	7ff00713          	li	a4,2047
     578:	4685                	li	a3,1
     57a:	4665                	li	a2,25
     57c:	45bd                	li	a1,15
     57e:	05a00513          	li	a0,90
     582:	1ff000ef          	jal	ra,f80 <tft_fill_rect>
     586:	7ff00713          	li	a4,2047
     58a:	4685                	li	a3,1
     58c:	4651                	li	a2,20
     58e:	45c9                	li	a1,18
     590:	05a00513          	li	a0,90
     594:	1ed000ef          	jal	ra,f80 <tft_fill_rect>
     598:	be09                	j	aa <__riscv_restore_0>

0000059a <ADC_Display_Draw_Logo_Style>:
     59a:	b07ff2ef          	jal	t0,a0 <__riscv_save_0>
     59e:	842a                	mv	s0,a0
     5a0:	4a000713          	li	a4,1184
     5a4:	4501                	li	a0,0
     5a6:	05000693          	li	a3,80
     5aa:	0a000613          	li	a2,160
     5ae:	4581                	li	a1,0
     5b0:	1d1000ef          	jal	ra,f80 <tft_fill_rect>
     5b4:	3161                	jal	23c <ADC_Display_Draw_Logo_Border>
     5b6:	3795                	jal	51a <ADC_Display_Draw_Logo_Header>
     5b8:	8522                	mv	a0,s0
     5ba:	39ed                	jal	2b4 <ADC_Display_Draw_Logo_Channels>
     5bc:	c409                	beqz	s0,5c6 <ADC_Display_Draw_Logo_Style+0x2c>
     5be:	281c                	lbu	a5,16(s0)
     5c0:	c399                	beqz	a5,5c6 <ADC_Display_Draw_Logo_Style+0x2c>
     5c2:	240a                	lhu	a0,8(s0)
     5c4:	3b9d                	jal	33a <ADC_Display_Draw_Logo_Main_Voltage>
     5c6:	b4d5                	j	aa <__riscv_restore_0>

000005c8 <NMI_Handler>:
     5c8:	a001                	j	5c8 <NMI_Handler>

000005ca <HardFault_Handler>:
     5ca:	beef07b7          	lui	a5,0xbeef0
     5ce:	e000e737          	lui	a4,0xe000e
     5d2:	08078793          	addi	a5,a5,128 # beef0080 <__global_pointer$+0x9eeef840>
     5d6:	c73c                	sw	a5,72(a4)
     5d8:	a001                	j	5d8 <HardFault_Handler+0xe>

000005da <EXTI7_0_IRQHandler>:
     5da:	545000ef          	jal	ra,131e <Touch_Button_IRQ_Handler>
     5de:	30200073          	mret

000005e2 <TIM2_IRQHandler>:
     5e2:	4585                	li	a1,1
     5e4:	40000537          	lui	a0,0x40000
     5e8:	1ee010ef          	jal	ra,17d6 <TIM_GetITStatus>
     5ec:	c919                	beqz	a0,602 <TIM2_IRQHandler+0x20>
     5ee:	9741a783          	lw	a5,-1676(gp) # 200001b4 <system_tick_ms>
     5f2:	4585                	li	a1,1
     5f4:	40000537          	lui	a0,0x40000
     5f8:	0785                	addi	a5,a5,1
     5fa:	96f1aa23          	sw	a5,-1676(gp) # 200001b4 <system_tick_ms>
     5fe:	1f0010ef          	jal	ra,17ee <TIM_ClearITPendingBit>
     602:	30200073          	mret

00000606 <Display_Control_Update>:
     606:	a9bff2ef          	jal	t0,a0 <__riscv_save_0>
     60a:	80c18413          	addi	s0,gp,-2036 # 2000004c <display_control>
     60e:	205c                	lbu	a5,4(s0)
     610:	cb89                	beqz	a5,622 <Display_Control_Update+0x1c>
     612:	2979                	jal	ab0 <PWM_Update_Fade>
     614:	401c                	lw	a5,0(s0)
     616:	4705                	li	a4,1
     618:	00e78663          	beq	a5,a4,624 <Display_Control_Update+0x1e>
     61c:	470d                	li	a4,3
     61e:	00e78a63          	beq	a5,a4,632 <Display_Control_Update+0x2c>
     622:	b461                	j	aa <__riscv_restore_0>
     624:	8201c703          	lbu	a4,-2016(gp) # 20000060 <pwm_control+0x4>
     628:	ff6d                	bnez	a4,622 <Display_Control_Update+0x1c>
     62a:	4709                	li	a4,2
     62c:	c018                	sw	a4,0(s0)
     62e:	b05c                	sb	a5,5(s0)
     630:	bfcd                	j	622 <Display_Control_Update+0x1c>
     632:	8201c783          	lbu	a5,-2016(gp) # 20000060 <pwm_control+0x4>
     636:	f7f5                	bnez	a5,622 <Display_Control_Update+0x1c>
     638:	21e5                	jal	b20 <PWM_Get_Brightness>
     63a:	f565                	bnez	a0,622 <Display_Control_Update+0x1c>
     63c:	00042023          	sw	zero,0(s0)
     640:	b7cd                	j	622 <Display_Control_Update+0x1c>

00000642 <Display_Control_Turn_On>:
     642:	80c18793          	addi	a5,gp,-2036 # 2000004c <display_control>
     646:	4398                	lw	a4,0(a5)
     648:	e719                	bnez	a4,656 <Display_Control_Turn_On+0x14>
     64a:	a57ff2ef          	jal	t0,a0 <__riscv_save_0>
     64e:	4705                	li	a4,1
     650:	c398                	sw	a4,0(a5)
     652:	2945                	jal	b02 <PWM_Turn_On>
     654:	bc99                	j	aa <__riscv_restore_0>
     656:	8082                	ret

00000658 <Display_Control_Turn_Off>:
     658:	80c18793          	addi	a5,gp,-2036 # 2000004c <display_control>
     65c:	4394                	lw	a3,0(a5)
     65e:	4709                	li	a4,2
     660:	00e69863          	bne	a3,a4,670 <Display_Control_Turn_Off+0x18>
     664:	a3dff2ef          	jal	t0,a0 <__riscv_save_0>
     668:	470d                	li	a4,3
     66a:	c398                	sw	a4,0(a5)
     66c:	215d                	jal	b12 <PWM_Turn_Off>
     66e:	bc35                	j	aa <__riscv_restore_0>
     670:	8082                	ret

00000672 <Display_Control_Toggle>:
     672:	a2fff2ef          	jal	t0,a0 <__riscv_save_0>
     676:	80c1a783          	lw	a5,-2036(gp) # 2000004c <display_control>
     67a:	e399                	bnez	a5,680 <Display_Control_Toggle+0xe>
     67c:	37d9                	jal	642 <Display_Control_Turn_On>
     67e:	b435                	j	aa <__riscv_restore_0>
     680:	4709                	li	a4,2
     682:	fee79ee3          	bne	a5,a4,67e <Display_Control_Toggle+0xc>
     686:	3fc9                	jal	658 <Display_Control_Turn_Off>
     688:	bfdd                	j	67e <Display_Control_Toggle+0xc>

0000068a <Display_Control_Clear_Screen>:
     68a:	a17ff2ef          	jal	t0,a0 <__riscv_save_0>
     68e:	4701                	li	a4,0
     690:	05000693          	li	a3,80
     694:	0a000613          	li	a2,160
     698:	4581                	li	a1,0
     69a:	4501                	li	a0,0
     69c:	0e5000ef          	jal	ra,f80 <tft_fill_rect>
     6a0:	b429                	j	aa <__riscv_restore_0>

000006a2 <Display_Control_Show_Startup_Message>:
     6a2:	9ffff2ef          	jal	t0,a0 <__riscv_save_0>
     6a6:	37d5                	jal	68a <Display_Control_Clear_Screen>
     6a8:	6441                	lui	s0,0x10
     6aa:	fff40513          	addi	a0,s0,-1 # ffff <_data_lma+0xd7af>
     6ae:	6d8000ef          	jal	ra,d86 <tft_set_color>
     6b2:	4501                	li	a0,0
     6b4:	6dc000ef          	jal	ra,d90 <tft_set_background_color>
     6b8:	45a9                	li	a1,10
     6ba:	4529                	li	a0,10
     6bc:	6bc000ef          	jal	ra,d78 <tft_set_cursor>
     6c0:	00002537          	lui	a0,0x2
     6c4:	19050513          	addi	a0,a0,400 # 2190 <CSWTCH.3+0x8>
     6c8:	7b2000ef          	jal	ra,e7a <tft_print>
     6cc:	45e5                	li	a1,25
     6ce:	4529                	li	a0,10
     6d0:	6a8000ef          	jal	ra,d78 <tft_set_cursor>
     6d4:	00002537          	lui	a0,0x2
     6d8:	1a050513          	addi	a0,a0,416 # 21a0 <CSWTCH.3+0x18>
     6dc:	79e000ef          	jal	ra,e7a <tft_print>
     6e0:	02d00593          	li	a1,45
     6e4:	4515                	li	a0,5
     6e6:	692000ef          	jal	ra,d78 <tft_set_cursor>
     6ea:	fe040513          	addi	a0,s0,-32
     6ee:	698000ef          	jal	ra,d86 <tft_set_color>
     6f2:	00002537          	lui	a0,0x2
     6f6:	1b050513          	addi	a0,a0,432 # 21b0 <CSWTCH.3+0x28>
     6fa:	780000ef          	jal	ra,e7a <tft_print>
     6fe:	03c00593          	li	a1,60
     702:	4515                	li	a0,5
     704:	674000ef          	jal	ra,d78 <tft_set_cursor>
     708:	00002537          	lui	a0,0x2
     70c:	1c050513          	addi	a0,a0,448 # 21c0 <CSWTCH.3+0x38>
     710:	76a000ef          	jal	ra,e7a <tft_print>
     714:	997ff06f          	j	aa <__riscv_restore_0>

00000718 <Display_Control_Init>:
     718:	989ff2ef          	jal	t0,a0 <__riscv_save_0>
     71c:	80c18413          	addi	s0,gp,-2036 # 2000004c <display_control>
     720:	10000793          	li	a5,256
     724:	a05e                	sh	a5,4(s0)
     726:	00042023          	sw	zero,0(s0)
     72a:	00042423          	sw	zero,8(s0)
     72e:	296d                	jal	be8 <tft_init>
     730:	3fa9                	jal	68a <Display_Control_Clear_Screen>
     732:	3f85                	jal	6a2 <Display_Control_Show_Startup_Message>
     734:	4785                	li	a5,1
     736:	a05c                	sb	a5,4(s0)
     738:	26e9                	jal	b02 <PWM_Turn_On>
     73a:	971ff06f          	j	aa <__riscv_restore_0>

0000073e <Display_Text_Custom>:
     73e:	963ff2ef          	jal	t0,a0 <__riscv_save_0>
     742:	1151                	addi	sp,sp,-12
     744:	842a                	mv	s0,a0
     746:	8536                	mv	a0,a3
     748:	c43e                	sw	a5,8(sp)
     74a:	c02e                	sw	a1,0(sp)
     74c:	84b2                	mv	s1,a2
     74e:	c23a                	sw	a4,4(sp)
     750:	636000ef          	jal	ra,d86 <tft_set_color>
     754:	4712                	lw	a4,4(sp)
     756:	853a                	mv	a0,a4
     758:	638000ef          	jal	ra,d90 <tft_set_background_color>
     75c:	47a2                	lw	a5,8(sp)
     75e:	4712                	lw	a4,4(sp)
     760:	cb89                	beqz	a5,772 <Display_Text_Custom+0x34>
     762:	05000693          	li	a3,80
     766:	0a000613          	li	a2,160
     76a:	4581                	li	a1,0
     76c:	4501                	li	a0,0
     76e:	013000ef          	jal	ra,f80 <tft_fill_rect>
     772:	4502                	lw	a0,0(sp)
     774:	85a6                	mv	a1,s1
     776:	602000ef          	jal	ra,d78 <tft_set_cursor>
     77a:	8522                	mv	a0,s0
     77c:	6fe000ef          	jal	ra,e7a <tft_print>
     780:	0131                	addi	sp,sp,12
     782:	929ff06f          	j	aa <__riscv_restore_0>

00000786 <Display_Text_Centered>:
     786:	91bff2ef          	jal	t0,a0 <__riscv_save_0>
     78a:	87ba                	mv	a5,a4
     78c:	4301                	li	t1,0
     78e:	00130293          	addi	t0,t1,1
     792:	00550733          	add	a4,a0,t0
     796:	fff70703          	lb	a4,-1(a4) # e000dfff <__global_pointer$+0xc000d7bf>
     79a:	e71d                	bnez	a4,7c8 <Display_Text_Centered+0x42>
     79c:	0342                	slli	t1,t1,0x10
     79e:	01035313          	srli	t1,t1,0x10
     7a2:	00231713          	slli	a4,t1,0x2
     7a6:	40e30333          	sub	t1,t1,a4
     7aa:	05030313          	addi	t1,t1,80
     7ae:	0342                	slli	t1,t1,0x10
     7b0:	01035313          	srli	t1,t1,0x10
     7b4:	e199                	bnez	a1,7ba <Display_Text_Centered+0x34>
     7b6:	02400593          	li	a1,36
     7ba:	8736                	mv	a4,a3
     7bc:	86b2                	mv	a3,a2
     7be:	862e                	mv	a2,a1
     7c0:	859a                	mv	a1,t1
     7c2:	3fb5                	jal	73e <Display_Text_Custom>
     7c4:	8e7ff06f          	j	aa <__riscv_restore_0>
     7c8:	8316                	mv	t1,t0
     7ca:	b7d1                	j	78e <Display_Text_Centered+0x8>

000007cc <Display_Text_Clear_Screen>:
     7cc:	8d5ff2ef          	jal	t0,a0 <__riscv_save_0>
     7d0:	872a                	mv	a4,a0
     7d2:	05000693          	li	a3,80
     7d6:	0a000613          	li	a2,160
     7da:	4581                	li	a1,0
     7dc:	4501                	li	a0,0
     7de:	7a2000ef          	jal	ra,f80 <tft_fill_rect>
     7e2:	8c9ff06f          	j	aa <__riscv_restore_0>

000007e6 <Display_Text_Welcome>:
     7e6:	8bbff2ef          	jal	t0,a0 <__riscv_save_0>
     7ea:	4501                	li	a0,0
     7ec:	37c5                	jal	7cc <Display_Text_Clear_Screen>
     7ee:	00002537          	lui	a0,0x2
     7f2:	4701                	li	a4,0
     7f4:	4681                	li	a3,0
     7f6:	7ff00613          	li	a2,2047
     7fa:	45d1                	li	a1,20
     7fc:	1d850513          	addi	a0,a0,472 # 21d8 <CSWTCH.3+0x50>
     800:	3759                	jal	786 <Display_Text_Centered>
     802:	6441                	lui	s0,0x10
     804:	00002537          	lui	a0,0x2
     808:	fff40613          	addi	a2,s0,-1 # ffff <_data_lma+0xd7af>
     80c:	4701                	li	a4,0
     80e:	4681                	li	a3,0
     810:	02300593          	li	a1,35
     814:	14450513          	addi	a0,a0,324 # 2144 <memcpy+0xfc>
     818:	37bd                	jal	786 <Display_Text_Centered>
     81a:	00002537          	lui	a0,0x2
     81e:	4701                	li	a4,0
     820:	4681                	li	a3,0
     822:	fe040613          	addi	a2,s0,-32
     826:	03200593          	li	a1,50
     82a:	1d050513          	addi	a0,a0,464 # 21d0 <CSWTCH.3+0x48>
     82e:	3fa1                	jal	786 <Display_Text_Centered>
     830:	87bff06f          	j	aa <__riscv_restore_0>

00000834 <System_Init>:
     834:	86dff2ef          	jal	t0,a0 <__riscv_save_0>
     838:	00002537          	lui	a0,0x2
     83c:	1e450513          	addi	a0,a0,484 # 21e4 <CSWTCH.3+0x5c>
     840:	7be010ef          	jal	ra,1ffe <puts>
     844:	24a9                	jal	a8e <PWM_Config_Init>
     846:	00002537          	lui	a0,0x2
     84a:	1f850513          	addi	a0,a0,504 # 21f8 <CSWTCH.3+0x70>
     84e:	7b0010ef          	jal	ra,1ffe <puts>
     852:	225000ef          	jal	ra,1276 <Touch_Button_Init>
     856:	00002537          	lui	a0,0x2
     85a:	20c50513          	addi	a0,a0,524 # 220c <CSWTCH.3+0x84>
     85e:	7a0010ef          	jal	ra,1ffe <puts>
     862:	3d5d                	jal	718 <Display_Control_Init>
     864:	00002537          	lui	a0,0x2
     868:	22850513          	addi	a0,a0,552 # 2228 <CSWTCH.3+0xa0>
     86c:	792010ef          	jal	ra,1ffe <puts>
     870:	9a7ff0ef          	jal	ra,216 <ADC_Display_Init>
     874:	00002537          	lui	a0,0x2
     878:	24850513          	addi	a0,a0,584 # 2248 <CSWTCH.3+0xc0>
     87c:	782010ef          	jal	ra,1ffe <puts>
     880:	00002537          	lui	a0,0x2
     884:	4705                	li	a4,1
     886:	26450513          	addi	a0,a0,612 # 2264 <CSWTCH.3+0xdc>
     88a:	80e18c23          	sb	a4,-2024(gp) # 20000058 <system_initialized>
     88e:	770010ef          	jal	ra,1ffe <puts>
     892:	819ff06f          	j	aa <__riscv_restore_0>

00000896 <main>:
     896:	80bff2ef          	jal	t0,a0 <__riscv_save_0>
     89a:	1121                	addi	sp,sp,-24
     89c:	4505                	li	a0,1
     89e:	4bd000ef          	jal	ra,155a <NVIC_PriorityGroupConfig>
     8a2:	0a1000ef          	jal	ra,1142 <SystemCoreClockUpdate>
     8a6:	04a010ef          	jal	ra,18f0 <Delay_Init>
     8aa:	6571                	lui	a0,0x1c
     8ac:	20050513          	addi	a0,a0,512 # 1c200 <_data_lma+0x199b0>
     8b0:	0ae010ef          	jal	ra,195e <USART_Printf_Init>
     8b4:	00002537          	lui	a0,0x2
     8b8:	28450513          	addi	a0,a0,644 # 2284 <CSWTCH.3+0xfc>
     8bc:	742010ef          	jal	ra,1ffe <puts>
     8c0:	200007b7          	lui	a5,0x20000
     8c4:	0147a583          	lw	a1,20(a5) # 20000014 <SystemCoreClock>
     8c8:	00002537          	lui	a0,0x2
     8cc:	2a450513          	addi	a0,a0,676 # 22a4 <CSWTCH.3+0x11c>
     8d0:	6e0010ef          	jal	ra,1fb0 <printf>
     8d4:	343000ef          	jal	ra,1416 <DBGMCU_GetCHIPID>
     8d8:	85aa                	mv	a1,a0
     8da:	00002537          	lui	a0,0x2
     8de:	2b850513          	addi	a0,a0,696 # 22b8 <CSWTCH.3+0x130>
     8e2:	6ce010ef          	jal	ra,1fb0 <printf>
     8e6:	37b9                	jal	834 <System_Init>
     8e8:	3dfd                	jal	7e6 <Display_Text_Welcome>
     8ea:	7d000513          	li	a0,2000
     8ee:	036010ef          	jal	ra,1924 <Delay_Ms>
     8f2:	0ce447b7          	lui	a5,0xce44
     8f6:	ead78793          	addi	a5,a5,-339 # ce43ead <_data_lma+0xce4165d>
     8fa:	c63e                	sw	a5,12(sp)
     8fc:	138807b7          	lui	a5,0x13880
     900:	67278793          	addi	a5,a5,1650 # 13880672 <_data_lma+0x1387de22>
     904:	c83e                	sw	a5,16(sp)
     906:	0044                	addi	s1,sp,4
     908:	6785                	lui	a5,0x1
     90a:	c202                	sw	zero,4(sp)
     90c:	c402                	sw	zero,8(sp)
     90e:	ca02                	sw	zero,20(sp)
     910:	8426                	mv	s0,s1
     912:	4605                	li	a2,1
     914:	ce478793          	addi	a5,a5,-796 # ce4 <tft_init+0xfc>
     918:	a890                	sb	a2,16(s1)
     91a:	240a                	lhu	a0,8(s0)
     91c:	85be                	mv	a1,a5
     91e:	c032                	sw	a2,0(sp)
     920:	0532                	slli	a0,a0,0xc
     922:	faaff0ef          	jal	ra,cc <__divsi3>
     926:	a00a                	sh	a0,0(s0)
     928:	6785                	lui	a5,0x1
     92a:	0409                	addi	s0,s0,2
     92c:	0074                	addi	a3,sp,12
     92e:	0485                	addi	s1,s1,1
     930:	ce478793          	addi	a5,a5,-796 # ce4 <tft_init+0xfc>
     934:	4602                	lw	a2,0(sp)
     936:	0058                	addi	a4,sp,4
     938:	fed410e3          	bne	s0,a3,918 <main+0x82>
     93c:	853a                	mv	a0,a4
     93e:	39b1                	jal	59a <ADC_Display_Draw_Logo_Style>
     940:	00002437          	lui	s0,0x2
     944:	15d000ef          	jal	ra,12a0 <Touch_Button_Update>
     948:	1cb000ef          	jal	ra,1312 <Touch_Button_Get_Event>
     94c:	4789                	li	a5,2
     94e:	02f50463          	beq	a0,a5,976 <main+0xe0>
     952:	478d                	li	a5,3
     954:	02f50763          	beq	a0,a5,982 <main+0xec>
     958:	4785                	li	a5,1
     95a:	00f51963          	bne	a0,a5,96c <main+0xd6>
     95e:	00002537          	lui	a0,0x2
     962:	2c850513          	addi	a0,a0,712 # 22c8 <CSWTCH.3+0x140>
     966:	698010ef          	jal	ra,1ffe <puts>
     96a:	39e1                	jal	642 <Display_Control_Turn_On>
     96c:	3969                	jal	606 <Display_Control_Update>
     96e:	4505                	li	a0,1
     970:	7b5000ef          	jal	ra,1924 <Delay_Ms>
     974:	bfc1                	j	944 <main+0xae>
     976:	2f440513          	addi	a0,s0,756 # 22f4 <CSWTCH.3+0x16c>
     97a:	684010ef          	jal	ra,1ffe <puts>
     97e:	39d5                	jal	672 <Display_Control_Toggle>
     980:	b7f5                	j	96c <main+0xd6>
     982:	00002537          	lui	a0,0x2
     986:	32050513          	addi	a0,a0,800 # 2320 <CSWTCH.3+0x198>
     98a:	674010ef          	jal	ra,1ffe <puts>
     98e:	31e9                	jal	658 <Display_Control_Turn_Off>
     990:	bff1                	j	96c <main+0xd6>

00000992 <PWM_GPIO_Config>:
     992:	f0eff2ef          	jal	t0,a0 <__riscv_save_0>
     996:	1151                	addi	sp,sp,-12
     998:	4585                	li	a1,1
     99a:	02000513          	li	a0,32
     99e:	c002                	sw	zero,0(sp)
     9a0:	c202                	sw	zero,4(sp)
     9a2:	c402                	sw	zero,8(sp)
     9a4:	4d3000ef          	jal	ra,1676 <RCC_APB2PeriphClockCmd>
     9a8:	4791                	li	a5,4
     9aa:	807c                	sh	a5,0(sp)
     9ac:	40011537          	lui	a0,0x40011
     9b0:	47e1                	li	a5,24
     9b2:	c43e                	sw	a5,8(sp)
     9b4:	858a                	mv	a1,sp
     9b6:	478d                	li	a5,3
     9b8:	40050513          	addi	a0,a0,1024 # 40011400 <__global_pointer$+0x20010bc0>
     9bc:	c23e                	sw	a5,4(sp)
     9be:	2f5000ef          	jal	ra,14b2 <GPIO_Init>
     9c2:	0131                	addi	sp,sp,12
     9c4:	ee6ff06f          	j	aa <__riscv_restore_0>

000009c8 <PWM_Timer_Config>:
     9c8:	ed8ff2ef          	jal	t0,a0 <__riscv_save_0>
     9cc:	6505                	lui	a0,0x1
     9ce:	1111                	addi	sp,sp,-28
     9d0:	4585                	li	a1,1
     9d2:	80050513          	addi	a0,a0,-2048 # 800 <Display_Text_Welcome+0x1a>
     9d6:	c002                	sw	zero,0(sp)
     9d8:	c202                	sw	zero,4(sp)
     9da:	00011423          	sh	zero,8(sp)
     9de:	c602                	sw	zero,12(sp)
     9e0:	c802                	sw	zero,16(sp)
     9e2:	ca02                	sw	zero,20(sp)
     9e4:	cc02                	sw	zero,24(sp)
     9e6:	491000ef          	jal	ra,1676 <RCC_APB2PeriphClockCmd>
     9ea:	200007b7          	lui	a5,0x20000
     9ee:	0147a503          	lw	a0,20(a5) # 20000014 <SystemCoreClock>
     9f2:	000f45b7          	lui	a1,0xf4
     9f6:	24058593          	addi	a1,a1,576 # f4240 <_data_lma+0xf19f0>
     9fa:	edaff0ef          	jal	ra,d4 <__udivsi3>
     9fe:	40013437          	lui	s0,0x40013
     a02:	157d                	addi	a0,a0,-1
     a04:	8068                	sh	a0,0(sp)
     a06:	3e700793          	li	a5,999
     a0a:	858a                	mv	a1,sp
     a0c:	c0040513          	addi	a0,s0,-1024 # 40012c00 <__global_pointer$+0x200123c0>
     a10:	c23e                	sw	a5,4(sp)
     a12:	00011123          	sh	zero,2(sp)
     a16:	49d000ef          	jal	ra,16b2 <TIM_TimeBaseInit>
     a1a:	67c1                	lui	a5,0x10
     a1c:	06078793          	addi	a5,a5,96 # 10060 <_data_lma+0xd810>
     a20:	006c                	addi	a1,sp,12
     a22:	c0040513          	addi	a0,s0,-1024
     a26:	c63e                	sw	a5,12(sp)
     a28:	00011923          	sh	zero,18(sp)
     a2c:	00011a23          	sh	zero,20(sp)
     a30:	4d1000ef          	jal	ra,1700 <TIM_OC1Init>
     a34:	c0040513          	addi	a0,s0,-1024
     a38:	45a1                	li	a1,8
     a3a:	58b000ef          	jal	ra,17c4 <TIM_OC1PreloadConfig>
     a3e:	c0040513          	addi	a0,s0,-1024
     a42:	4585                	li	a1,1
     a44:	567000ef          	jal	ra,17aa <TIM_ARRPreloadConfig>
     a48:	c0040513          	addi	a0,s0,-1024
     a4c:	4585                	li	a1,1
     a4e:	51d000ef          	jal	ra,176a <TIM_Cmd>
     a52:	4585                	li	a1,1
     a54:	c0040513          	addi	a0,s0,-1024
     a58:	52b000ef          	jal	ra,1782 <TIM_CtrlPWMOutputs>
     a5c:	0171                	addi	sp,sp,28
     a5e:	e4cff06f          	j	aa <__riscv_restore_0>

00000a62 <PWM_Set_Brightness>:
     a62:	e3eff2ef          	jal	t0,a0 <__riscv_save_0>
     a66:	3e800793          	li	a5,1000
     a6a:	3e800413          	li	s0,1000
     a6e:	00a7e363          	bltu	a5,a0,a74 <PWM_Set_Brightness+0x12>
     a72:	842a                	mv	s0,a0
     a74:	01041593          	slli	a1,s0,0x10
     a78:	40013537          	lui	a0,0x40013
     a7c:	81c1                	srli	a1,a1,0x10
     a7e:	c0050513          	addi	a0,a0,-1024 # 40012c00 <__global_pointer$+0x200123c0>
     a82:	551000ef          	jal	ra,17d2 <TIM_SetCompare1>
     a86:	80819e23          	sh	s0,-2020(gp) # 2000005c <pwm_control>
     a8a:	e20ff06f          	j	aa <__riscv_restore_0>

00000a8e <PWM_Config_Init>:
     a8e:	e12ff2ef          	jal	t0,a0 <__riscv_save_0>
     a92:	81c18793          	addi	a5,gp,-2020 # 2000005c <pwm_control>
     a96:	01f40737          	lui	a4,0x1f40
     a9a:	c398                	sw	a4,0(a5)
     a9c:	6705                	lui	a4,0x1
     a9e:	a0070713          	addi	a4,a4,-1536 # a00 <PWM_Timer_Config+0x38>
     aa2:	a3da                	sh	a4,4(a5)
     aa4:	35fd                	jal	992 <PWM_GPIO_Config>
     aa6:	370d                	jal	9c8 <PWM_Timer_Config>
     aa8:	4501                	li	a0,0
     aaa:	3f65                	jal	a62 <PWM_Set_Brightness>
     aac:	dfeff06f          	j	aa <__riscv_restore_0>

00000ab0 <PWM_Update_Fade>:
     ab0:	81c18793          	addi	a5,gp,-2020 # 2000005c <pwm_control>
     ab4:	23d8                	lbu	a4,4(a5)
     ab6:	c729                	beqz	a4,b00 <PWM_Update_Fade+0x50>
     ab8:	de8ff2ef          	jal	t0,a0 <__riscv_save_0>
     abc:	238a                	lhu	a0,0(a5)
     abe:	23ba                	lhu	a4,2(a5)
     ac0:	00e57e63          	bgeu	a0,a4,adc <PWM_Update_Fade+0x2c>
     ac4:	33d4                	lbu	a3,5(a5)
     ac6:	9536                	add	a0,a0,a3
     ac8:	0542                	slli	a0,a0,0x10
     aca:	8141                	srli	a0,a0,0x10
     acc:	00e56563          	bltu	a0,a4,ad6 <PWM_Update_Fade+0x26>
     ad0:	00078223          	sb	zero,4(a5)
     ad4:	853a                	mv	a0,a4
     ad6:	3771                	jal	a62 <PWM_Set_Brightness>
     ad8:	dd2ff06f          	j	aa <__riscv_restore_0>
     adc:	00a77f63          	bgeu	a4,a0,afa <PWM_Update_Fade+0x4a>
     ae0:	81c18693          	addi	a3,gp,-2020 # 2000005c <pwm_control>
     ae4:	32dc                	lbu	a5,5(a3)
     ae6:	00f56763          	bltu	a0,a5,af4 <PWM_Update_Fade+0x44>
     aea:	8d1d                	sub	a0,a0,a5
     aec:	0542                	slli	a0,a0,0x10
     aee:	8141                	srli	a0,a0,0x10
     af0:	fea763e3          	bltu	a4,a0,ad6 <PWM_Update_Fade+0x26>
     af4:	00068223          	sb	zero,4(a3)
     af8:	bff1                	j	ad4 <PWM_Update_Fade+0x24>
     afa:	00078223          	sb	zero,4(a5)
     afe:	bfe9                	j	ad8 <PWM_Update_Fade+0x28>
     b00:	8082                	ret

00000b02 <PWM_Turn_On>:
     b02:	81c18793          	addi	a5,gp,-2020 # 2000005c <pwm_control>
     b06:	1f400713          	li	a4,500
     b0a:	a3ba                	sh	a4,2(a5)
     b0c:	4705                	li	a4,1
     b0e:	a3d8                	sb	a4,4(a5)
     b10:	8082                	ret

00000b12 <PWM_Turn_Off>:
     b12:	81c18793          	addi	a5,gp,-2020 # 2000005c <pwm_control>
     b16:	4705                	li	a4,1
     b18:	00079123          	sh	zero,2(a5)
     b1c:	a3d8                	sb	a4,4(a5)
     b1e:	8082                	ret

00000b20 <PWM_Get_Brightness>:
     b20:	81c1d503          	lhu	a0,-2020(gp) # 2000005c <pwm_control>
     b24:	8082                	ret

00000b26 <SPI_send_DMA>:
     b26:	400207b7          	lui	a5,0x40020
     b2a:	dfc8                	sw	a0,60(a5)
     b2c:	dbcc                	sw	a1,52(a5)
     b2e:	5b98                	lw	a4,48(a5)
     b30:	400206b7          	lui	a3,0x40020
     b34:	20000593          	li	a1,512
     b38:	00176713          	ori	a4,a4,1
     b3c:	db98                	sw	a4,48(a5)
     b3e:	67c1                	lui	a5,0x10
     b40:	17fd                	addi	a5,a5,-1
     b42:	167d                	addi	a2,a2,-1
     b44:	0642                	slli	a2,a2,0x10
     b46:	8241                	srli	a2,a2,0x10
     b48:	00f61863          	bne	a2,a5,b58 <SPI_send_DMA+0x32>
     b4c:	40020737          	lui	a4,0x40020
     b50:	5b1c                	lw	a5,48(a4)
     b52:	9bf9                	andi	a5,a5,-2
     b54:	db1c                	sw	a5,48(a4)
     b56:	8082                	ret
     b58:	c2cc                	sw	a1,4(a3)
     b5a:	4298                	lw	a4,0(a3)
     b5c:	20077713          	andi	a4,a4,512
     b60:	df6d                	beqz	a4,b5a <SPI_send_DMA+0x34>
     b62:	b7c5                	j	b42 <SPI_send_DMA+0x1c>

00000b64 <SPI_send>:
     b64:	400137b7          	lui	a5,0x40013
     b68:	a7ca                	sh	a0,12(a5)
     b6a:	40013737          	lui	a4,0x40013
     b6e:	271e                	lhu	a5,8(a4)
     b70:	8b89                	andi	a5,a5,2
     b72:	dff5                	beqz	a5,b6e <SPI_send+0xa>
     b74:	8082                	ret

00000b76 <write_command_8>:
     b76:	d2aff2ef          	jal	t0,a0 <__riscv_save_0>
     b7a:	40011737          	lui	a4,0x40011
     b7e:	4b5c                	lw	a5,20(a4)
     b80:	0087e793          	ori	a5,a5,8
     b84:	cb5c                	sw	a5,20(a4)
     b86:	3ff9                	jal	b64 <SPI_send>
     b88:	d22ff06f          	j	aa <__riscv_restore_0>

00000b8c <write_data_16>:
     b8c:	d14ff2ef          	jal	t0,a0 <__riscv_save_0>
     b90:	40011737          	lui	a4,0x40011
     b94:	4b1c                	lw	a5,16(a4)
     b96:	842a                	mv	s0,a0
     b98:	8121                	srli	a0,a0,0x8
     b9a:	0087e793          	ori	a5,a5,8
     b9e:	cb1c                	sw	a5,16(a4)
     ba0:	37d1                	jal	b64 <SPI_send>
     ba2:	0ff47513          	andi	a0,s0,255
     ba6:	3f7d                	jal	b64 <SPI_send>
     ba8:	d02ff06f          	j	aa <__riscv_restore_0>

00000bac <tft_set_window>:
     bac:	cf4ff2ef          	jal	t0,a0 <__riscv_save_0>
     bb0:	1151                	addi	sp,sp,-12
     bb2:	842a                	mv	s0,a0
     bb4:	02a00513          	li	a0,42
     bb8:	c036                	sw	a3,0(sp)
     bba:	c42e                	sw	a1,8(sp)
     bbc:	c232                	sw	a2,4(sp)
     bbe:	3f65                	jal	b76 <write_command_8>
     bc0:	8522                	mv	a0,s0
     bc2:	37e9                	jal	b8c <write_data_16>
     bc4:	4612                	lw	a2,4(sp)
     bc6:	8532                	mv	a0,a2
     bc8:	37d1                	jal	b8c <write_data_16>
     bca:	02b00513          	li	a0,43
     bce:	3765                	jal	b76 <write_command_8>
     bd0:	45a2                	lw	a1,8(sp)
     bd2:	852e                	mv	a0,a1
     bd4:	3f65                	jal	b8c <write_data_16>
     bd6:	4682                	lw	a3,0(sp)
     bd8:	8536                	mv	a0,a3
     bda:	3f4d                	jal	b8c <write_data_16>
     bdc:	02c00513          	li	a0,44
     be0:	3f59                	jal	b76 <write_command_8>
     be2:	0131                	addi	sp,sp,12
     be4:	cc6ff06f          	j	aa <__riscv_restore_0>

00000be8 <tft_init>:
     be8:	cb8ff2ef          	jal	t0,a0 <__riscv_save_0>
     bec:	400216b7          	lui	a3,0x40021
     bf0:	4e9c                	lw	a5,24(a3)
     bf2:	6705                	lui	a4,0x1
     bf4:	0741                	addi	a4,a4,16
     bf6:	8fd9                	or	a5,a5,a4
     bf8:	40011437          	lui	s0,0x40011
     bfc:	ce9c                	sw	a5,24(a3)
     bfe:	401c                	lw	a5,0(s0)
     c00:	777d                	lui	a4,0xfffff
     c02:	0ff70713          	addi	a4,a4,255 # fffff0ff <__global_pointer$+0xdfffe8bf>
     c06:	8ff9                	and	a5,a5,a4
     c08:	c01c                	sw	a5,0(s0)
     c0a:	401c                	lw	a5,0(s0)
     c0c:	7745                	lui	a4,0xffff1
     c0e:	177d                	addi	a4,a4,-1
     c10:	3007e793          	ori	a5,a5,768
     c14:	c01c                	sw	a5,0(s0)
     c16:	401c                	lw	a5,0(s0)
     c18:	fff10637          	lui	a2,0xfff10
     c1c:	167d                	addi	a2,a2,-1
     c1e:	8ff9                	and	a5,a5,a4
     c20:	c01c                	sw	a5,0(s0)
     c22:	401c                	lw	a5,0(s0)
     c24:	670d                	lui	a4,0x3
     c26:	1101                	addi	sp,sp,-32
     c28:	8fd9                	or	a5,a5,a4
     c2a:	c01c                	sw	a5,0(s0)
     c2c:	401c                	lw	a5,0(s0)
     c2e:	0b070713          	addi	a4,a4,176 # 30b0 <_data_lma+0x860>
     c32:	03200513          	li	a0,50
     c36:	8ff1                	and	a5,a5,a2
     c38:	c01c                	sw	a5,0(s0)
     c3a:	401c                	lw	a5,0(s0)
     c3c:	00030637          	lui	a2,0x30
     c40:	8fd1                	or	a5,a5,a2
     c42:	c01c                	sw	a5,0(s0)
     c44:	401c                	lw	a5,0(s0)
     c46:	ff100637          	lui	a2,0xff100
     c4a:	167d                	addi	a2,a2,-1
     c4c:	8ff1                	and	a5,a5,a2
     c4e:	c01c                	sw	a5,0(s0)
     c50:	401c                	lw	a5,0(s0)
     c52:	00b00637          	lui	a2,0xb00
     c56:	8fd1                	or	a5,a5,a2
     c58:	c01c                	sw	a5,0(s0)
     c5a:	401c                	lw	a5,0(s0)
     c5c:	f1000637          	lui	a2,0xf1000
     c60:	167d                	addi	a2,a2,-1
     c62:	8ff1                	and	a5,a5,a2
     c64:	c01c                	sw	a5,0(s0)
     c66:	401c                	lw	a5,0(s0)
     c68:	0b000637          	lui	a2,0xb000
     c6c:	8fd1                	or	a5,a5,a2
     c6e:	7671                	lui	a2,0xffffc
     c70:	c01c                	sw	a5,0(s0)
     c72:	30460613          	addi	a2,a2,772 # ffffc304 <__global_pointer$+0xdfffbac4>
     c76:	400137b7          	lui	a5,0x40013
     c7a:	a392                	sh	a2,0(a5)
     c7c:	461d                	li	a2,7
     c7e:	ab92                	sh	a2,16(a5)
     c80:	23d2                	lhu	a2,4(a5)
     c82:	07b1                	addi	a5,a5,12
     c84:	00266613          	ori	a2,a2,2
     c88:	fec79c23          	sh	a2,-8(a5) # 40012ff8 <__global_pointer$+0x200127b8>
     c8c:	ff47d603          	lhu	a2,-12(a5)
     c90:	04066613          	ori	a2,a2,64
     c94:	fec79a23          	sh	a2,-12(a5)
     c98:	4ad0                	lw	a2,20(a3)
     c9a:	00166613          	ori	a2,a2,1
     c9e:	cad0                	sw	a2,20(a3)
     ca0:	400206b7          	lui	a3,0x40020
     ca4:	da98                	sw	a4,48(a3)
     ca6:	de9c                	sw	a5,56(a3)
     ca8:	485c                	lw	a5,20(s0)
     caa:	0047e793          	ori	a5,a5,4
     cae:	c85c                	sw	a5,20(s0)
     cb0:	475000ef          	jal	ra,1924 <Delay_Ms>
     cb4:	481c                	lw	a5,16(s0)
     cb6:	03200513          	li	a0,50
     cba:	0047e793          	ori	a5,a5,4
     cbe:	c81c                	sw	a5,16(s0)
     cc0:	465000ef          	jal	ra,1924 <Delay_Ms>
     cc4:	485c                	lw	a5,20(s0)
     cc6:	4545                	li	a0,17
     cc8:	0107e793          	ori	a5,a5,16
     ccc:	c85c                	sw	a5,20(s0)
     cce:	3565                	jal	b76 <write_command_8>
     cd0:	07800513          	li	a0,120
     cd4:	451000ef          	jal	ra,1924 <Delay_Ms>
     cd8:	03600513          	li	a0,54
     cdc:	3d69                	jal	b76 <write_command_8>
     cde:	481c                	lw	a5,16(s0)
     ce0:	0a800513          	li	a0,168
     ce4:	0087e793          	ori	a5,a5,8
     ce8:	c81c                	sw	a5,16(s0)
     cea:	3dad                	jal	b64 <SPI_send>
     cec:	03a00513          	li	a0,58
     cf0:	3559                	jal	b76 <write_command_8>
     cf2:	481c                	lw	a5,16(s0)
     cf4:	4515                	li	a0,5
     cf6:	0087e793          	ori	a5,a5,8
     cfa:	c81c                	sw	a5,16(s0)
     cfc:	35a5                	jal	b64 <SPI_send>
     cfe:	6589                	lui	a1,0x2
     d00:	11c58493          	addi	s1,a1,284 # 211c <memcpy+0xd4>
     d04:	4641                	li	a2,16
     d06:	11c58593          	addi	a1,a1,284
     d0a:	850a                	mv	a0,sp
     d0c:	33c010ef          	jal	ra,2048 <memcpy>
     d10:	0e000513          	li	a0,224
     d14:	358d                	jal	b76 <write_command_8>
     d16:	481c                	lw	a5,16(s0)
     d18:	850a                	mv	a0,sp
     d1a:	4605                	li	a2,1
     d1c:	0087e793          	ori	a5,a5,8
     d20:	c81c                	sw	a5,16(s0)
     d22:	45c1                	li	a1,16
     d24:	3509                	jal	b26 <SPI_send_DMA>
     d26:	01048593          	addi	a1,s1,16
     d2a:	4641                	li	a2,16
     d2c:	0808                	addi	a0,sp,16
     d2e:	31a010ef          	jal	ra,2048 <memcpy>
     d32:	0e100513          	li	a0,225
     d36:	3581                	jal	b76 <write_command_8>
     d38:	481c                	lw	a5,16(s0)
     d3a:	4605                	li	a2,1
     d3c:	45c1                	li	a1,16
     d3e:	0087e793          	ori	a5,a5,8
     d42:	c81c                	sw	a5,16(s0)
     d44:	0808                	addi	a0,sp,16
     d46:	33c5                	jal	b26 <SPI_send_DMA>
     d48:	4529                	li	a0,10
     d4a:	3db000ef          	jal	ra,1924 <Delay_Ms>
     d4e:	02100513          	li	a0,33
     d52:	3515                	jal	b76 <write_command_8>
     d54:	454d                	li	a0,19
     d56:	3505                	jal	b76 <write_command_8>
     d58:	4529                	li	a0,10
     d5a:	3cb000ef          	jal	ra,1924 <Delay_Ms>
     d5e:	02900513          	li	a0,41
     d62:	3d11                	jal	b76 <write_command_8>
     d64:	4529                	li	a0,10
     d66:	3bf000ef          	jal	ra,1924 <Delay_Ms>
     d6a:	481c                	lw	a5,16(s0)
     d6c:	0107e793          	ori	a5,a5,16
     d70:	c81c                	sw	a5,16(s0)
     d72:	6105                	addi	sp,sp,32
     d74:	b36ff06f          	j	aa <__riscv_restore_0>

00000d78 <tft_set_cursor>:
     d78:	0505                	addi	a0,a0,1
     d7a:	96a19223          	sh	a0,-1692(gp) # 200001a4 <_cursor_x>
     d7e:	05e9                	addi	a1,a1,26
     d80:	96b19323          	sh	a1,-1690(gp) # 200001a6 <_cursor_y>
     d84:	8082                	ret

00000d86 <tft_set_color>:
     d86:	200007b7          	lui	a5,0x20000
     d8a:	00a79023          	sh	a0,0(a5) # 20000000 <_highcode_vma_end>
     d8e:	8082                	ret

00000d90 <tft_set_background_color>:
     d90:	82a19123          	sh	a0,-2014(gp) # 20000062 <_bg_color>
     d94:	8082                	ret

00000d96 <tft_print_char>:
     d96:	b0aff2ef          	jal	t0,a0 <__riscv_save_0>
     d9a:	00251793          	slli	a5,a0,0x2
     d9e:	953e                	add	a0,a0,a5
     da0:	8221d783          	lhu	a5,-2014(gp) # 20000062 <_bg_color>
     da4:	1131                	addi	sp,sp,-20
     da6:	0087d713          	srli	a4,a5,0x8
     daa:	0ff7f793          	andi	a5,a5,255
     dae:	c63e                	sw	a5,12(sp)
     db0:	200007b7          	lui	a5,0x20000
     db4:	0007d783          	lhu	a5,0(a5) # 20000000 <_highcode_vma_end>
     db8:	c43a                	sw	a4,8(sp)
     dba:	4281                	li	t0,0
     dbc:	0087d713          	srli	a4,a5,0x8
     dc0:	0ff7f793          	andi	a5,a5,255
     dc4:	c23e                	sw	a5,4(sp)
     dc6:	6789                	lui	a5,0x2
     dc8:	34878793          	addi	a5,a5,840 # 2348 <font>
     dcc:	c03a                	sw	a4,0(sp)
     dce:	4681                	li	a3,0
     dd0:	c83e                	sw	a5,16(sp)
     dd2:	82418313          	addi	t1,gp,-2012 # 20000064 <_buffer>
     dd6:	4785                	li	a5,1
     dd8:	005790b3          	sll	ra,a5,t0
     ddc:	85b6                	mv	a1,a3
     dde:	4601                	li	a2,0
     de0:	44c2                	lw	s1,16(sp)
     de2:	00c503b3          	add	t2,a0,a2
     de6:	872e                	mv	a4,a1
     de8:	93a6                	add	t2,t2,s1
     dea:	0003c383          	lbu	t2,0(t2)
     dee:	00158793          	addi	a5,a1,1
     df2:	0589                	addi	a1,a1,2
     df4:	07c2                	slli	a5,a5,0x10
     df6:	05c2                	slli	a1,a1,0x10
     df8:	0013f3b3          	and	t2,t2,ra
     dfc:	83c1                	srli	a5,a5,0x10
     dfe:	81c1                	srli	a1,a1,0x10
     e00:	971a                	add	a4,a4,t1
     e02:	06038763          	beqz	t2,e70 <tft_print_char+0xda>
     e06:	4482                	lw	s1,0(sp)
     e08:	979a                	add	a5,a5,t1
     e0a:	a304                	sb	s1,0(a4)
     e0c:	4712                	lw	a4,4(sp)
     e0e:	a398                	sb	a4,0(a5)
     e10:	0605                	addi	a2,a2,1
     e12:	4795                	li	a5,5
     e14:	fcf616e3          	bne	a2,a5,de0 <tft_print_char+0x4a>
     e18:	06a9                	addi	a3,a3,10
     e1a:	06c2                	slli	a3,a3,0x10
     e1c:	82c1                	srli	a3,a3,0x10
     e1e:	04600793          	li	a5,70
     e22:	0285                	addi	t0,t0,1
     e24:	faf699e3          	bne	a3,a5,dd6 <tft_print_char+0x40>
     e28:	400114b7          	lui	s1,0x40011
     e2c:	48dc                	lw	a5,20(s1)
     e2e:	0107e793          	ori	a5,a5,16
     e32:	c8dc                	sw	a5,20(s1)
     e34:	9641d503          	lhu	a0,-1692(gp) # 200001a4 <_cursor_x>
     e38:	9661d583          	lhu	a1,-1690(gp) # 200001a6 <_cursor_y>
     e3c:	00450613          	addi	a2,a0,4
     e40:	0642                	slli	a2,a2,0x10
     e42:	00658693          	addi	a3,a1,6
     e46:	06c2                	slli	a3,a3,0x10
     e48:	82c1                	srli	a3,a3,0x10
     e4a:	8241                	srli	a2,a2,0x10
     e4c:	3385                	jal	bac <tft_set_window>
     e4e:	489c                	lw	a5,16(s1)
     e50:	4605                	li	a2,1
     e52:	04600593          	li	a1,70
     e56:	0087e793          	ori	a5,a5,8
     e5a:	c89c                	sw	a5,16(s1)
     e5c:	82418513          	addi	a0,gp,-2012 # 20000064 <_buffer>
     e60:	31d9                	jal	b26 <SPI_send_DMA>
     e62:	489c                	lw	a5,16(s1)
     e64:	0107e793          	ori	a5,a5,16
     e68:	c89c                	sw	a5,16(s1)
     e6a:	0151                	addi	sp,sp,20
     e6c:	a3eff06f          	j	aa <__riscv_restore_0>
     e70:	44a2                	lw	s1,8(sp)
     e72:	979a                	add	a5,a5,t1
     e74:	a304                	sb	s1,0(a4)
     e76:	4732                	lw	a4,12(sp)
     e78:	bf59                	j	e0e <tft_print_char+0x78>

00000e7a <tft_print>:
     e7a:	a26ff2ef          	jal	t0,a0 <__riscv_save_0>
     e7e:	842a                	mv	s0,a0
     e80:	00040503          	lb	a0,0(s0) # 40011000 <__global_pointer$+0x200107c0>
     e84:	e119                	bnez	a0,e8a <tft_print+0x10>
     e86:	a24ff06f          	j	aa <__riscv_restore_0>
     e8a:	3731                	jal	d96 <tft_print_char>
     e8c:	96418713          	addi	a4,gp,-1692 # 200001a4 <_cursor_x>
     e90:	231e                	lhu	a5,0(a4)
     e92:	0405                	addi	s0,s0,1
     e94:	0799                	addi	a5,a5,6
     e96:	a31e                	sh	a5,0(a4)
     e98:	b7e5                	j	e80 <tft_print+0x6>

00000e9a <tft_print_number>:
     e9a:	a06ff2ef          	jal	t0,a0 <__riscv_save_0>
     e9e:	1141                	addi	sp,sp,-16
     ea0:	87aa                	mv	a5,a0
     ea2:	86ae                	mv	a3,a1
     ea4:	4701                	li	a4,0
     ea6:	00055563          	bgez	a0,eb0 <tft_print_number+0x16>
     eaa:	40a007b3          	neg	a5,a0
     eae:	4705                	li	a4,1
     eb0:	96818613          	addi	a2,gp,-1688 # 200001a8 <str.4169>
     eb4:	000605a3          	sb	zero,11(a2)
     eb8:	442d                	li	s0,11
     eba:	96818493          	addi	s1,gp,-1688 # 200001a8 <str.4169>
     ebe:	eba9                	bnez	a5,f10 <tft_print_number+0x76>
     ec0:	47ad                	li	a5,11
     ec2:	00f41663          	bne	s0,a5,ece <tft_print_number+0x34>
     ec6:	03000793          	li	a5,48
     eca:	a4bc                	sb	a5,10(s1)
     ecc:	4429                	li	s0,10
     ece:	cb09                	beqz	a4,ee0 <tft_print_number+0x46>
     ed0:	147d                	addi	s0,s0,-1
     ed2:	0ff47413          	andi	s0,s0,255
     ed6:	008487b3          	add	a5,s1,s0
     eda:	02d00713          	li	a4,45
     ede:	a398                	sb	a4,0(a5)
     ee0:	472d                	li	a4,11
     ee2:	8f01                	sub	a4,a4,s0
     ee4:	00171793          	slli	a5,a4,0x1
     ee8:	97ba                	add	a5,a5,a4
     eea:	0786                	slli	a5,a5,0x1
     eec:	17fd                	addi	a5,a5,-1
     eee:	07c2                	slli	a5,a5,0x10
     ef0:	83c1                	srli	a5,a5,0x10
     ef2:	00d7f963          	bgeu	a5,a3,f04 <tft_print_number+0x6a>
     ef6:	96418713          	addi	a4,gp,-1692 # 200001a4 <_cursor_x>
     efa:	2312                	lhu	a2,0(a4)
     efc:	96b2                	add	a3,a3,a2
     efe:	40f687b3          	sub	a5,a3,a5
     f02:	a31e                	sh	a5,0(a4)
     f04:	00848533          	add	a0,s1,s0
     f08:	3f8d                	jal	e7a <tft_print>
     f0a:	0141                	addi	sp,sp,16
     f0c:	99eff06f          	j	aa <__riscv_restore_0>
     f10:	147d                	addi	s0,s0,-1
     f12:	0ff47413          	andi	s0,s0,255
     f16:	00848633          	add	a2,s1,s0
     f1a:	45a9                	li	a1,10
     f1c:	853e                	mv	a0,a5
     f1e:	c636                	sw	a3,12(sp)
     f20:	c43a                	sw	a4,8(sp)
     f22:	c232                	sw	a2,4(sp)
     f24:	c03e                	sw	a5,0(sp)
     f26:	9feff0ef          	jal	ra,124 <__modsi3>
     f2a:	4782                	lw	a5,0(sp)
     f2c:	4612                	lw	a2,4(sp)
     f2e:	03050513          	addi	a0,a0,48
     f32:	45a9                	li	a1,10
     f34:	a208                	sb	a0,0(a2)
     f36:	853e                	mv	a0,a5
     f38:	994ff0ef          	jal	ra,cc <__divsi3>
     f3c:	87aa                	mv	a5,a0
     f3e:	46b2                	lw	a3,12(sp)
     f40:	4722                	lw	a4,8(sp)
     f42:	bfb5                	j	ebe <tft_print_number+0x24>

00000f44 <tft_draw_pixel>:
     f44:	95cff2ef          	jal	t0,a0 <__riscv_save_0>
     f48:	40011437          	lui	s0,0x40011
     f4c:	485c                	lw	a5,20(s0)
     f4e:	84b2                	mv	s1,a2
     f50:	01a58693          	addi	a3,a1,26
     f54:	00150613          	addi	a2,a0,1
     f58:	0642                	slli	a2,a2,0x10
     f5a:	06c2                	slli	a3,a3,0x10
     f5c:	8241                	srli	a2,a2,0x10
     f5e:	82c1                	srli	a3,a3,0x10
     f60:	0107e793          	ori	a5,a5,16
     f64:	c85c                	sw	a5,20(s0)
     f66:	8532                	mv	a0,a2
     f68:	85b6                	mv	a1,a3
     f6a:	c43ff0ef          	jal	ra,bac <tft_set_window>
     f6e:	8526                	mv	a0,s1
     f70:	c1dff0ef          	jal	ra,b8c <write_data_16>
     f74:	481c                	lw	a5,16(s0)
     f76:	0107e793          	ori	a5,a5,16
     f7a:	c81c                	sw	a5,16(s0)
     f7c:	92eff06f          	j	aa <__riscv_restore_0>

00000f80 <tft_fill_rect>:
     f80:	920ff2ef          	jal	t0,a0 <__riscv_save_0>
     f84:	0505                	addi	a0,a0,1
     f86:	05e9                	addi	a1,a1,26
     f88:	0542                	slli	a0,a0,0x10
     f8a:	05c2                	slli	a1,a1,0x10
     f8c:	8336                	mv	t1,a3
     f8e:	1171                	addi	sp,sp,-4
     f90:	8141                	srli	a0,a0,0x10
     f92:	81c1                	srli	a1,a1,0x10
     f94:	00875393          	srli	t2,a4,0x8
     f98:	4781                	li	a5,0
     f9a:	82418693          	addi	a3,gp,-2012 # 20000064 <_buffer>
     f9e:	00179413          	slli	s0,a5,0x1
     fa2:	0442                	slli	s0,s0,0x10
     fa4:	8041                	srli	s0,s0,0x10
     fa6:	04c79763          	bne	a5,a2,ff4 <tft_fill_rect+0x74>
     faa:	400114b7          	lui	s1,0x40011
     fae:	48d8                	lw	a4,20(s1)
     fb0:	fff30693          	addi	a3,t1,-1
     fb4:	fff78613          	addi	a2,a5,-1
     fb8:	96ae                	add	a3,a3,a1
     fba:	962a                	add	a2,a2,a0
     fbc:	01076713          	ori	a4,a4,16
     fc0:	06c2                	slli	a3,a3,0x10
     fc2:	0642                	slli	a2,a2,0x10
     fc4:	c8d8                	sw	a4,20(s1)
     fc6:	82c1                	srli	a3,a3,0x10
     fc8:	8241                	srli	a2,a2,0x10
     fca:	c01a                	sw	t1,0(sp)
     fcc:	be1ff0ef          	jal	ra,bac <tft_set_window>
     fd0:	489c                	lw	a5,16(s1)
     fd2:	4302                	lw	t1,0(sp)
     fd4:	0087e793          	ori	a5,a5,8
     fd8:	c89c                	sw	a5,16(s1)
     fda:	861a                	mv	a2,t1
     fdc:	85a2                	mv	a1,s0
     fde:	82418513          	addi	a0,gp,-2012 # 20000064 <_buffer>
     fe2:	b45ff0ef          	jal	ra,b26 <SPI_send_DMA>
     fe6:	489c                	lw	a5,16(s1)
     fe8:	0107e793          	ori	a5,a5,16
     fec:	c89c                	sw	a5,16(s1)
     fee:	0111                	addi	sp,sp,4
     ff0:	8baff06f          	j	aa <__riscv_restore_0>
     ff4:	008684b3          	add	s1,a3,s0
     ff8:	0405                	addi	s0,s0,1
     ffa:	0442                	slli	s0,s0,0x10
     ffc:	8041                	srli	s0,s0,0x10
     ffe:	0785                	addi	a5,a5,1
    1000:	00748023          	sb	t2,0(s1) # 40011000 <__global_pointer$+0x200107c0>
    1004:	9436                	add	s0,s0,a3
    1006:	07c2                	slli	a5,a5,0x10
    1008:	a018                	sb	a4,0(s0)
    100a:	83c1                	srli	a5,a5,0x10
    100c:	bf49                	j	f9e <tft_fill_rect+0x1e>

0000100e <SystemInit>:
    100e:	892ff2ef          	jal	t0,a0 <__riscv_save_0>
    1012:	40021437          	lui	s0,0x40021
    1016:	401c                	lw	a5,0(s0)
    1018:	f8ff0737          	lui	a4,0xf8ff0
    101c:	1161                	addi	sp,sp,-8
    101e:	0017e793          	ori	a5,a5,1
    1022:	c01c                	sw	a5,0(s0)
    1024:	405c                	lw	a5,4(s0)
    1026:	4541                	li	a0,16
    1028:	8ff9                	and	a5,a5,a4
    102a:	c05c                	sw	a5,4(s0)
    102c:	401c                	lw	a5,0(s0)
    102e:	fef70737          	lui	a4,0xfef70
    1032:	177d                	addi	a4,a4,-1
    1034:	8ff9                	and	a5,a5,a4
    1036:	c01c                	sw	a5,0(s0)
    1038:	401c                	lw	a5,0(s0)
    103a:	fffc0737          	lui	a4,0xfffc0
    103e:	177d                	addi	a4,a4,-1
    1040:	8ff9                	and	a5,a5,a4
    1042:	c01c                	sw	a5,0(s0)
    1044:	405c                	lw	a5,4(s0)
    1046:	7741                	lui	a4,0xffff0
    1048:	177d                	addi	a4,a4,-1
    104a:	8ff9                	and	a5,a5,a4
    104c:	c05c                	sw	a5,4(s0)
    104e:	009f07b7          	lui	a5,0x9f0
    1052:	c41c                	sw	a5,8(s0)
    1054:	23bd                	jal	15c2 <RCC_AdjustHSICalibrationValue>
    1056:	4c1c                	lw	a5,24(s0)
    1058:	00020637          	lui	a2,0x20
    105c:	0207e793          	ori	a5,a5,32
    1060:	cc1c                	sw	a5,24(s0)
    1062:	400117b7          	lui	a5,0x40011
    1066:	4007a703          	lw	a4,1024(a5) # 40011400 <__global_pointer$+0x20010bc0>
    106a:	40078693          	addi	a3,a5,1024
    106e:	f0f77713          	andi	a4,a4,-241
    1072:	40e7a023          	sw	a4,1024(a5)
    1076:	4007a703          	lw	a4,1024(a5)
    107a:	08076713          	ori	a4,a4,128
    107e:	40e7a023          	sw	a4,1024(a5)
    1082:	4789                	li	a5,2
    1084:	ca9c                	sw	a5,16(a3)
    1086:	c002                	sw	zero,0(sp)
    1088:	c202                	sw	zero,4(sp)
    108a:	4c1c                	lw	a5,24(s0)
    108c:	40010737          	lui	a4,0x40010
    1090:	66a1                	lui	a3,0x8
    1092:	0017e793          	ori	a5,a5,1
    1096:	cc1c                	sw	a5,24(s0)
    1098:	435c                	lw	a5,4(a4)
    109a:	8fd5                	or	a5,a5,a3
    109c:	c35c                	sw	a5,4(a4)
    109e:	401c                	lw	a5,0(s0)
    10a0:	6741                	lui	a4,0x10
    10a2:	400216b7          	lui	a3,0x40021
    10a6:	8fd9                	or	a5,a5,a4
    10a8:	c01c                	sw	a5,0(s0)
    10aa:	6709                	lui	a4,0x2
    10ac:	429c                	lw	a5,0(a3)
    10ae:	8ff1                	and	a5,a5,a2
    10b0:	c23e                	sw	a5,4(sp)
    10b2:	4782                	lw	a5,0(sp)
    10b4:	0785                	addi	a5,a5,1
    10b6:	c03e                	sw	a5,0(sp)
    10b8:	4792                	lw	a5,4(sp)
    10ba:	e781                	bnez	a5,10c2 <SystemInit+0xb4>
    10bc:	4782                	lw	a5,0(sp)
    10be:	fee797e3          	bne	a5,a4,10ac <SystemInit+0x9e>
    10c2:	400217b7          	lui	a5,0x40021
    10c6:	439c                	lw	a5,0(a5)
    10c8:	00e79713          	slli	a4,a5,0xe
    10cc:	06075963          	bgez	a4,113e <SystemInit+0x130>
    10d0:	4785                	li	a5,1
    10d2:	c23e                	sw	a5,4(sp)
    10d4:	4712                	lw	a4,4(sp)
    10d6:	4785                	li	a5,1
    10d8:	06f71063          	bne	a4,a5,1138 <SystemInit+0x12a>
    10dc:	400227b7          	lui	a5,0x40022
    10e0:	4398                	lw	a4,0(a5)
    10e2:	76c1                	lui	a3,0xffff0
    10e4:	16fd                	addi	a3,a3,-1
    10e6:	9b71                	andi	a4,a4,-4
    10e8:	c398                	sw	a4,0(a5)
    10ea:	4398                	lw	a4,0(a5)
    10ec:	00176713          	ori	a4,a4,1
    10f0:	c398                	sw	a4,0(a5)
    10f2:	400217b7          	lui	a5,0x40021
    10f6:	43d8                	lw	a4,4(a5)
    10f8:	c3d8                	sw	a4,4(a5)
    10fa:	43d8                	lw	a4,4(a5)
    10fc:	8f75                	and	a4,a4,a3
    10fe:	c3d8                	sw	a4,4(a5)
    1100:	43d8                	lw	a4,4(a5)
    1102:	66c1                	lui	a3,0x10
    1104:	8f55                	or	a4,a4,a3
    1106:	c3d8                	sw	a4,4(a5)
    1108:	4398                	lw	a4,0(a5)
    110a:	010006b7          	lui	a3,0x1000
    110e:	8f55                	or	a4,a4,a3
    1110:	c398                	sw	a4,0(a5)
    1112:	4398                	lw	a4,0(a5)
    1114:	00671693          	slli	a3,a4,0x6
    1118:	fe06dde3          	bgez	a3,1112 <SystemInit+0x104>
    111c:	43d8                	lw	a4,4(a5)
    111e:	400216b7          	lui	a3,0x40021
    1122:	9b71                	andi	a4,a4,-4
    1124:	c3d8                	sw	a4,4(a5)
    1126:	43d8                	lw	a4,4(a5)
    1128:	00276713          	ori	a4,a4,2
    112c:	c3d8                	sw	a4,4(a5)
    112e:	4721                	li	a4,8
    1130:	42dc                	lw	a5,4(a3)
    1132:	8bb1                	andi	a5,a5,12
    1134:	fee79ee3          	bne	a5,a4,1130 <SystemInit+0x122>
    1138:	0121                	addi	sp,sp,8
    113a:	f71fe06f          	j	aa <__riscv_restore_0>
    113e:	c202                	sw	zero,4(sp)
    1140:	bf51                	j	10d4 <SystemInit+0xc6>

00001142 <SystemCoreClockUpdate>:
    1142:	f5ffe2ef          	jal	t0,a0 <__riscv_save_0>
    1146:	40021737          	lui	a4,0x40021
    114a:	435c                	lw	a5,4(a4)
    114c:	20000437          	lui	s0,0x20000
    1150:	4691                	li	a3,4
    1152:	8bb1                	andi	a5,a5,12
    1154:	01440413          	addi	s0,s0,20 # 20000014 <SystemCoreClock>
    1158:	00d78563          	beq	a5,a3,1162 <SystemCoreClockUpdate+0x20>
    115c:	46a1                	li	a3,8
    115e:	04d78263          	beq	a5,a3,11a2 <SystemCoreClockUpdate+0x60>
    1162:	016e37b7          	lui	a5,0x16e3
    1166:	60078793          	addi	a5,a5,1536 # 16e3600 <_data_lma+0x16e0db0>
    116a:	c01c                	sw	a5,0(s0)
    116c:	400216b7          	lui	a3,0x40021
    1170:	42dc                	lw	a5,4(a3)
    1172:	4008                	lw	a0,0(s0)
    1174:	8391                	srli	a5,a5,0x4
    1176:	00f7f713          	andi	a4,a5,15
    117a:	200007b7          	lui	a5,0x20000
    117e:	00478793          	addi	a5,a5,4 # 20000004 <AHBPrescTable>
    1182:	97ba                	add	a5,a5,a4
    1184:	238c                	lbu	a1,0(a5)
    1186:	42dc                	lw	a5,4(a3)
    1188:	0ff5f593          	andi	a1,a1,255
    118c:	0807f793          	andi	a5,a5,128
    1190:	00b55733          	srl	a4,a0,a1
    1194:	e781                	bnez	a5,119c <SystemCoreClockUpdate+0x5a>
    1196:	f3ffe0ef          	jal	ra,d4 <__udivsi3>
    119a:	872a                	mv	a4,a0
    119c:	c018                	sw	a4,0(s0)
    119e:	f0dfe06f          	j	aa <__riscv_restore_0>
    11a2:	435c                	lw	a5,4(a4)
    11a4:	02dc77b7          	lui	a5,0x2dc7
    11a8:	c0078793          	addi	a5,a5,-1024 # 2dc6c00 <_data_lma+0x2dc43b0>
    11ac:	bf7d                	j	116a <SystemCoreClockUpdate+0x28>

000011ae <Touch_Button_GPIO_Config>:
    11ae:	ef3fe2ef          	jal	t0,a0 <__riscv_save_0>
    11b2:	1151                	addi	sp,sp,-12
    11b4:	4585                	li	a1,1
    11b6:	02000513          	li	a0,32
    11ba:	c002                	sw	zero,0(sp)
    11bc:	c202                	sw	zero,4(sp)
    11be:	c402                	sw	zero,8(sp)
    11c0:	295d                	jal	1676 <RCC_APB2PeriphClockCmd>
    11c2:	4785                	li	a5,1
    11c4:	40011537          	lui	a0,0x40011
    11c8:	807c                	sh	a5,0(sp)
    11ca:	858a                	mv	a1,sp
    11cc:	04800793          	li	a5,72
    11d0:	40050513          	addi	a0,a0,1024 # 40011400 <__global_pointer$+0x20010bc0>
    11d4:	c43e                	sw	a5,8(sp)
    11d6:	2cf1                	jal	14b2 <GPIO_Init>
    11d8:	0131                	addi	sp,sp,12
    11da:	ed1fe06f          	j	aa <__riscv_restore_0>

000011de <Touch_Button_Timer_Init>:
    11de:	ec3fe2ef          	jal	t0,a0 <__riscv_save_0>
    11e2:	1131                	addi	sp,sp,-20
    11e4:	4585                	li	a1,1
    11e6:	4505                	li	a0,1
    11e8:	c402                	sw	zero,8(sp)
    11ea:	c602                	sw	zero,12(sp)
    11ec:	00011823          	sh	zero,16(sp)
    11f0:	c002                	sw	zero,0(sp)
    11f2:	c202                	sw	zero,4(sp)
    11f4:	2145                	jal	1694 <RCC_APB1PeriphClockCmd>
    11f6:	02f00793          	li	a5,47
    11fa:	c43e                	sw	a5,8(sp)
    11fc:	002c                	addi	a1,sp,8
    11fe:	3e700793          	li	a5,999
    1202:	40000537          	lui	a0,0x40000
    1206:	c63e                	sw	a5,12(sp)
    1208:	216d                	jal	16b2 <TIM_TimeBaseInit>
    120a:	4605                	li	a2,1
    120c:	4585                	li	a1,1
    120e:	40000537          	lui	a0,0x40000
    1212:	2359                	jal	1798 <TIM_ITConfig>
    1214:	22600793          	li	a5,550
    1218:	807c                	sh	a5,0(sp)
    121a:	850a                	mv	a0,sp
    121c:	4785                	li	a5,1
    121e:	c23e                	sw	a5,4(sp)
    1220:	00010123          	sb	zero,2(sp)
    1224:	2e35                	jal	1560 <NVIC_Init>
    1226:	4585                	li	a1,1
    1228:	40000537          	lui	a0,0x40000
    122c:	2b3d                	jal	176a <TIM_Cmd>
    122e:	0151                	addi	sp,sp,20
    1230:	e7bfe06f          	j	aa <__riscv_restore_0>

00001234 <Touch_Button_EXTI_Config>:
    1234:	e6dfe2ef          	jal	t0,a0 <__riscv_save_0>
    1238:	1121                	addi	sp,sp,-24
    123a:	4585                	li	a1,1
    123c:	4505                	li	a0,1
    123e:	c402                	sw	zero,8(sp)
    1240:	c602                	sw	zero,12(sp)
    1242:	c802                	sw	zero,16(sp)
    1244:	ca02                	sw	zero,20(sp)
    1246:	c002                	sw	zero,0(sp)
    1248:	c202                	sw	zero,4(sp)
    124a:	2135                	jal	1676 <RCC_APB2PeriphClockCmd>
    124c:	4581                	li	a1,0
    124e:	450d                	li	a0,3
    1250:	24e5                	jal	1538 <GPIO_EXTILineConfig>
    1252:	4405                	li	s0,1
    1254:	47c1                	li	a5,16
    1256:	0028                	addi	a0,sp,8
    1258:	c422                	sw	s0,8(sp)
    125a:	c83e                	sw	a5,16(sp)
    125c:	ca22                	sw	s0,20(sp)
    125e:	c602                	sw	zero,12(sp)
    1260:	22c1                	jal	1420 <EXTI_Init>
    1262:	11400793          	li	a5,276
    1266:	850a                	mv	a0,sp
    1268:	807c                	sh	a5,0(sp)
    126a:	8140                	sb	s0,2(sp)
    126c:	c222                	sw	s0,4(sp)
    126e:	2ccd                	jal	1560 <NVIC_Init>
    1270:	0161                	addi	sp,sp,24
    1272:	e39fe06f          	j	aa <__riscv_restore_0>

00001276 <Touch_Button_Init>:
    1276:	e2bfe2ef          	jal	t0,a0 <__riscv_save_0>
    127a:	97818793          	addi	a5,gp,-1672 # 200001b8 <touch_button>
    127e:	00079823          	sh	zero,16(a5)
    1282:	0007a023          	sw	zero,0(a5)
    1286:	0007a223          	sw	zero,4(a5)
    128a:	0007a423          	sw	zero,8(a5)
    128e:	0007a623          	sw	zero,12(a5)
    1292:	00078923          	sb	zero,18(a5)
    1296:	3f21                	jal	11ae <Touch_Button_GPIO_Config>
    1298:	3f71                	jal	1234 <Touch_Button_EXTI_Config>
    129a:	3791                	jal	11de <Touch_Button_Timer_Init>
    129c:	e0ffe06f          	j	aa <__riscv_restore_0>

000012a0 <Touch_Button_Update>:
    12a0:	9741a683          	lw	a3,-1676(gp) # 200001b4 <system_tick_ms>
    12a4:	97818793          	addi	a5,gp,-1672 # 200001b8 <touch_button>
    12a8:	4790                	lw	a2,8(a5)
    12aa:	438c                	lw	a1,0(a5)
    12ac:	4505                	li	a0,1
    12ae:	40c68633          	sub	a2,a3,a2
    12b2:	97818713          	addi	a4,gp,-1672 # 200001b8 <touch_button>
    12b6:	02a58663          	beq	a1,a0,12e2 <Touch_Button_Update+0x42>
    12ba:	c589                	beqz	a1,12c4 <Touch_Button_Update+0x24>
    12bc:	478d                	li	a5,3
    12be:	04f58063          	beq	a1,a5,12fe <Touch_Button_Update+0x5e>
    12c2:	8082                	ret
    12c4:	3b98                	lbu	a4,17(a5)
    12c6:	c729                	beqz	a4,1310 <Touch_Button_Update+0x70>
    12c8:	2bb8                	lbu	a4,18(a5)
    12ca:	e339                	bnez	a4,1310 <Touch_Button_Update+0x70>
    12cc:	47d8                	lw	a4,12(a5)
    12ce:	8e99                	sub	a3,a3,a4
    12d0:	7cf00713          	li	a4,1999
    12d4:	02d77e63          	bgeu	a4,a3,1310 <Touch_Button_Update+0x70>
    12d8:	470d                	li	a4,3
    12da:	000788a3          	sb	zero,17(a5)
    12de:	c3d8                	sw	a4,4(a5)
    12e0:	8082                	ret
    12e2:	3e700713          	li	a4,999
    12e6:	02c77563          	bgeu	a4,a2,1310 <Touch_Button_Update+0x70>
    12ea:	4709                	li	a4,2
    12ec:	c398                	sw	a4,0(a5)
    12ee:	c3d8                	sw	a4,4(a5)
    12f0:	2bb8                	lbu	a4,18(a5)
    12f2:	bb8c                	sb	a1,17(a5)
    12f4:	c7d4                	sw	a3,12(a5)
    12f6:	00173713          	seqz	a4,a4
    12fa:	abb8                	sb	a4,18(a5)
    12fc:	8082                	ret
    12fe:	3e700793          	li	a5,999
    1302:	00c7e563          	bltu	a5,a2,130c <Touch_Button_Update+0x6c>
    1306:	c348                	sw	a0,4(a4)
    1308:	bb08                	sb	a0,17(a4)
    130a:	c754                	sw	a3,12(a4)
    130c:	00072023          	sw	zero,0(a4) # 40021000 <__global_pointer$+0x200207c0>
    1310:	8082                	ret

00001312 <Touch_Button_Get_Event>:
    1312:	97818793          	addi	a5,gp,-1672 # 200001b8 <touch_button>
    1316:	43c8                	lw	a0,4(a5)
    1318:	0007a223          	sw	zero,4(a5)
    131c:	8082                	ret

0000131e <Touch_Button_IRQ_Handler>:
    131e:	d83fe2ef          	jal	t0,a0 <__riscv_save_0>
    1322:	4505                	li	a0,1
    1324:	229d                	jal	148a <EXTI_GetITStatus>
    1326:	c505                	beqz	a0,134e <Touch_Button_IRQ_Handler+0x30>
    1328:	40011537          	lui	a0,0x40011
    132c:	4585                	li	a1,1
    132e:	40050513          	addi	a0,a0,1024 # 40011400 <__global_pointer$+0x20010bc0>
    1332:	9741a403          	lw	s0,-1676(gp) # 200001b4 <system_tick_ms>
    1336:	2ae5                	jal	152e <GPIO_ReadInputDataBit>
    1338:	97818793          	addi	a5,gp,-1672 # 200001b8 <touch_button>
    133c:	4398                	lw	a4,0(a5)
    133e:	c911                	beqz	a0,1352 <Touch_Button_IRQ_Handler+0x34>
    1340:	e709                	bnez	a4,134a <Touch_Button_IRQ_Handler+0x2c>
    1342:	4705                	li	a4,1
    1344:	c398                	sw	a4,0(a5)
    1346:	c780                	sw	s0,8(a5)
    1348:	ab98                	sb	a4,16(a5)
    134a:	4505                	li	a0,1
    134c:	2ab1                	jal	14a8 <EXTI_ClearITPendingBit>
    134e:	d5dfe06f          	j	aa <__riscv_restore_0>
    1352:	177d                	addi	a4,a4,-1
    1354:	4685                	li	a3,1
    1356:	fee6eae3          	bltu	a3,a4,134a <Touch_Button_IRQ_Handler+0x2c>
    135a:	470d                	li	a4,3
    135c:	c398                	sw	a4,0(a5)
    135e:	00078823          	sb	zero,16(a5)
    1362:	b7e5                	j	134a <Touch_Button_IRQ_Handler+0x2c>

00001364 <ADC1_IRQHandler>:
    1364:	a001                	j	1364 <ADC1_IRQHandler>

00001366 <handle_reset>:
    1366:	1ffff197          	auipc	gp,0x1ffff
    136a:	4da18193          	addi	gp,gp,1242 # 20000840 <__global_pointer$>
    136e:	fc018113          	addi	sp,gp,-64 # 20000800 <_eusrstack>
    1372:	0a000513          	li	a0,160
    1376:	1ffff597          	auipc	a1,0x1ffff
    137a:	c8a58593          	addi	a1,a1,-886 # 20000000 <_highcode_vma_end>
    137e:	1ffff617          	auipc	a2,0x1ffff
    1382:	c8260613          	addi	a2,a2,-894 # 20000000 <_highcode_vma_end>
    1386:	00c5fa63          	bgeu	a1,a2,139a <handle_reset+0x34>
    138a:	00052283          	lw	t0,0(a0)
    138e:	0055a023          	sw	t0,0(a1)
    1392:	0511                	addi	a0,a0,4
    1394:	0591                	addi	a1,a1,4
    1396:	fec5eae3          	bltu	a1,a2,138a <handle_reset+0x24>
    139a:	00001517          	auipc	a0,0x1
    139e:	4b650513          	addi	a0,a0,1206 # 2850 <_data_lma>
    13a2:	1ffff597          	auipc	a1,0x1ffff
    13a6:	c5e58593          	addi	a1,a1,-930 # 20000000 <_highcode_vma_end>
    13aa:	1ffff617          	auipc	a2,0x1ffff
    13ae:	c9660613          	addi	a2,a2,-874 # 20000040 <_edata>
    13b2:	00c5fa63          	bgeu	a1,a2,13c6 <handle_reset+0x60>
    13b6:	00052283          	lw	t0,0(a0)
    13ba:	0055a023          	sw	t0,0(a1)
    13be:	0511                	addi	a0,a0,4
    13c0:	0591                	addi	a1,a1,4
    13c2:	fec5eae3          	bltu	a1,a2,13b6 <handle_reset+0x50>
    13c6:	1ffff517          	auipc	a0,0x1ffff
    13ca:	c7a50513          	addi	a0,a0,-902 # 20000040 <_edata>
    13ce:	99418593          	addi	a1,gp,-1644 # 200001d4 <_ebss>
    13d2:	00b57763          	bgeu	a0,a1,13e0 <handle_reset+0x7a>
    13d6:	00052023          	sw	zero,0(a0)
    13da:	0511                	addi	a0,a0,4
    13dc:	feb56de3          	bltu	a0,a1,13d6 <handle_reset+0x70>
    13e0:	000022b7          	lui	t0,0x2
    13e4:	88028293          	addi	t0,t0,-1920 # 1880 <USART_Init+0x86>
    13e8:	30029073          	csrw	mstatus,t0
    13ec:	428d                	li	t0,3
    13ee:	80429073          	csrw	0x804,t0
    13f2:	fffff297          	auipc	t0,0xfffff
    13f6:	c0e28293          	addi	t0,t0,-1010 # 0 <_sinit>
    13fa:	0032e293          	ori	t0,t0,3
    13fe:	30529073          	csrw	mtvec,t0
    1402:	c0dff0ef          	jal	ra,100e <SystemInit>
    1406:	fffff297          	auipc	t0,0xfffff
    140a:	49028293          	addi	t0,t0,1168 # 896 <main>
    140e:	34129073          	csrw	mepc,t0
    1412:	30200073          	mret

00001416 <DBGMCU_GetCHIPID>:
    1416:	1ffff7b7          	lui	a5,0x1ffff
    141a:	7c47a503          	lw	a0,1988(a5) # 1ffff7c4 <_data_lma+0x1fffcf74>
    141e:	8082                	ret

00001420 <EXTI_Init>:
    1420:	4158                	lw	a4,4(a0)
    1422:	00052303          	lw	t1,0(a0)
    1426:	454c                	lw	a1,12(a0)
    1428:	40010637          	lui	a2,0x40010
    142c:	40060793          	addi	a5,a2,1024 # 40010400 <__global_pointer$+0x2000fbc0>
    1430:	973e                	add	a4,a4,a5
    1432:	fff34693          	not	a3,t1
    1436:	c5b1                	beqz	a1,1482 <EXTI_Init+0x62>
    1438:	40062583          	lw	a1,1024(a2)
    143c:	8df5                	and	a1,a1,a3
    143e:	40b62023          	sw	a1,1024(a2)
    1442:	43d0                	lw	a2,4(a5)
    1444:	8ef1                	and	a3,a3,a2
    1446:	c3d4                	sw	a3,4(a5)
    1448:	4314                	lw	a3,0(a4)
    144a:	0066e6b3          	or	a3,a3,t1
    144e:	c314                	sw	a3,0(a4)
    1450:	4118                	lw	a4,0(a0)
    1452:	4790                	lw	a2,8(a5)
    1454:	fff74693          	not	a3,a4
    1458:	8e75                	and	a2,a2,a3
    145a:	c790                	sw	a2,8(a5)
    145c:	47d0                	lw	a2,12(a5)
    145e:	8ef1                	and	a3,a3,a2
    1460:	c7d4                	sw	a3,12(a5)
    1462:	4514                	lw	a3,8(a0)
    1464:	4641                	li	a2,16
    1466:	00c69963          	bne	a3,a2,1478 <EXTI_Init+0x58>
    146a:	4794                	lw	a3,8(a5)
    146c:	8ed9                	or	a3,a3,a4
    146e:	c794                	sw	a3,8(a5)
    1470:	47d4                	lw	a3,12(a5)
    1472:	8f55                	or	a4,a4,a3
    1474:	c7d8                	sw	a4,12(a5)
    1476:	8082                	ret
    1478:	97b6                	add	a5,a5,a3
    147a:	4394                	lw	a3,0(a5)
    147c:	8f55                	or	a4,a4,a3
    147e:	c398                	sw	a4,0(a5)
    1480:	8082                	ret
    1482:	431c                	lw	a5,0(a4)
    1484:	8ff5                	and	a5,a5,a3
    1486:	c31c                	sw	a5,0(a4)
    1488:	8082                	ret

0000148a <EXTI_GetITStatus>:
    148a:	400107b7          	lui	a5,0x40010
    148e:	40078713          	addi	a4,a5,1024 # 40010400 <__global_pointer$+0x2000fbc0>
    1492:	4007a783          	lw	a5,1024(a5)
    1496:	4b58                	lw	a4,20(a4)
    1498:	8f69                	and	a4,a4,a0
    149a:	c709                	beqz	a4,14a4 <EXTI_GetITStatus+0x1a>
    149c:	8d7d                	and	a0,a0,a5
    149e:	00a03533          	snez	a0,a0
    14a2:	8082                	ret
    14a4:	4501                	li	a0,0
    14a6:	8082                	ret

000014a8 <EXTI_ClearITPendingBit>:
    14a8:	400107b7          	lui	a5,0x40010
    14ac:	40a7aa23          	sw	a0,1044(a5) # 40010414 <__global_pointer$+0x2000fbd4>
    14b0:	8082                	ret

000014b2 <GPIO_Init>:
    14b2:	4594                	lw	a3,8(a1)
    14b4:	0106f793          	andi	a5,a3,16
    14b8:	00f6f293          	andi	t0,a3,15
    14bc:	c781                	beqz	a5,14c4 <GPIO_Init+0x12>
    14be:	41dc                	lw	a5,4(a1)
    14c0:	00f2e2b3          	or	t0,t0,a5
    14c4:	0005d383          	lhu	t2,0(a1)
    14c8:	0ff3f793          	andi	a5,t2,255
    14cc:	c3a5                	beqz	a5,152c <GPIO_Init+0x7a>
    14ce:	00052303          	lw	t1,0(a0)
    14d2:	1161                	addi	sp,sp,-8
    14d4:	c222                	sw	s0,4(sp)
    14d6:	c026                	sw	s1,0(sp)
    14d8:	4781                	li	a5,0
    14da:	02800413          	li	s0,40
    14de:	04800493          	li	s1,72
    14e2:	4705                	li	a4,1
    14e4:	00f71633          	sll	a2,a4,a5
    14e8:	00c3f733          	and	a4,t2,a2
    14ec:	02e61263          	bne	a2,a4,1510 <GPIO_Init+0x5e>
    14f0:	00279593          	slli	a1,a5,0x2
    14f4:	473d                	li	a4,15
    14f6:	00b71733          	sll	a4,a4,a1
    14fa:	fff74713          	not	a4,a4
    14fe:	00677333          	and	t1,a4,t1
    1502:	00b295b3          	sll	a1,t0,a1
    1506:	0065e333          	or	t1,a1,t1
    150a:	00869d63          	bne	a3,s0,1524 <GPIO_Init+0x72>
    150e:	c950                	sw	a2,20(a0)
    1510:	0785                	addi	a5,a5,1
    1512:	4721                	li	a4,8
    1514:	fce797e3          	bne	a5,a4,14e2 <GPIO_Init+0x30>
    1518:	4412                	lw	s0,4(sp)
    151a:	00652023          	sw	t1,0(a0)
    151e:	4482                	lw	s1,0(sp)
    1520:	0121                	addi	sp,sp,8
    1522:	8082                	ret
    1524:	fe9696e3          	bne	a3,s1,1510 <GPIO_Init+0x5e>
    1528:	c910                	sw	a2,16(a0)
    152a:	b7dd                	j	1510 <GPIO_Init+0x5e>
    152c:	8082                	ret

0000152e <GPIO_ReadInputDataBit>:
    152e:	4508                	lw	a0,8(a0)
    1530:	8d6d                	and	a0,a0,a1
    1532:	00a03533          	snez	a0,a0
    1536:	8082                	ret

00001538 <GPIO_EXTILineConfig>:
    1538:	40010737          	lui	a4,0x40010
    153c:	4714                	lw	a3,8(a4)
    153e:	0586                	slli	a1,a1,0x1
    1540:	478d                	li	a5,3
    1542:	00b797b3          	sll	a5,a5,a1
    1546:	fff7c793          	not	a5,a5
    154a:	8ff5                	and	a5,a5,a3
    154c:	c71c                	sw	a5,8(a4)
    154e:	471c                	lw	a5,8(a4)
    1550:	00b515b3          	sll	a1,a0,a1
    1554:	8ddd                	or	a1,a1,a5
    1556:	c70c                	sw	a1,8(a4)
    1558:	8082                	ret

0000155a <NVIC_PriorityGroupConfig>:
    155a:	98a1a623          	sw	a0,-1652(gp) # 200001cc <NVIC_Priority_Group>
    155e:	8082                	ret

00001560 <NVIC_Init>:
    1560:	98c1a683          	lw	a3,-1652(gp) # 200001cc <NVIC_Priority_Group>
    1564:	4785                	li	a5,1
    1566:	2118                	lbu	a4,0(a0)
    1568:	02f69063          	bne	a3,a5,1588 <NVIC_Init+0x28>
    156c:	311c                	lbu	a5,1(a0)
    156e:	02d79c63          	bne	a5,a3,15a6 <NVIC_Init+0x46>
    1572:	213c                	lbu	a5,2(a0)
    1574:	079a                	slli	a5,a5,0x6
    1576:	f807e793          	ori	a5,a5,-128
    157a:	e000e6b7          	lui	a3,0xe000e
    157e:	0ff7f793          	andi	a5,a5,255
    1582:	96ba                	add	a3,a3,a4
    1584:	40f68023          	sb	a5,1024(a3) # e000e400 <__global_pointer$+0xc000dbc0>
    1588:	4685                	li	a3,1
    158a:	00575793          	srli	a5,a4,0x5
    158e:	00e69733          	sll	a4,a3,a4
    1592:	4154                	lw	a3,4(a0)
    1594:	ce89                	beqz	a3,15ae <NVIC_Init+0x4e>
    1596:	04078793          	addi	a5,a5,64
    159a:	078a                	slli	a5,a5,0x2
    159c:	e000e6b7          	lui	a3,0xe000e
    15a0:	97b6                	add	a5,a5,a3
    15a2:	c398                	sw	a4,0(a5)
    15a4:	8082                	ret
    15a6:	f3ed                	bnez	a5,1588 <NVIC_Init+0x28>
    15a8:	213c                	lbu	a5,2(a0)
    15aa:	079a                	slli	a5,a5,0x6
    15ac:	b7f9                	j	157a <NVIC_Init+0x1a>
    15ae:	06078793          	addi	a5,a5,96
    15b2:	e000e6b7          	lui	a3,0xe000e
    15b6:	078a                	slli	a5,a5,0x2
    15b8:	97b6                	add	a5,a5,a3
    15ba:	c398                	sw	a4,0(a5)
    15bc:	0000100f          	fence.i
    15c0:	8082                	ret

000015c2 <RCC_AdjustHSICalibrationValue>:
    15c2:	40021737          	lui	a4,0x40021
    15c6:	431c                	lw	a5,0(a4)
    15c8:	050e                	slli	a0,a0,0x3
    15ca:	f077f793          	andi	a5,a5,-249
    15ce:	8d5d                	or	a0,a0,a5
    15d0:	c308                	sw	a0,0(a4)
    15d2:	8082                	ret

000015d4 <RCC_GetClocksFreq>:
    15d4:	acdfe2ef          	jal	t0,a0 <__riscv_save_0>
    15d8:	40021737          	lui	a4,0x40021
    15dc:	435c                	lw	a5,4(a4)
    15de:	4691                	li	a3,4
    15e0:	842a                	mv	s0,a0
    15e2:	8bb1                	andi	a5,a5,12
    15e4:	00d78563          	beq	a5,a3,15ee <RCC_GetClocksFreq+0x1a>
    15e8:	46a1                	li	a3,8
    15ea:	08d78063          	beq	a5,a3,166a <RCC_GetClocksFreq+0x96>
    15ee:	016e37b7          	lui	a5,0x16e3
    15f2:	60078793          	addi	a5,a5,1536 # 16e3600 <_data_lma+0x16e0db0>
    15f6:	c01c                	sw	a5,0(s0)
    15f8:	400216b7          	lui	a3,0x40021
    15fc:	42dc                	lw	a5,4(a3)
    15fe:	8391                	srli	a5,a5,0x4
    1600:	00f7f713          	andi	a4,a5,15
    1604:	200007b7          	lui	a5,0x20000
    1608:	02c78793          	addi	a5,a5,44 # 2000002c <APBAHBPrescTable>
    160c:	97ba                	add	a5,a5,a4
    160e:	238c                	lbu	a1,0(a5)
    1610:	42dc                	lw	a5,4(a3)
    1612:	4018                	lw	a4,0(s0)
    1614:	0ff5f593          	andi	a1,a1,255
    1618:	0807f793          	andi	a5,a5,128
    161c:	00b75533          	srl	a0,a4,a1
    1620:	e781                	bnez	a5,1628 <RCC_GetClocksFreq+0x54>
    1622:	853a                	mv	a0,a4
    1624:	ab1fe0ef          	jal	ra,d4 <__udivsi3>
    1628:	c048                	sw	a0,4(s0)
    162a:	c408                	sw	a0,8(s0)
    162c:	c448                	sw	a0,12(s0)
    162e:	400217b7          	lui	a5,0x40021
    1632:	43dc                	lw	a5,4(a5)
    1634:	468d                	li	a3,3
    1636:	83ad                	srli	a5,a5,0xb
    1638:	8bfd                	andi	a5,a5,31
    163a:	0037d713          	srli	a4,a5,0x3
    163e:	078a                	slli	a5,a5,0x2
    1640:	8bf1                	andi	a5,a5,28
    1642:	8fd9                	or	a5,a5,a4
    1644:	0137f613          	andi	a2,a5,19
    1648:	0037f713          	andi	a4,a5,3
    164c:	00c6f463          	bgeu	a3,a2,1654 <RCC_GetClocksFreq+0x80>
    1650:	ff478713          	addi	a4,a5,-12 # 40020ff4 <__global_pointer$+0x200207b4>
    1654:	200007b7          	lui	a5,0x20000
    1658:	01878793          	addi	a5,a5,24 # 20000018 <ADCPrescTable>
    165c:	97ba                	add	a5,a5,a4
    165e:	238c                	lbu	a1,0(a5)
    1660:	a75fe0ef          	jal	ra,d4 <__udivsi3>
    1664:	c808                	sw	a0,16(s0)
    1666:	a45fe06f          	j	aa <__riscv_restore_0>
    166a:	435c                	lw	a5,4(a4)
    166c:	02dc77b7          	lui	a5,0x2dc7
    1670:	c0078793          	addi	a5,a5,-1024 # 2dc6c00 <_data_lma+0x2dc43b0>
    1674:	b749                	j	15f6 <RCC_GetClocksFreq+0x22>

00001676 <RCC_APB2PeriphClockCmd>:
    1676:	c599                	beqz	a1,1684 <RCC_APB2PeriphClockCmd+0xe>
    1678:	40021737          	lui	a4,0x40021
    167c:	4f1c                	lw	a5,24(a4)
    167e:	8d5d                	or	a0,a0,a5
    1680:	cf08                	sw	a0,24(a4)
    1682:	8082                	ret
    1684:	400217b7          	lui	a5,0x40021
    1688:	4f98                	lw	a4,24(a5)
    168a:	fff54513          	not	a0,a0
    168e:	8d79                	and	a0,a0,a4
    1690:	cf88                	sw	a0,24(a5)
    1692:	8082                	ret

00001694 <RCC_APB1PeriphClockCmd>:
    1694:	c599                	beqz	a1,16a2 <RCC_APB1PeriphClockCmd+0xe>
    1696:	40021737          	lui	a4,0x40021
    169a:	4f5c                	lw	a5,28(a4)
    169c:	8d5d                	or	a0,a0,a5
    169e:	cf48                	sw	a0,28(a4)
    16a0:	8082                	ret
    16a2:	400217b7          	lui	a5,0x40021
    16a6:	4fd8                	lw	a4,28(a5)
    16a8:	fff54513          	not	a0,a0
    16ac:	8d79                	and	a0,a0,a4
    16ae:	cfc8                	sw	a0,28(a5)
    16b0:	8082                	ret

000016b2 <TIM_TimeBaseInit>:
    16b2:	211e                	lhu	a5,0(a0)
    16b4:	40013737          	lui	a4,0x40013
    16b8:	c0070713          	addi	a4,a4,-1024 # 40012c00 <__global_pointer$+0x200123c0>
    16bc:	07c2                	slli	a5,a5,0x10
    16be:	83c1                	srli	a5,a5,0x10
    16c0:	00e50663          	beq	a0,a4,16cc <TIM_TimeBaseInit+0x1a>
    16c4:	40000737          	lui	a4,0x40000
    16c8:	00e51663          	bne	a0,a4,16d4 <TIM_TimeBaseInit+0x22>
    16cc:	21ba                	lhu	a4,2(a1)
    16ce:	f8f7f793          	andi	a5,a5,-113
    16d2:	8fd9                	or	a5,a5,a4
    16d4:	21fa                	lhu	a4,6(a1)
    16d6:	cff7f793          	andi	a5,a5,-769
    16da:	07c2                	slli	a5,a5,0x10
    16dc:	83c1                	srli	a5,a5,0x10
    16de:	8fd9                	or	a5,a5,a4
    16e0:	a11e                	sh	a5,0(a0)
    16e2:	21de                	lhu	a5,4(a1)
    16e4:	b55e                	sh	a5,44(a0)
    16e6:	219e                	lhu	a5,0(a1)
    16e8:	b51e                	sh	a5,40(a0)
    16ea:	400137b7          	lui	a5,0x40013
    16ee:	c0078793          	addi	a5,a5,-1024 # 40012c00 <__global_pointer$+0x200123c0>
    16f2:	00f51463          	bne	a0,a5,16fa <TIM_TimeBaseInit+0x48>
    16f6:	259c                	lbu	a5,8(a1)
    16f8:	b91e                	sh	a5,48(a0)
    16fa:	4785                	li	a5,1
    16fc:	a95e                	sh	a5,20(a0)
    16fe:	8082                	ret

00001700 <TIM_OC1Init>:
    1700:	311e                	lhu	a5,32(a0)
    1702:	2192                	lhu	a2,0(a1)
    1704:	0025d303          	lhu	t1,2(a1)
    1708:	07c2                	slli	a5,a5,0x10
    170a:	83c1                	srli	a5,a5,0x10
    170c:	9bf9                	andi	a5,a5,-2
    170e:	07c2                	slli	a5,a5,0x10
    1710:	83c1                	srli	a5,a5,0x10
    1712:	b11e                	sh	a5,32(a0)
    1714:	311e                	lhu	a5,32(a0)
    1716:	2156                	lhu	a3,4(a0)
    1718:	2d1a                	lhu	a4,24(a0)
    171a:	07c2                	slli	a5,a5,0x10
    171c:	83c1                	srli	a5,a5,0x10
    171e:	0742                	slli	a4,a4,0x10
    1720:	8341                	srli	a4,a4,0x10
    1722:	f8c77713          	andi	a4,a4,-116
    1726:	8f51                	or	a4,a4,a2
    1728:	2592                	lhu	a2,8(a1)
    172a:	9bf5                	andi	a5,a5,-3
    172c:	06c2                	slli	a3,a3,0x10
    172e:	00666633          	or	a2,a2,t1
    1732:	8fd1                	or	a5,a5,a2
    1734:	40013637          	lui	a2,0x40013
    1738:	c0060613          	addi	a2,a2,-1024 # 40012c00 <__global_pointer$+0x200123c0>
    173c:	82c1                	srli	a3,a3,0x10
    173e:	02c51063          	bne	a0,a2,175e <TIM_OC1Init+0x5e>
    1742:	25b2                	lhu	a2,10(a1)
    1744:	9bdd                	andi	a5,a5,-9
    1746:	00e5d303          	lhu	t1,14(a1)
    174a:	8fd1                	or	a5,a5,a2
    174c:	21d2                	lhu	a2,4(a1)
    174e:	9bed                	andi	a5,a5,-5
    1750:	cff6f693          	andi	a3,a3,-769
    1754:	8fd1                	or	a5,a5,a2
    1756:	25d2                	lhu	a2,12(a1)
    1758:	00666633          	or	a2,a2,t1
    175c:	8ed1                	or	a3,a3,a2
    175e:	a156                	sh	a3,4(a0)
    1760:	ad1a                	sh	a4,24(a0)
    1762:	21fa                	lhu	a4,6(a1)
    1764:	d958                	sw	a4,52(a0)
    1766:	b11e                	sh	a5,32(a0)
    1768:	8082                	ret

0000176a <TIM_Cmd>:
    176a:	211e                	lhu	a5,0(a0)
    176c:	c589                	beqz	a1,1776 <TIM_Cmd+0xc>
    176e:	0017e793          	ori	a5,a5,1
    1772:	a11e                	sh	a5,0(a0)
    1774:	8082                	ret
    1776:	07c2                	slli	a5,a5,0x10
    1778:	83c1                	srli	a5,a5,0x10
    177a:	9bf9                	andi	a5,a5,-2
    177c:	07c2                	slli	a5,a5,0x10
    177e:	83c1                	srli	a5,a5,0x10
    1780:	bfcd                	j	1772 <TIM_Cmd+0x8>

00001782 <TIM_CtrlPWMOutputs>:
    1782:	04455783          	lhu	a5,68(a0)
    1786:	c591                	beqz	a1,1792 <TIM_CtrlPWMOutputs+0x10>
    1788:	6721                	lui	a4,0x8
    178a:	8fd9                	or	a5,a5,a4
    178c:	04f51223          	sh	a5,68(a0)
    1790:	8082                	ret
    1792:	07c6                	slli	a5,a5,0x11
    1794:	83c5                	srli	a5,a5,0x11
    1796:	bfdd                	j	178c <TIM_CtrlPWMOutputs+0xa>

00001798 <TIM_ITConfig>:
    1798:	255e                	lhu	a5,12(a0)
    179a:	c601                	beqz	a2,17a2 <TIM_ITConfig+0xa>
    179c:	8ddd                	or	a1,a1,a5
    179e:	a54e                	sh	a1,12(a0)
    17a0:	8082                	ret
    17a2:	fff5c593          	not	a1,a1
    17a6:	8dfd                	and	a1,a1,a5
    17a8:	bfdd                	j	179e <TIM_ITConfig+0x6>

000017aa <TIM_ARRPreloadConfig>:
    17aa:	211e                	lhu	a5,0(a0)
    17ac:	c589                	beqz	a1,17b6 <TIM_ARRPreloadConfig+0xc>
    17ae:	0807e793          	ori	a5,a5,128
    17b2:	a11e                	sh	a5,0(a0)
    17b4:	8082                	ret
    17b6:	07c2                	slli	a5,a5,0x10
    17b8:	83c1                	srli	a5,a5,0x10
    17ba:	f7f7f793          	andi	a5,a5,-129
    17be:	07c2                	slli	a5,a5,0x10
    17c0:	83c1                	srli	a5,a5,0x10
    17c2:	bfc5                	j	17b2 <TIM_ARRPreloadConfig+0x8>

000017c4 <TIM_OC1PreloadConfig>:
    17c4:	2d1e                	lhu	a5,24(a0)
    17c6:	07c2                	slli	a5,a5,0x10
    17c8:	83c1                	srli	a5,a5,0x10
    17ca:	9bdd                	andi	a5,a5,-9
    17cc:	8ddd                	or	a1,a1,a5
    17ce:	ad0e                	sh	a1,24(a0)
    17d0:	8082                	ret

000017d2 <TIM_SetCompare1>:
    17d2:	d94c                	sw	a1,52(a0)
    17d4:	8082                	ret

000017d6 <TIM_GetITStatus>:
    17d6:	291e                	lhu	a5,16(a0)
    17d8:	254a                	lhu	a0,12(a0)
    17da:	8fed                	and	a5,a5,a1
    17dc:	0542                	slli	a0,a0,0x10
    17de:	8141                	srli	a0,a0,0x10
    17e0:	c789                	beqz	a5,17ea <TIM_GetITStatus+0x14>
    17e2:	8d6d                	and	a0,a0,a1
    17e4:	00a03533          	snez	a0,a0
    17e8:	8082                	ret
    17ea:	4501                	li	a0,0
    17ec:	8082                	ret

000017ee <TIM_ClearITPendingBit>:
    17ee:	fff5c593          	not	a1,a1
    17f2:	05c2                	slli	a1,a1,0x10
    17f4:	81c1                	srli	a1,a1,0x10
    17f6:	a90e                	sh	a1,16(a0)
    17f8:	8082                	ret

000017fa <USART_Init>:
    17fa:	8a7fe2ef          	jal	t0,a0 <__riscv_save_0>
    17fe:	2916                	lhu	a3,16(a0)
    1800:	77f5                	lui	a5,0xffffd
    1802:	17fd                	addi	a5,a5,-1
    1804:	8ff5                	and	a5,a5,a3
    1806:	21f6                	lhu	a3,6(a1)
    1808:	25da                	lhu	a4,12(a1)
    180a:	1121                	addi	sp,sp,-24
    180c:	8fd5                	or	a5,a5,a3
    180e:	a91e                	sh	a5,16(a0)
    1810:	2556                	lhu	a3,12(a0)
    1812:	77fd                	lui	a5,0xfffff
    1814:	9f378793          	addi	a5,a5,-1549 # ffffe9f3 <__global_pointer$+0xdfffe1b3>
    1818:	8ff5                	and	a5,a5,a3
    181a:	21d6                	lhu	a3,4(a1)
    181c:	842a                	mv	s0,a0
    181e:	c02e                	sw	a1,0(sp)
    1820:	8fd5                	or	a5,a5,a3
    1822:	2596                	lhu	a3,8(a1)
    1824:	8fd5                	or	a5,a5,a3
    1826:	25b6                	lhu	a3,10(a1)
    1828:	8fd5                	or	a5,a5,a3
    182a:	a55e                	sh	a5,12(a0)
    182c:	295e                	lhu	a5,20(a0)
    182e:	07c2                	slli	a5,a5,0x10
    1830:	83c1                	srli	a5,a5,0x10
    1832:	cff7f793          	andi	a5,a5,-769
    1836:	8f5d                	or	a4,a4,a5
    1838:	a95a                	sh	a4,20(a0)
    183a:	0048                	addi	a0,sp,4
    183c:	d99ff0ef          	jal	ra,15d4 <RCC_GetClocksFreq>
    1840:	400147b7          	lui	a5,0x40014
    1844:	80078793          	addi	a5,a5,-2048 # 40013800 <__global_pointer$+0x20012fc0>
    1848:	4582                	lw	a1,0(sp)
    184a:	06f41263          	bne	s0,a5,18ae <USART_Init+0xb4>
    184e:	47c2                	lw	a5,16(sp)
    1850:	245a                	lhu	a4,12(s0)
    1852:	00179513          	slli	a0,a5,0x1
    1856:	953e                	add	a0,a0,a5
    1858:	0742                	slli	a4,a4,0x10
    185a:	050e                	slli	a0,a0,0x3
    185c:	8741                	srai	a4,a4,0x10
    185e:	953e                	add	a0,a0,a5
    1860:	418c                	lw	a1,0(a1)
    1862:	04075863          	bgez	a4,18b2 <USART_Init+0xb8>
    1866:	0586                	slli	a1,a1,0x1
    1868:	86dfe0ef          	jal	ra,d4 <__udivsi3>
    186c:	06400593          	li	a1,100
    1870:	c02a                	sw	a0,0(sp)
    1872:	863fe0ef          	jal	ra,d4 <__udivsi3>
    1876:	4782                	lw	a5,0(sp)
    1878:	00451493          	slli	s1,a0,0x4
    187c:	06400593          	li	a1,100
    1880:	853e                	mv	a0,a5
    1882:	87ffe0ef          	jal	ra,100 <__umodsi3>
    1886:	245e                	lhu	a5,12(s0)
    1888:	07c2                	slli	a5,a5,0x10
    188a:	87c1                	srai	a5,a5,0x10
    188c:	0207d563          	bgez	a5,18b6 <USART_Init+0xbc>
    1890:	050e                	slli	a0,a0,0x3
    1892:	06400593          	li	a1,100
    1896:	03250513          	addi	a0,a0,50
    189a:	83bfe0ef          	jal	ra,d4 <__udivsi3>
    189e:	891d                	andi	a0,a0,7
    18a0:	8cc9                	or	s1,s1,a0
    18a2:	04c2                	slli	s1,s1,0x10
    18a4:	80c1                	srli	s1,s1,0x10
    18a6:	a406                	sh	s1,8(s0)
    18a8:	0161                	addi	sp,sp,24
    18aa:	801fe06f          	j	aa <__riscv_restore_0>
    18ae:	47b2                	lw	a5,12(sp)
    18b0:	b745                	j	1850 <USART_Init+0x56>
    18b2:	058a                	slli	a1,a1,0x2
    18b4:	bf55                	j	1868 <USART_Init+0x6e>
    18b6:	0512                	slli	a0,a0,0x4
    18b8:	06400593          	li	a1,100
    18bc:	03250513          	addi	a0,a0,50
    18c0:	815fe0ef          	jal	ra,d4 <__udivsi3>
    18c4:	893d                	andi	a0,a0,15
    18c6:	bfe9                	j	18a0 <USART_Init+0xa6>

000018c8 <USART_Cmd>:
    18c8:	c591                	beqz	a1,18d4 <USART_Cmd+0xc>
    18ca:	255e                	lhu	a5,12(a0)
    18cc:	6709                	lui	a4,0x2
    18ce:	8fd9                	or	a5,a5,a4
    18d0:	a55e                	sh	a5,12(a0)
    18d2:	8082                	ret
    18d4:	255a                	lhu	a4,12(a0)
    18d6:	77f9                	lui	a5,0xffffe
    18d8:	17fd                	addi	a5,a5,-1
    18da:	8ff9                	and	a5,a5,a4
    18dc:	bfd5                	j	18d0 <USART_Cmd+0x8>

000018de <USART_SendData>:
    18de:	1ff5f593          	andi	a1,a1,511
    18e2:	a14e                	sh	a1,4(a0)
    18e4:	8082                	ret

000018e6 <USART_GetFlagStatus>:
    18e6:	210a                	lhu	a0,0(a0)
    18e8:	8d6d                	and	a0,a0,a1
    18ea:	00a03533          	snez	a0,a0
    18ee:	8082                	ret

000018f0 <Delay_Init>:
    18f0:	fb0fe2ef          	jal	t0,a0 <__riscv_save_0>
    18f4:	200007b7          	lui	a5,0x20000
    18f8:	0147a503          	lw	a0,20(a5) # 20000014 <SystemCoreClock>
    18fc:	007a15b7          	lui	a1,0x7a1
    1900:	20058593          	addi	a1,a1,512 # 7a1200 <_data_lma+0x79e9b0>
    1904:	fd0fe0ef          	jal	ra,d4 <__udivsi3>
    1908:	0ff57513          	andi	a0,a0,255
    190c:	98a18923          	sb	a0,-1646(gp) # 200001d2 <p_us>
    1910:	00551793          	slli	a5,a0,0x5
    1914:	8f89                	sub	a5,a5,a0
    1916:	078a                	slli	a5,a5,0x2
    1918:	953e                	add	a0,a0,a5
    191a:	050e                	slli	a0,a0,0x3
    191c:	98a19823          	sh	a0,-1648(gp) # 200001d0 <p_ms>
    1920:	f8afe06f          	j	aa <__riscv_restore_0>

00001924 <Delay_Ms>:
    1924:	f7cfe2ef          	jal	t0,a0 <__riscv_save_0>
    1928:	e000f437          	lui	s0,0xe000f
    192c:	405c                	lw	a5,4(s0)
    192e:	85aa                	mv	a1,a0
    1930:	9bf9                	andi	a5,a5,-2
    1932:	c05c                	sw	a5,4(s0)
    1934:	9901d503          	lhu	a0,-1648(gp) # 200001d0 <p_ms>
    1938:	f7cfe0ef          	jal	ra,b4 <__mulsi3>
    193c:	c808                	sw	a0,16(s0)
    193e:	00042423          	sw	zero,8(s0) # e000f008 <__global_pointer$+0xc000e7c8>
    1942:	401c                	lw	a5,0(s0)
    1944:	0017e793          	ori	a5,a5,1
    1948:	c01c                	sw	a5,0(s0)
    194a:	e000f7b7          	lui	a5,0xe000f
    194e:	43d8                	lw	a4,4(a5)
    1950:	8b05                	andi	a4,a4,1
    1952:	df75                	beqz	a4,194e <Delay_Ms+0x2a>
    1954:	4398                	lw	a4,0(a5)
    1956:	9b79                	andi	a4,a4,-2
    1958:	c398                	sw	a4,0(a5)
    195a:	f50fe06f          	j	aa <__riscv_restore_0>

0000195e <USART_Printf_Init>:
    195e:	f42fe2ef          	jal	t0,a0 <__riscv_save_0>
    1962:	842a                	mv	s0,a0
    1964:	6511                	lui	a0,0x4
    1966:	1111                	addi	sp,sp,-28
    1968:	4585                	li	a1,1
    196a:	02050513          	addi	a0,a0,32 # 4020 <_data_lma+0x17d0>
    196e:	d09ff0ef          	jal	ra,1676 <RCC_APB2PeriphClockCmd>
    1972:	02000793          	li	a5,32
    1976:	807c                	sh	a5,0(sp)
    1978:	40011537          	lui	a0,0x40011
    197c:	478d                	li	a5,3
    197e:	c23e                	sw	a5,4(sp)
    1980:	858a                	mv	a1,sp
    1982:	47e1                	li	a5,24
    1984:	40050513          	addi	a0,a0,1024 # 40011400 <__global_pointer$+0x20010bc0>
    1988:	c43e                	sw	a5,8(sp)
    198a:	b29ff0ef          	jal	ra,14b2 <GPIO_Init>
    198e:	c622                	sw	s0,12(sp)
    1990:	40014437          	lui	s0,0x40014
    1994:	000807b7          	lui	a5,0x80
    1998:	006c                	addi	a1,sp,12
    199a:	80040513          	addi	a0,s0,-2048 # 40013800 <__global_pointer$+0x20012fc0>
    199e:	ca3e                	sw	a5,20(sp)
    19a0:	c802                	sw	zero,16(sp)
    19a2:	00011c23          	sh	zero,24(sp)
    19a6:	3d91                	jal	17fa <USART_Init>
    19a8:	4585                	li	a1,1
    19aa:	80040513          	addi	a0,s0,-2048
    19ae:	3f29                	jal	18c8 <USART_Cmd>
    19b0:	0171                	addi	sp,sp,28
    19b2:	ef8fe06f          	j	aa <__riscv_restore_0>

000019b6 <_write>:
    19b6:	eeafe2ef          	jal	t0,a0 <__riscv_save_0>
    19ba:	1171                	addi	sp,sp,-4
    19bc:	84ae                	mv	s1,a1
    19be:	4401                	li	s0,0
    19c0:	02c45d63          	bge	s0,a2,19fa <_write+0x44>
    19c4:	400147b7          	lui	a5,0x40014
    19c8:	80078713          	addi	a4,a5,-2048 # 40013800 <__global_pointer$+0x20012fc0>
    19cc:	853a                	mv	a0,a4
    19ce:	04000593          	li	a1,64
    19d2:	c032                	sw	a2,0(sp)
    19d4:	3f09                	jal	18e6 <USART_GetFlagStatus>
    19d6:	400147b7          	lui	a5,0x40014
    19da:	80078713          	addi	a4,a5,-2048 # 40013800 <__global_pointer$+0x20012fc0>
    19de:	4602                	lw	a2,0(sp)
    19e0:	d575                	beqz	a0,19cc <_write+0x16>
    19e2:	00848733          	add	a4,s1,s0
    19e6:	00070583          	lb	a1,0(a4) # 2000 <puts+0x2>
    19ea:	80078513          	addi	a0,a5,-2048
    19ee:	0405                	addi	s0,s0,1
    19f0:	05c2                	slli	a1,a1,0x10
    19f2:	81c1                	srli	a1,a1,0x10
    19f4:	35ed                	jal	18de <USART_SendData>
    19f6:	4602                	lw	a2,0(sp)
    19f8:	b7e1                	j	19c0 <_write+0xa>
    19fa:	8532                	mv	a0,a2
    19fc:	0111                	addi	sp,sp,4
    19fe:	eacfe06f          	j	aa <__riscv_restore_0>

00001a02 <printchar>:
    1a02:	1141                	addi	sp,sp,-16
    1a04:	c606                	sw	ra,12(sp)
    1a06:	c02e                	sw	a1,0(sp)
    1a08:	cd0d                	beqz	a0,1a42 <printchar+0x40>
    1a0a:	4118                	lw	a4,0(a0)
    1a0c:	87aa                	mv	a5,a0
    1a0e:	c305                	beqz	a4,1a2e <printchar+0x2c>
    1a10:	4158                	lw	a4,4(a0)
    1a12:	557d                	li	a0,-1
    1a14:	cb11                	beqz	a4,1a28 <printchar+0x26>
    1a16:	4685                	li	a3,1
    1a18:	00d71b63          	bne	a4,a3,1a2e <printchar+0x2c>
    1a1c:	4798                	lw	a4,8(a5)
    1a1e:	00070023          	sb	zero,0(a4)
    1a22:	0007a223          	sw	zero,4(a5)
    1a26:	4505                	li	a0,1
    1a28:	40b2                	lw	ra,12(sp)
    1a2a:	0141                	addi	sp,sp,16
    1a2c:	8082                	ret
    1a2e:	4798                	lw	a4,8(a5)
    1a30:	4682                	lw	a3,0(sp)
    1a32:	a314                	sb	a3,0(a4)
    1a34:	4798                	lw	a4,8(a5)
    1a36:	0705                	addi	a4,a4,1
    1a38:	c798                	sw	a4,8(a5)
    1a3a:	43d8                	lw	a4,4(a5)
    1a3c:	177d                	addi	a4,a4,-1
    1a3e:	c3d8                	sw	a4,4(a5)
    1a40:	b7dd                	j	1a26 <printchar+0x24>
    1a42:	4605                	li	a2,1
    1a44:	858a                	mv	a1,sp
    1a46:	3f85                	jal	19b6 <_write>
    1a48:	bff9                	j	1a26 <printchar+0x24>

00001a4a <prints>:
    1a4a:	1101                	addi	sp,sp,-32
    1a4c:	cc22                	sw	s0,24(sp)
    1a4e:	c22e                	sw	a1,4(sp)
    1a50:	ce06                	sw	ra,28(sp)
    1a52:	ca26                	sw	s1,20(sp)
    1a54:	842a                	mv	s0,a0
    1a56:	4781                	li	a5,0
    1a58:	02000593          	li	a1,32
    1a5c:	02064563          	bltz	a2,1a86 <prints+0x3c>
    1a60:	4592                	lw	a1,4(sp)
    1a62:	95be                	add	a1,a1,a5
    1a64:	00058583          	lb	a1,0(a1)
    1a68:	e58d                	bnez	a1,1a92 <prints+0x48>
    1a6a:	02c7d863          	bge	a5,a2,1a9a <prints+0x50>
    1a6e:	02e7d463          	bge	a5,a4,1a96 <prints+0x4c>
    1a72:	8e19                	sub	a2,a2,a4
    1a74:	02000513          	li	a0,32
    1a78:	0026f593          	andi	a1,a3,2
    1a7c:	c02a                	sw	a0,0(sp)
    1a7e:	c589                	beqz	a1,1a88 <prints+0x3e>
    1a80:	e701                	bnez	a4,1a88 <prints+0x3e>
    1a82:	03000593          	li	a1,48
    1a86:	c02e                	sw	a1,0(sp)
    1a88:	8a85                	andi	a3,a3,1
    1a8a:	4481                	li	s1,0
    1a8c:	ea95                	bnez	a3,1ac0 <prints+0x76>
    1a8e:	84b2                	mv	s1,a2
    1a90:	a00d                	j	1ab2 <prints+0x68>
    1a92:	0785                	addi	a5,a5,1
    1a94:	b7f1                	j	1a60 <prints+0x16>
    1a96:	8e1d                	sub	a2,a2,a5
    1a98:	bff1                	j	1a74 <prints+0x2a>
    1a9a:	4601                	li	a2,0
    1a9c:	bfe1                	j	1a74 <prints+0x2a>
    1a9e:	4582                	lw	a1,0(sp)
    1aa0:	8522                	mv	a0,s0
    1aa2:	c83a                	sw	a4,16(sp)
    1aa4:	c632                	sw	a2,12(sp)
    1aa6:	c43e                	sw	a5,8(sp)
    1aa8:	3fa9                	jal	1a02 <printchar>
    1aaa:	47a2                	lw	a5,8(sp)
    1aac:	4632                	lw	a2,12(sp)
    1aae:	4742                	lw	a4,16(sp)
    1ab0:	14fd                	addi	s1,s1,-1
    1ab2:	fe9046e3          	bgtz	s1,1a9e <prints+0x54>
    1ab6:	84b2                	mv	s1,a2
    1ab8:	00065363          	bgez	a2,1abe <prints+0x74>
    1abc:	4481                	li	s1,0
    1abe:	8e05                	sub	a2,a2,s1
    1ac0:	02e7c763          	blt	a5,a4,1aee <prints+0xa4>
    1ac4:	87a6                	mv	a5,s1
    1ac6:	4692                	lw	a3,4(sp)
    1ac8:	40978733          	sub	a4,a5,s1
    1acc:	9736                	add	a4,a4,a3
    1ace:	00070583          	lb	a1,0(a4)
    1ad2:	ed95                	bnez	a1,1b0e <prints+0xc4>
    1ad4:	84b2                	mv	s1,a2
    1ad6:	04904463          	bgtz	s1,1b1e <prints+0xd4>
    1ada:	00065363          	bgez	a2,1ae0 <prints+0x96>
    1ade:	4601                	li	a2,0
    1ae0:	40f2                	lw	ra,28(sp)
    1ae2:	4462                	lw	s0,24(sp)
    1ae4:	44d2                	lw	s1,20(sp)
    1ae6:	00f60533          	add	a0,a2,a5
    1aea:	6105                	addi	sp,sp,32
    1aec:	8082                	ret
    1aee:	8f1d                	sub	a4,a4,a5
    1af0:	87ba                	mv	a5,a4
    1af2:	03000593          	li	a1,48
    1af6:	8522                	mv	a0,s0
    1af8:	c832                	sw	a2,16(sp)
    1afa:	c63e                	sw	a5,12(sp)
    1afc:	c43a                	sw	a4,8(sp)
    1afe:	3711                	jal	1a02 <printchar>
    1b00:	47b2                	lw	a5,12(sp)
    1b02:	4722                	lw	a4,8(sp)
    1b04:	4642                	lw	a2,16(sp)
    1b06:	17fd                	addi	a5,a5,-1
    1b08:	f7ed                	bnez	a5,1af2 <prints+0xa8>
    1b0a:	94ba                	add	s1,s1,a4
    1b0c:	bf65                	j	1ac4 <prints+0x7a>
    1b0e:	8522                	mv	a0,s0
    1b10:	c632                	sw	a2,12(sp)
    1b12:	c43e                	sw	a5,8(sp)
    1b14:	35fd                	jal	1a02 <printchar>
    1b16:	47a2                	lw	a5,8(sp)
    1b18:	4632                	lw	a2,12(sp)
    1b1a:	0785                	addi	a5,a5,1
    1b1c:	b76d                	j	1ac6 <prints+0x7c>
    1b1e:	4582                	lw	a1,0(sp)
    1b20:	8522                	mv	a0,s0
    1b22:	c432                	sw	a2,8(sp)
    1b24:	c23e                	sw	a5,4(sp)
    1b26:	3df1                	jal	1a02 <printchar>
    1b28:	14fd                	addi	s1,s1,-1
    1b2a:	4622                	lw	a2,8(sp)
    1b2c:	4792                	lw	a5,4(sp)
    1b2e:	b765                	j	1ad6 <prints+0x8c>

00001b30 <printInt>:
    1b30:	7139                	addi	sp,sp,-64
    1b32:	de06                	sw	ra,60(sp)
    1b34:	dc22                	sw	s0,56(sp)
    1b36:	da26                	sw	s1,52(sp)
    1b38:	c23e                	sw	a5,4(sp)
    1b3a:	8332                	mv	t1,a2
    1b3c:	863a                	mv	a2,a4
    1b3e:	ed89                	bnez	a1,1b58 <printInt+0x28>
    1b40:	4692                	lw	a3,4(sp)
    1b42:	03000793          	li	a5,48
    1b46:	4701                	li	a4,0
    1b48:	086c                	addi	a1,sp,28
    1b4a:	86fc                	sh	a5,28(sp)
    1b4c:	3dfd                	jal	1a4a <prints>
    1b4e:	50f2                	lw	ra,60(sp)
    1b50:	5462                	lw	s0,56(sp)
    1b52:	54d2                	lw	s1,52(sp)
    1b54:	6121                	addi	sp,sp,64
    1b56:	8082                	ret
    1b58:	84aa                	mv	s1,a0
    1b5a:	8436                	mv	s0,a3
    1b5c:	87ae                	mv	a5,a1
    1b5e:	ca91                	beqz	a3,1b72 <printInt+0x42>
    1b60:	4729                	li	a4,10
    1b62:	4401                	li	s0,0
    1b64:	00e31763          	bne	t1,a4,1b72 <printInt+0x42>
    1b68:	0005d563          	bgez	a1,1b72 <printInt+0x42>
    1b6c:	40b007b3          	neg	a5,a1
    1b70:	4405                	li	s0,1
    1b72:	4686                	lw	a3,64(sp)
    1b74:	020109a3          	sb	zero,51(sp)
    1b78:	03310713          	addi	a4,sp,51
    1b7c:	fc668693          	addi	a3,a3,-58 # 40020fc6 <__global_pointer$+0x20020786>
    1b80:	c436                	sw	a3,8(sp)
    1b82:	859a                	mv	a1,t1
    1b84:	853e                	mv	a0,a5
    1b86:	ca32                	sw	a2,20(sp)
    1b88:	c83a                	sw	a4,16(sp)
    1b8a:	c61a                	sw	t1,12(sp)
    1b8c:	c03e                	sw	a5,0(sp)
    1b8e:	d72fe0ef          	jal	ra,100 <__umodsi3>
    1b92:	46a5                	li	a3,9
    1b94:	4782                	lw	a5,0(sp)
    1b96:	4332                	lw	t1,12(sp)
    1b98:	4742                	lw	a4,16(sp)
    1b9a:	4652                	lw	a2,20(sp)
    1b9c:	00a6d463          	bge	a3,a0,1ba4 <printInt+0x74>
    1ba0:	46a2                	lw	a3,8(sp)
    1ba2:	9536                	add	a0,a0,a3
    1ba4:	03050513          	addi	a0,a0,48
    1ba8:	fff70693          	addi	a3,a4,-1
    1bac:	fea70fa3          	sb	a0,-1(a4)
    1bb0:	859a                	mv	a1,t1
    1bb2:	853e                	mv	a0,a5
    1bb4:	cc32                	sw	a2,24(sp)
    1bb6:	ca3a                	sw	a4,20(sp)
    1bb8:	c81a                	sw	t1,16(sp)
    1bba:	c63e                	sw	a5,12(sp)
    1bbc:	c036                	sw	a3,0(sp)
    1bbe:	d16fe0ef          	jal	ra,d4 <__udivsi3>
    1bc2:	47b2                	lw	a5,12(sp)
    1bc4:	4342                	lw	t1,16(sp)
    1bc6:	4752                	lw	a4,20(sp)
    1bc8:	4662                	lw	a2,24(sp)
    1bca:	0467fd63          	bgeu	a5,t1,1c24 <printInt+0xf4>
    1bce:	cc09                	beqz	s0,1be8 <printInt+0xb8>
    1bd0:	ce29                	beqz	a2,1c2a <printInt+0xfa>
    1bd2:	4792                	lw	a5,4(sp)
    1bd4:	8b89                	andi	a5,a5,2
    1bd6:	cbb1                	beqz	a5,1c2a <printInt+0xfa>
    1bd8:	02d00593          	li	a1,45
    1bdc:	8526                	mv	a0,s1
    1bde:	c432                	sw	a2,8(sp)
    1be0:	e23ff0ef          	jal	ra,1a02 <printchar>
    1be4:	4622                	lw	a2,8(sp)
    1be6:	167d                	addi	a2,a2,-1
    1be8:	4792                	lw	a5,4(sp)
    1bea:	8b91                	andi	a5,a5,4
    1bec:	c785                	beqz	a5,1c14 <printInt+0xe4>
    1bee:	4706                	lw	a4,64(sp)
    1bf0:	06100793          	li	a5,97
    1bf4:	c432                	sw	a2,8(sp)
    1bf6:	03000593          	li	a1,48
    1bfa:	8526                	mv	a0,s1
    1bfc:	04f71163          	bne	a4,a5,1c3e <printInt+0x10e>
    1c00:	e03ff0ef          	jal	ra,1a02 <printchar>
    1c04:	07800593          	li	a1,120
    1c08:	8526                	mv	a0,s1
    1c0a:	df9ff0ef          	jal	ra,1a02 <printchar>
    1c0e:	4622                	lw	a2,8(sp)
    1c10:	0409                	addi	s0,s0,2
    1c12:	1679                	addi	a2,a2,-2
    1c14:	4716                	lw	a4,68(sp)
    1c16:	4692                	lw	a3,4(sp)
    1c18:	4582                	lw	a1,0(sp)
    1c1a:	8526                	mv	a0,s1
    1c1c:	e2fff0ef          	jal	ra,1a4a <prints>
    1c20:	9522                	add	a0,a0,s0
    1c22:	b735                	j	1b4e <printInt+0x1e>
    1c24:	87aa                	mv	a5,a0
    1c26:	4702                	lw	a4,0(sp)
    1c28:	bfa9                	j	1b82 <printInt+0x52>
    1c2a:	4682                	lw	a3,0(sp)
    1c2c:	02d00793          	li	a5,45
    1c30:	4401                	li	s0,0
    1c32:	fef68fa3          	sb	a5,-1(a3)
    1c36:	ffe70793          	addi	a5,a4,-2
    1c3a:	c03e                	sw	a5,0(sp)
    1c3c:	b775                	j	1be8 <printInt+0xb8>
    1c3e:	dc5ff0ef          	jal	ra,1a02 <printchar>
    1c42:	05800593          	li	a1,88
    1c46:	b7c9                	j	1c08 <printInt+0xd8>

00001c48 <printLongLongInt>:
    1c48:	4501                	li	a0,0
    1c4a:	8082                	ret

00001c4c <printDouble>:
    1c4c:	4501                	li	a0,0
    1c4e:	8082                	ret

00001c50 <print>:
    1c50:	fd810113          	addi	sp,sp,-40
    1c54:	d022                	sw	s0,32(sp)
    1c56:	ce26                	sw	s1,28(sp)
    1c58:	d206                	sw	ra,36(sp)
    1c5a:	c42a                	sw	a0,8(sp)
    1c5c:	82ae                	mv	t0,a1
    1c5e:	8432                	mv	s0,a2
    1c60:	c602                	sw	zero,12(sp)
    1c62:	4481                	li	s1,0
    1c64:	00028583          	lb	a1,0(t0)
    1c68:	ed91                	bnez	a1,1c84 <print+0x34>
    1c6a:	47a2                	lw	a5,8(sp)
    1c6c:	c789                	beqz	a5,1c76 <print+0x26>
    1c6e:	4581                	li	a1,0
    1c70:	853e                	mv	a0,a5
    1c72:	d91ff0ef          	jal	ra,1a02 <printchar>
    1c76:	5092                	lw	ra,36(sp)
    1c78:	5402                	lw	s0,32(sp)
    1c7a:	8526                	mv	a0,s1
    1c7c:	44f2                	lw	s1,28(sp)
    1c7e:	02810113          	addi	sp,sp,40
    1c82:	8082                	ret
    1c84:	02500793          	li	a5,37
    1c88:	00f58963          	beq	a1,a5,1c9a <print+0x4a>
    1c8c:	4522                	lw	a0,8(sp)
    1c8e:	c816                	sw	t0,16(sp)
    1c90:	0485                	addi	s1,s1,1
    1c92:	d71ff0ef          	jal	ra,1a02 <printchar>
    1c96:	42c2                	lw	t0,16(sp)
    1c98:	a005                	j	1cb8 <print+0x68>
    1c9a:	00128783          	lb	a5,1(t0)
    1c9e:	00128713          	addi	a4,t0,1
    1ca2:	00b79d63          	bne	a5,a1,1cbc <print+0x6c>
    1ca6:	4522                	lw	a0,8(sp)
    1ca8:	02500593          	li	a1,37
    1cac:	c83a                	sw	a4,16(sp)
    1cae:	d55ff0ef          	jal	ra,1a02 <printchar>
    1cb2:	4742                	lw	a4,16(sp)
    1cb4:	0485                	addi	s1,s1,1
    1cb6:	82ba                	mv	t0,a4
    1cb8:	0285                	addi	t0,t0,1
    1cba:	b76d                	j	1c64 <print+0x14>
    1cbc:	d7dd                	beqz	a5,1c6a <print+0x1a>
    1cbe:	02b00693          	li	a3,43
    1cc2:	04d78963          	beq	a5,a3,1d14 <print+0xc4>
    1cc6:	00f6c863          	blt	a3,a5,1cd6 <print+0x86>
    1cca:	02300693          	li	a3,35
    1cce:	04d78663          	beq	a5,a3,1d1a <print+0xca>
    1cd2:	4781                	li	a5,0
    1cd4:	a005                	j	1cf4 <print+0xa4>
    1cd6:	02d00693          	li	a3,45
    1cda:	00d78a63          	beq	a5,a3,1cee <print+0x9e>
    1cde:	03000693          	li	a3,48
    1ce2:	fed798e3          	bne	a5,a3,1cd2 <print+0x82>
    1ce6:	00228713          	addi	a4,t0,2
    1cea:	4789                	li	a5,2
    1cec:	a021                	j	1cf4 <print+0xa4>
    1cee:	00228713          	addi	a4,t0,2
    1cf2:	4785                	li	a5,1
    1cf4:	00070683          	lb	a3,0(a4)
    1cf8:	02b00613          	li	a2,43
    1cfc:	04c68363          	beq	a3,a2,1d42 <print+0xf2>
    1d00:	02d64163          	blt	a2,a3,1d22 <print+0xd2>
    1d04:	02300613          	li	a2,35
    1d08:	02c68b63          	beq	a3,a2,1d3e <print+0xee>
    1d0c:	82ba                	mv	t0,a4
    1d0e:	4501                	li	a0,0
    1d10:	46a5                	li	a3,9
    1d12:	a081                	j	1d52 <print+0x102>
    1d14:	00228713          	addi	a4,t0,2
    1d18:	bf6d                	j	1cd2 <print+0x82>
    1d1a:	00228713          	addi	a4,t0,2
    1d1e:	4791                	li	a5,4
    1d20:	bfd1                	j	1cf4 <print+0xa4>
    1d22:	02d00613          	li	a2,45
    1d26:	00c68963          	beq	a3,a2,1d38 <print+0xe8>
    1d2a:	03000613          	li	a2,48
    1d2e:	fcc69fe3          	bne	a3,a2,1d0c <print+0xbc>
    1d32:	0027e793          	ori	a5,a5,2
    1d36:	a031                	j	1d42 <print+0xf2>
    1d38:	0705                	addi	a4,a4,1
    1d3a:	4785                	li	a5,1
    1d3c:	bfc1                	j	1d0c <print+0xbc>
    1d3e:	0047e793          	ori	a5,a5,4
    1d42:	0705                	addi	a4,a4,1
    1d44:	b7e1                	j	1d0c <print+0xbc>
    1d46:	00251613          	slli	a2,a0,0x2
    1d4a:	9532                	add	a0,a0,a2
    1d4c:	0506                	slli	a0,a0,0x1
    1d4e:	953a                	add	a0,a0,a4
    1d50:	0285                	addi	t0,t0,1
    1d52:	00028603          	lb	a2,0(t0)
    1d56:	fd060713          	addi	a4,a2,-48
    1d5a:	0ff77593          	andi	a1,a4,255
    1d5e:	feb6f4e3          	bgeu	a3,a1,1d46 <print+0xf6>
    1d62:	02e00713          	li	a4,46
    1d66:	4699                	li	a3,6
    1d68:	00e61e63          	bne	a2,a4,1d84 <print+0x134>
    1d6c:	0285                	addi	t0,t0,1
    1d6e:	4681                	li	a3,0
    1d70:	45a5                	li	a1,9
    1d72:	00028603          	lb	a2,0(t0)
    1d76:	fd060613          	addi	a2,a2,-48
    1d7a:	0ff67713          	andi	a4,a2,255
    1d7e:	02e5f563          	bgeu	a1,a4,1da8 <print+0x158>
    1d82:	c636                	sw	a3,12(sp)
    1d84:	00028703          	lb	a4,0(t0)
    1d88:	06a00613          	li	a2,106
    1d8c:	0ac70d63          	beq	a4,a2,1e46 <print+0x1f6>
    1d90:	02e64363          	blt	a2,a4,1db6 <print+0x166>
    1d94:	04c00613          	li	a2,76
    1d98:	0ac70763          	beq	a4,a2,1e46 <print+0x1f6>
    1d9c:	06800613          	li	a2,104
    1da0:	08c70c63          	beq	a4,a2,1e38 <print+0x1e8>
    1da4:	4581                	li	a1,0
    1da6:	a82d                	j	1de0 <print+0x190>
    1da8:	00269713          	slli	a4,a3,0x2
    1dac:	96ba                	add	a3,a3,a4
    1dae:	0686                	slli	a3,a3,0x1
    1db0:	96b2                	add	a3,a3,a2
    1db2:	0285                	addi	t0,t0,1
    1db4:	bf7d                	j	1d72 <print+0x122>
    1db6:	07400613          	li	a2,116
    1dba:	08c70663          	beq	a4,a2,1e46 <print+0x1f6>
    1dbe:	07a00613          	li	a2,122
    1dc2:	08c70263          	beq	a4,a2,1e46 <print+0x1f6>
    1dc6:	06c00613          	li	a2,108
    1dca:	4581                	li	a1,0
    1dcc:	00c71a63          	bne	a4,a2,1de0 <print+0x190>
    1dd0:	00128603          	lb	a2,1(t0)
    1dd4:	458d                	li	a1,3
    1dd6:	00e61463          	bne	a2,a4,1dde <print+0x18e>
    1dda:	0285                	addi	t0,t0,1
    1ddc:	4591                	li	a1,4
    1dde:	0285                	addi	t0,t0,1
    1de0:	00028603          	lb	a2,0(t0)
    1de4:	06000393          	li	t2,96
    1de8:	06100713          	li	a4,97
    1dec:	00c3c463          	blt	t2,a2,1df4 <print+0x1a4>
    1df0:	04100713          	li	a4,65
    1df4:	06700393          	li	t2,103
    1df8:	06c3c463          	blt	t2,a2,1e60 <print+0x210>
    1dfc:	06500393          	li	t2,101
    1e00:	18765663          	bge	a2,t2,1f8c <print+0x33c>
    1e04:	04700393          	li	t2,71
    1e08:	04c3c163          	blt	t2,a2,1e4a <print+0x1fa>
    1e0c:	04500593          	li	a1,69
    1e10:	16b65e63          	bge	a2,a1,1f8c <print+0x33c>
    1e14:	04300713          	li	a4,67
    1e18:	eae610e3          	bne	a2,a4,1cb8 <print+0x68>
    1e1c:	4018                	lw	a4,0(s0)
    1e1e:	00440393          	addi	t2,s0,4
    1e22:	ca16                	sw	t0,20(sp)
    1e24:	00e10c23          	sb	a4,24(sp)
    1e28:	c81e                	sw	t2,16(sp)
    1e2a:	00010ca3          	sb	zero,25(sp)
    1e2e:	4701                	li	a4,0
    1e30:	86be                	mv	a3,a5
    1e32:	862a                	mv	a2,a0
    1e34:	082c                	addi	a1,sp,24
    1e36:	a849                	j	1ec8 <print+0x278>
    1e38:	00128603          	lb	a2,1(t0)
    1e3c:	4581                	li	a1,0
    1e3e:	fae611e3          	bne	a2,a4,1de0 <print+0x190>
    1e42:	0289                	addi	t0,t0,2
    1e44:	bf71                	j	1de0 <print+0x190>
    1e46:	0285                	addi	t0,t0,1
    1e48:	bfb1                	j	1da4 <print+0x154>
    1e4a:	06300693          	li	a3,99
    1e4e:	fcd607e3          	beq	a2,a3,1e1c <print+0x1cc>
    1e52:	06c6cf63          	blt	a3,a2,1ed0 <print+0x280>
    1e56:	05800693          	li	a3,88
    1e5a:	02d60363          	beq	a2,a3,1e80 <print+0x230>
    1e5e:	bda9                	j	1cb8 <print+0x68>
    1e60:	07300693          	li	a3,115
    1e64:	04d60463          	beq	a2,a3,1eac <print+0x25c>
    1e68:	02c6cb63          	blt	a3,a2,1e9e <print+0x24e>
    1e6c:	06f00693          	li	a3,111
    1e70:	0ed60563          	beq	a2,a3,1f5a <print+0x30a>
    1e74:	07000693          	li	a3,112
    1e78:	0047e793          	ori	a5,a5,4
    1e7c:	e2d61ee3          	bne	a2,a3,1cb8 <print+0x68>
    1e80:	4691                	li	a3,4
    1e82:	0cd59263          	bne	a1,a3,1f46 <print+0x2f6>
    1e86:	00840393          	addi	t2,s0,8
    1e8a:	400c                	lw	a1,0(s0)
    1e8c:	4050                	lw	a2,4(s0)
    1e8e:	ca16                	sw	t0,20(sp)
    1e90:	c23a                	sw	a4,4(sp)
    1e92:	c03e                	sw	a5,0(sp)
    1e94:	c81e                	sw	t2,16(sp)
    1e96:	87aa                	mv	a5,a0
    1e98:	4701                	li	a4,0
    1e9a:	46c1                	li	a3,16
    1e9c:	a881                	j	1eec <print+0x29c>
    1e9e:	07500693          	li	a3,117
    1ea2:	06d60b63          	beq	a2,a3,1f18 <print+0x2c8>
    1ea6:	07800693          	li	a3,120
    1eaa:	bf45                	j	1e5a <print+0x20a>
    1eac:	4018                	lw	a4,0(s0)
    1eae:	000036b7          	lui	a3,0x3
    1eb2:	00440393          	addi	t2,s0,4
    1eb6:	84868593          	addi	a1,a3,-1976 # 2848 <font+0x500>
    1eba:	c311                	beqz	a4,1ebe <print+0x26e>
    1ebc:	85ba                	mv	a1,a4
    1ebe:	4732                	lw	a4,12(sp)
    1ec0:	ca16                	sw	t0,20(sp)
    1ec2:	c81e                	sw	t2,16(sp)
    1ec4:	86be                	mv	a3,a5
    1ec6:	862a                	mv	a2,a0
    1ec8:	4522                	lw	a0,8(sp)
    1eca:	b81ff0ef          	jal	ra,1a4a <prints>
    1ece:	a015                	j	1ef2 <print+0x2a2>
    1ed0:	4691                	li	a3,4
    1ed2:	02d59563          	bne	a1,a3,1efc <print+0x2ac>
    1ed6:	00840393          	addi	t2,s0,8
    1eda:	400c                	lw	a1,0(s0)
    1edc:	4050                	lw	a2,4(s0)
    1ede:	ca16                	sw	t0,20(sp)
    1ee0:	c23a                	sw	a4,4(sp)
    1ee2:	c03e                	sw	a5,0(sp)
    1ee4:	c81e                	sw	t2,16(sp)
    1ee6:	87aa                	mv	a5,a0
    1ee8:	4705                	li	a4,1
    1eea:	46a9                	li	a3,10
    1eec:	4522                	lw	a0,8(sp)
    1eee:	d5bff0ef          	jal	ra,1c48 <printLongLongInt>
    1ef2:	43c2                	lw	t2,16(sp)
    1ef4:	94aa                	add	s1,s1,a0
    1ef6:	841e                	mv	s0,t2
    1ef8:	42d2                	lw	t0,20(sp)
    1efa:	bb7d                	j	1cb8 <print+0x68>
    1efc:	46b2                	lw	a3,12(sp)
    1efe:	400c                	lw	a1,0(s0)
    1f00:	c816                	sw	t0,16(sp)
    1f02:	c236                	sw	a3,4(sp)
    1f04:	c03a                	sw	a4,0(sp)
    1f06:	0411                	addi	s0,s0,4
    1f08:	872a                	mv	a4,a0
    1f0a:	4685                	li	a3,1
    1f0c:	4629                	li	a2,10
    1f0e:	4522                	lw	a0,8(sp)
    1f10:	c21ff0ef          	jal	ra,1b30 <printInt>
    1f14:	94aa                	add	s1,s1,a0
    1f16:	b341                	j	1c96 <print+0x46>
    1f18:	4691                	li	a3,4
    1f1a:	00d59d63          	bne	a1,a3,1f34 <print+0x2e4>
    1f1e:	00840393          	addi	t2,s0,8
    1f22:	400c                	lw	a1,0(s0)
    1f24:	4050                	lw	a2,4(s0)
    1f26:	ca16                	sw	t0,20(sp)
    1f28:	c23a                	sw	a4,4(sp)
    1f2a:	c03e                	sw	a5,0(sp)
    1f2c:	c81e                	sw	t2,16(sp)
    1f2e:	87aa                	mv	a5,a0
    1f30:	4701                	li	a4,0
    1f32:	bf65                	j	1eea <print+0x29a>
    1f34:	46b2                	lw	a3,12(sp)
    1f36:	400c                	lw	a1,0(s0)
    1f38:	c816                	sw	t0,16(sp)
    1f3a:	c236                	sw	a3,4(sp)
    1f3c:	c03a                	sw	a4,0(sp)
    1f3e:	0411                	addi	s0,s0,4
    1f40:	872a                	mv	a4,a0
    1f42:	4681                	li	a3,0
    1f44:	b7e1                	j	1f0c <print+0x2bc>
    1f46:	46b2                	lw	a3,12(sp)
    1f48:	c816                	sw	t0,16(sp)
    1f4a:	400c                	lw	a1,0(s0)
    1f4c:	4641                	li	a2,16
    1f4e:	c236                	sw	a3,4(sp)
    1f50:	c03a                	sw	a4,0(sp)
    1f52:	0411                	addi	s0,s0,4
    1f54:	872a                	mv	a4,a0
    1f56:	4681                	li	a3,0
    1f58:	bf5d                	j	1f0e <print+0x2be>
    1f5a:	4691                	li	a3,4
    1f5c:	00d59e63          	bne	a1,a3,1f78 <print+0x328>
    1f60:	00840393          	addi	t2,s0,8
    1f64:	400c                	lw	a1,0(s0)
    1f66:	4050                	lw	a2,4(s0)
    1f68:	ca16                	sw	t0,20(sp)
    1f6a:	c23a                	sw	a4,4(sp)
    1f6c:	c03e                	sw	a5,0(sp)
    1f6e:	c81e                	sw	t2,16(sp)
    1f70:	87aa                	mv	a5,a0
    1f72:	4701                	li	a4,0
    1f74:	46a1                	li	a3,8
    1f76:	bf9d                	j	1eec <print+0x29c>
    1f78:	46b2                	lw	a3,12(sp)
    1f7a:	400c                	lw	a1,0(s0)
    1f7c:	c816                	sw	t0,16(sp)
    1f7e:	c236                	sw	a3,4(sp)
    1f80:	c03a                	sw	a4,0(sp)
    1f82:	0411                	addi	s0,s0,4
    1f84:	872a                	mv	a4,a0
    1f86:	4681                	li	a3,0
    1f88:	4621                	li	a2,8
    1f8a:	b751                	j	1f0e <print+0x2be>
    1f8c:	400c                	lw	a1,0(s0)
    1f8e:	00840613          	addi	a2,s0,8
    1f92:	4040                	lw	s0,4(s0)
    1f94:	c23a                	sw	a4,4(sp)
    1f96:	872a                	mv	a4,a0
    1f98:	4522                	lw	a0,8(sp)
    1f9a:	c832                	sw	a2,16(sp)
    1f9c:	c03e                	sw	a5,0(sp)
    1f9e:	8622                	mv	a2,s0
    1fa0:	87b6                	mv	a5,a3
    1fa2:	46a9                	li	a3,10
    1fa4:	ca16                	sw	t0,20(sp)
    1fa6:	ca7ff0ef          	jal	ra,1c4c <printDouble>
    1faa:	94aa                	add	s1,s1,a0
    1fac:	4442                	lw	s0,16(sp)
    1fae:	b7a9                	j	1ef8 <print+0x2a8>

00001fb0 <printf>:
    1fb0:	fdc10113          	addi	sp,sp,-36
    1fb4:	c82e                	sw	a1,16(sp)
    1fb6:	ca32                	sw	a2,20(sp)
    1fb8:	85aa                	mv	a1,a0
    1fba:	0810                	addi	a2,sp,16
    1fbc:	4501                	li	a0,0
    1fbe:	c606                	sw	ra,12(sp)
    1fc0:	cc36                	sw	a3,24(sp)
    1fc2:	ce3a                	sw	a4,28(sp)
    1fc4:	d03e                	sw	a5,32(sp)
    1fc6:	c032                	sw	a2,0(sp)
    1fc8:	c89ff0ef          	jal	ra,1c50 <print>
    1fcc:	40b2                	lw	ra,12(sp)
    1fce:	02410113          	addi	sp,sp,36
    1fd2:	8082                	ret

00001fd4 <snprintf>:
    1fd4:	fd810113          	addi	sp,sp,-40
    1fd8:	8332                	mv	t1,a2
    1fda:	d23e                	sw	a5,36(sp)
    1fdc:	c42e                	sw	a1,8(sp)
    1fde:	c62a                	sw	a0,12(sp)
    1fe0:	0870                	addi	a2,sp,28
    1fe2:	4785                	li	a5,1
    1fe4:	0048                	addi	a0,sp,4
    1fe6:	859a                	mv	a1,t1
    1fe8:	cc06                	sw	ra,24(sp)
    1fea:	ce36                	sw	a3,28(sp)
    1fec:	d03a                	sw	a4,32(sp)
    1fee:	c23e                	sw	a5,4(sp)
    1ff0:	c032                	sw	a2,0(sp)
    1ff2:	c5fff0ef          	jal	ra,1c50 <print>
    1ff6:	40e2                	lw	ra,24(sp)
    1ff8:	02810113          	addi	sp,sp,40
    1ffc:	8082                	ret

00001ffe <puts>:
    1ffe:	1141                	addi	sp,sp,-16
    2000:	c422                	sw	s0,8(sp)
    2002:	c226                	sw	s1,4(sp)
    2004:	c606                	sw	ra,12(sp)
    2006:	211c                	lbu	a5,0(a0)
    2008:	84aa                	mv	s1,a0
    200a:	4401                	li	s0,0
    200c:	81dc                	sb	a5,3(sp)
    200e:	00310783          	lb	a5,3(sp)
    2012:	0405                	addi	s0,s0,1
    2014:	ef99                	bnez	a5,2032 <puts+0x34>
    2016:	47a9                	li	a5,10
    2018:	00310593          	addi	a1,sp,3
    201c:	4605                	li	a2,1
    201e:	4501                	li	a0,0
    2020:	81dc                	sb	a5,3(sp)
    2022:	995ff0ef          	jal	ra,19b6 <_write>
    2026:	8522                	mv	a0,s0
    2028:	40b2                	lw	ra,12(sp)
    202a:	4422                	lw	s0,8(sp)
    202c:	4492                	lw	s1,4(sp)
    202e:	0141                	addi	sp,sp,16
    2030:	8082                	ret
    2032:	4605                	li	a2,1
    2034:	00310593          	addi	a1,sp,3
    2038:	4501                	li	a0,0
    203a:	97dff0ef          	jal	ra,19b6 <_write>
    203e:	008487b3          	add	a5,s1,s0
    2042:	239c                	lbu	a5,0(a5)
    2044:	81dc                	sb	a5,3(sp)
    2046:	b7e1                	j	200e <puts+0x10>

00002048 <memcpy>:
    2048:	00a5c7b3          	xor	a5,a1,a0
    204c:	8b8d                	andi	a5,a5,3
    204e:	00c50733          	add	a4,a0,a2
    2052:	e781                	bnez	a5,205a <memcpy+0x12>
    2054:	478d                	li	a5,3
    2056:	02c7e763          	bltu	a5,a2,2084 <memcpy+0x3c>
    205a:	87aa                	mv	a5,a0
    205c:	0ae57e63          	bgeu	a0,a4,2118 <memcpy+0xd0>
    2060:	2194                	lbu	a3,0(a1)
    2062:	0785                	addi	a5,a5,1
    2064:	0585                	addi	a1,a1,1
    2066:	fed78fa3          	sb	a3,-1(a5)
    206a:	fee7ebe3          	bltu	a5,a4,2060 <memcpy+0x18>
    206e:	8082                	ret
    2070:	2194                	lbu	a3,0(a1)
    2072:	0785                	addi	a5,a5,1
    2074:	0585                	addi	a1,a1,1
    2076:	fed78fa3          	sb	a3,-1(a5)
    207a:	fee7ebe3          	bltu	a5,a4,2070 <memcpy+0x28>
    207e:	4402                	lw	s0,0(sp)
    2080:	0111                	addi	sp,sp,4
    2082:	8082                	ret
    2084:	00357693          	andi	a3,a0,3
    2088:	87aa                	mv	a5,a0
    208a:	ca89                	beqz	a3,209c <memcpy+0x54>
    208c:	2194                	lbu	a3,0(a1)
    208e:	0785                	addi	a5,a5,1
    2090:	0585                	addi	a1,a1,1
    2092:	fed78fa3          	sb	a3,-1(a5)
    2096:	0037f693          	andi	a3,a5,3
    209a:	bfc5                	j	208a <memcpy+0x42>
    209c:	ffc77693          	andi	a3,a4,-4
    20a0:	fe068613          	addi	a2,a3,-32
    20a4:	06c7f563          	bgeu	a5,a2,210e <memcpy+0xc6>
    20a8:	1171                	addi	sp,sp,-4
    20aa:	c022                	sw	s0,0(sp)
    20ac:	49c0                	lw	s0,20(a1)
    20ae:	0005a303          	lw	t1,0(a1)
    20b2:	0085a383          	lw	t2,8(a1)
    20b6:	cbc0                	sw	s0,20(a5)
    20b8:	4d80                	lw	s0,24(a1)
    20ba:	0067a023          	sw	t1,0(a5)
    20be:	0045a303          	lw	t1,4(a1)
    20c2:	cf80                	sw	s0,24(a5)
    20c4:	4dc0                	lw	s0,28(a1)
    20c6:	0067a223          	sw	t1,4(a5)
    20ca:	00c5a283          	lw	t0,12(a1)
    20ce:	0105a303          	lw	t1,16(a1)
    20d2:	02458593          	addi	a1,a1,36
    20d6:	cfc0                	sw	s0,28(a5)
    20d8:	ffc5a403          	lw	s0,-4(a1)
    20dc:	0077a423          	sw	t2,8(a5)
    20e0:	0057a623          	sw	t0,12(a5)
    20e4:	0067a823          	sw	t1,16(a5)
    20e8:	02478793          	addi	a5,a5,36
    20ec:	fe87ae23          	sw	s0,-4(a5)
    20f0:	fac7eee3          	bltu	a5,a2,20ac <memcpy+0x64>
    20f4:	f8d7f3e3          	bgeu	a5,a3,207a <memcpy+0x32>
    20f8:	4190                	lw	a2,0(a1)
    20fa:	0791                	addi	a5,a5,4
    20fc:	0591                	addi	a1,a1,4
    20fe:	fec7ae23          	sw	a2,-4(a5)
    2102:	bfcd                	j	20f4 <memcpy+0xac>
    2104:	4190                	lw	a2,0(a1)
    2106:	0791                	addi	a5,a5,4
    2108:	0591                	addi	a1,a1,4
    210a:	fec7ae23          	sw	a2,-4(a5)
    210e:	fed7ebe3          	bltu	a5,a3,2104 <memcpy+0xbc>
    2112:	f4e7e7e3          	bltu	a5,a4,2060 <memcpy+0x18>
    2116:	8082                	ret
    2118:	8082                	ret
    211a:	0000                	unimp
    211c:	1609                	addi	a2,a2,-30
    211e:	2009                	jal	2120 <memcpy+0xd8>
    2120:	1b21                	addi	s6,s6,-24
    2122:	15171913          	0x15171913
    2126:	2b1e                	lhu	a5,16(a4)
    2128:	0504                	addi	s1,sp,640
    212a:	0e02                	c.slli64	t3
    212c:	1e08140b          	0x1e08140b
    2130:	1d22                	slli	s10,s10,0x28
    2132:	1e18                	addi	a4,sp,816
    2134:	2b241a1b          	0x2b241a1b
    2138:	0606                	slli	a2,a2,0x1
    213a:	0f02                	c.slli64	t5
    213c:	00004843          	fmadd.s	fa6,ft0,ft0,ft0,rmm
    2140:	003a                	c.slli	zero,0xe
    2142:	0000                	unimp
    2144:	4441                	li	s0,16
    2146:	6f4d2043          	fmadd.q	ft0,fs10,fs4,fa3,rdn
    214a:	696e                	flw	fs2,216(sp)
    214c:	6f74                	flw	fa3,92(a4)
    214e:	0072                	c.slli	zero,0x1c
    2150:	64254843          	0x64254843
    2154:	0000                	unimp
    2156:	0000                	unimp
    2158:	2e2d                	jal	2492 <font+0x14a>
    215a:	2d2d                	jal	2794 <font+0x44c>
    215c:	0056                	c.slli	zero,0x15
    215e:	0000                	unimp
    2160:	32334843          	fmadd.d	fa6,ft6,ft3,ft6,rmm
    2164:	0056                	c.slli	zero,0x15
    2166:	0000                	unimp
    2168:	4956                	lw	s2,84(sp)
    216a:	5445                	li	s0,-15
    216c:	414e                	lw	sp,208(sp)
    216e:	004d                	c.nop	19
    2170:	6425                	lui	s0,0x9
    2172:	252e                	lhu	a1,10(a0)
    2174:	3330                	lbu	a2,3(a4)
    2176:	5664                	lw	s1,108(a2)
    2178:	0000                	unimp
    217a:	0000                	unimp
    217c:	6425                	lui	s0,0x9
    217e:	252e                	lhu	a1,10(a0)
    2180:	3230                	lbu	a2,3(a2)
    2182:	5664                	lw	s1,108(a2)
    2184:	0000                	unimp
	...

00002188 <CSWTCH.3>:
    2188:	07ff 07e0 ffe0 f81f 4843 3233 3056 3330     ........CH32V003
    2198:	4120 4344 0000 0000 6f4d 696e 6f74 2072      ADC....Monitor 
    21a8:	3176 302e 0000 0000 6f54 6375 3a68 5420     v1.0....Touch: T
    21b8:	7275 206e 6e4f 0000 6f48 646c 203a 6f54     urn On..Hold: To
    21c8:	6767 656c 0000 0000 6568 6c6c 006f 0000     ggle....hello...
    21d8:	4843 3233 3056 3330 0000 0000 4441 2043     CH32V003....ADC 
    21e8:	6e69 7469 6169 696c 657a 0d64 0000 0000     initialized.....
    21f8:	5750 204d 6e69 7469 6169 696c 657a 0d64     PWM initialized.
    2208:	0000 0000 6f54 6375 2068 7562 7474 6e6f     ....Touch button
    2218:	6920 696e 6974 6c61 7a69 6465 000d 0000      initialized....
    2228:	6944 7073 616c 2079 6f63 746e 6f72 206c     Display control 
    2238:	6e69 7469 6169 696c 657a 0d64 0000 0000     initialized.....
    2248:	4441 2043 6964 7073 616c 2079 6e69 7469     ADC display init
    2258:	6169 696c 657a 0d64 0000 0000 7953 7473     ialized.....Syst
    2268:	6d65 6920 696e 6974 6c61 7a69 7461 6f69     em initializatio
    2278:	206e 6f63 706d 656c 6574 000d 0a0d 3d3d     n complete....==
    2288:	203d 4843 3233 3056 3330 4120 4344 4d20     = CH32V003 ADC M
    2298:	6e6f 7469 726f 3d20 3d3d 000d 7953 7473     onitor ===..Syst
    22a8:	6d65 6c43 3a6b 2520 2064 7a48 0a0d 0000     emClk: %d Hz....
    22b8:	6843 7069 4449 203a 3025 7838 0a0d 0000     ChipID: %08x....
    22c8:	6f54 6375 3a68 5320 6f68 7472 7020 6572     Touch: Short pre
    22d8:	7373 2d20 7420 7275 696e 676e 6f20 206e     ss - turning on 
    22e8:	6964 7073 616c 0d79 0000 0000 6f54 6375     display.....Touc
    22f8:	3a68 4c20 6e6f 2067 7270 7365 2073 202d     h: Long press - 
    2308:	6f74 6767 696c 676e 6420 7369 6c70 7961     toggling display
    2318:	6d20 646f 0d65 0000 6f54 6375 3a68 5420      mode...Touch: T
    2328:	6d69 6f65 7475 2d20 7420 7275 696e 676e     imeout - turning
    2338:	6f20 6666 6420 7369 6c70 7961 000d 0000      off display....

00002348 <font>:
    2348:	0000 0000 3e00 4f5b 3e5b 6b3e 6b4f 1c3e     .....>[O[>>kOk>.
    2358:	7c3e 1c3e 3c18 3c7e 1c18 7d57 1c57 5e1c     >|>..<~<..W}W..^
    2368:	5e7f 001c 3c18 0018 e7ff e7c3 00ff 2418     .^...<.........$
    2378:	0018 e7ff e7db 30ff 3a48 0e06 2926 2979     .......0H:..&)y)
    2388:	4026 057f 0705 7f40 2505 5a3f e73c 5a3c     &@....@..%?Z<.<Z
    2398:	3e7f 1c1c 0808 1c1c 7f3e 2214 227f 5f14     .>......>.."."._
    23a8:	005f 5f5f 0906 017f 007f 8966 6a95 6060     _.__......f..j``
    23b8:	6060 9460 ffa2 94a2 0408 047e 1008 7e20     ```.......~... ~
    23c8:	1020 0808 1c2a 0808 2a1c 0808 101e 1010      ...*....*......
    23d8:	0c10 0c1e 0c1e 3830 383e 0630 3e0e 060e     ......08>80..>..
    23e8:	0000 0000 0000 5f00 0000 0700 0700 1400     ......._........
    23f8:	147f 147f 2a24 2a7f 2312 0813 6264 4936     ....$*.*.#..db6I
    2408:	2056 0050 0708 0003 1c00 4122 0000 2241     V P......."A..A"
    2418:	001c 1c2a 1c7f 082a 3e08 0808 8000 3070     ..*...*..>....p0
    2428:	0800 0808 0808 0000 6060 2000 0810 0204     ........``. ....
    2438:	513e 4549 003e 7f42 0040 4972 4949 2146     >QIE>.B.@.rIIIF!
    2448:	4941 334d 1418 7f12 2710 4545 3945 4a3c     AIM3.....'EEE9<J
    2458:	4949 4131 1121 0709 4936 4949 4636 4949     II1A!...6III6FII
    2468:	1e29 0000 0014 0000 3440 0000 0800 2214     ).......@4....."
    2478:	1441 1414 1414 4100 1422 0208 5901 0609     A......A"....Y..
    2488:	413e 595d 7c4e 1112 7c12 497f 4949 3e36     >A]YN|...|.III6>
    2498:	4141 2241 417f 4141 7f3e 4949 4149 097f     AAA".AAA>.IIIA..
    24a8:	0909 3e01 4141 7351 087f 0808 007f 7f41     ...>AAQs......A.
    24b8:	0041 4020 3f41 7f01 1408 4122 407f 4040     A. @A?...."A.@@@
    24c8:	7f40 1c02 7f02 047f 1008 3e7f 4141 3e41     @..........>AAA>
    24d8:	097f 0909 3e06 5141 5e21 097f 2919 2646     .....>AQ!^...)F&
    24e8:	4949 3249 0103 017f 3f03 4040 3f40 201f     III2.....?@@@?. 
    24f8:	2040 3f1f 3840 3f40 1463 1408 0363 7804     @ .?@8@?c...c..x
    2508:	0304 5961 4d49 0043 417f 4141 0402 1008     ..aYIMC..AAA....
    2518:	0020 4141 7f41 0204 0201 4004 4040 4040      .AAA......@@@@@
    2528:	0300 0807 2000 5454 4078 287f 4444 3838     ..... TTx@.(DD88
    2538:	4444 2844 4438 2844 387f 5454 1854 0800     DDD(8DD(.8TTT...
    2548:	097e 1802 a4a4 789c 087f 0404 0078 7d44     ~......x....x.D}
    2558:	0040 4020 3d40 7f00 2810 0044 4100 407f     @. @@=...(D..A.@
    2568:	7c00 7804 7804 087c 0404 3878 4444 3844     .|.x.x|...x8DDD8
    2578:	18fc 2424 1818 2424 fc18 087c 0404 4808     ..$$..$$..|....H
    2588:	5454 2454 0404 443f 3c24 4040 7c20 201c     TTT$..?D$<@@ |. 
    2598:	2040 3c1c 3040 3c40 2844 2810 4c44 9090     @ .<@0@<D(.(DL..
    25a8:	7c90 6444 4c54 0044 3608 0041 0000 0077     .|DdTLD..6A...w.
    25b8:	0000 3641 0008 0102 0402 3c02 2326 3c26     ..A6.......<&#&<
    25c8:	a11e 61a1 3a12 4040 7a20 5438 5554 2159     ...a.:@@ z8TTUY!
    25d8:	5555 4179 5422 7854 2142 5455 4078 5420     UUyA"TTxB!UTx@ T
    25e8:	7955 0c40 521e 1272 5539 5555 3959 5454     <EMAIL>.9UUUY9TT
    25f8:	5954 5539 5454 0058 4500 417c 0200 7d45     TY9UTTX..E|A..E}
    2608:	0042 4501 407c 127d 1211 f07d 2528 f028     B..E|@}...}.(%(.
    2618:	547c 4555 2000 5454 547c 0a7c 7f09 3249     |TUE. TT|T|...I2
    2628:	4949 3249 443a 4444 323a 484a 3048 413a     III2:DDD:2JHH0:A
    2638:	2141 3a7a 4042 7820 9d00 a0a0 3d7d 4242     A!z:B@ x....}=BB
    2648:	3d42 403d 4040 3c3d ff24 2424 7e48 4349     B==@@@=<$.$$H~IC
    2658:	2b66 fc2f 2b2f 09ff f629 c020 7e88 0309     f+/./+..). ..~..
    2668:	5420 7954 0041 4400 417d 4830 4a48 3832      TTyA..D}A0HHJ28
    2678:	4040 7a22 7a00 0a0a 7d72 190d 7d31 2926     @@"z.z..r}..1}&)
    2688:	2f29 2628 2929 2629 4830 404d 3820 0808     )/(&)))&0HM@ 8..
    2698:	0808 0808 0808 2f38 c810 baac 102f 3428     ......8/..../.(4
    26a8:	00fa 7b00 0000 1408 142a 2222 2a14 0814     ...{....*."".*..
    26b8:	0055 0055 aa55 aa55 aa55 55ff 55ff 00ff     U.U.U.U.U..U.U..
    26c8:	0000 00ff 1010 ff10 1400 1414 00ff 1010     ................
    26d8:	00ff 10ff f010 f010 1414 fc14 1400 f714     ................
    26e8:	ff00 0000 00ff 14ff f414 fc04 1414 1017     ................
    26f8:	101f 1f10 1f10 1414 1f14 1000 1010 00f0     ................
    2708:	0000 1f00 1010 1010 101f 1010 f010 0010     ................
    2718:	0000 10ff 1010 1010 1010 1010 10ff 0000     ................
    2728:	ff00 0014 ff00 ff00 0000 101f 0017 fc00     ................
    2738:	f404 1414 1017 1417 f414 f404 0000 00ff     ................
    2748:	14f7 1414 1414 1414 00f7 14f7 1414 1417     ................
    2758:	1010 101f 141f 1414 14f4 1010 10f0 00f0     ................
    2768:	1f00 1f10 0000 1f00 0014 0000 14fc 0000     ................
    2778:	10f0 10f0 ff10 ff10 1414 ff14 1014 1010     ................
    2788:	001f 0000 f000 ff10 ffff ffff f0f0 f0f0     ................
    2798:	fff0 ffff 0000 0000 ff00 0fff 0f0f 0f0f     ................
    27a8:	4438 3844 fc44 4a4a 344a 027e 0602 0206     8DD8D.JJJ4~.....
    27b8:	027e 027e 5563 4149 3863 4444 043c 7e40     ~.~.cUIAc8DD<.@~
    27c8:	1e20 0620 7e02 0202 a599 a5e7 1c99 492a      . ..~........*I
    27d8:	1c2a 724c 7201 304c 4d4a 304d 4830 4878     *.Lr.rL0JMM00HxH
    27e8:	bc30 5a62 3d46 493e 4949 7e00 0101 7e01     0.bZF=>III.~...~
    27f8:	2a2a 2a2a 442a 5f44 4444 5140 444a 4040     *****DD_DD@QJD@@
    2808:	4a44 4051 0000 01ff e003 ff80 0000 0808     DJQ@............
    2818:	6b6b 3608 3612 3624 0600 0909 0006 1800     kk.6.6$6........
    2828:	0018 0000 1010 3000 ff40 0101 1f00 0101     .......0@.......
    2838:	001e 1d19 1217 3c00 3c3c 003c 0000 0000     .......<<<<.....
    2848:	6e28 6c75 296c 0000                         (null)..
