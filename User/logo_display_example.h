/**
 * @file logo_display_example.h
 * @brief Header file for logo-style display examples
 * <AUTHOR> Project
 * @date 2025
 */

#ifndef __LOGO_DISPLAY_EXAMPLE_H__
#define __LOGO_DISPLAY_EXAMPLE_H__

#include <stdint.h>

/**
 * @brief Example function to demonstrate the logo-style display
 * Creates a display similar to the reference image with green border,
 * black display area, cyan accents, and voltage readings
 */
void Logo_Display_Example(void);

/**
 * @brief Example with live ADC data
 * Reads actual ADC values and displays them in logo style
 */
void Logo_Display_Live_Example(void);

/**
 * @brief Update just the main voltage display
 * @param new_voltage_mv New voltage value in millivolts
 */
void Logo_Display_Update_Main_Voltage(uint16_t new_voltage_mv);

/**
 * @brief Example showing animated voltage display
 * Creates a simple animation effect by cycling through voltage values
 */
void Logo_Display_Animation_Example(void);

/**
 * @brief Test function to verify all logo display components
 * Tests each component individually with delays
 */
void Logo_Display_Test_All_Components(void);

#endif // __LOGO_DISPLAY_EXAMPLE_H__
