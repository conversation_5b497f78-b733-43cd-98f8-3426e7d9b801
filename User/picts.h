#include <stdint.h>

typedef struct {
    const uint8_t *data;
    uint16_t width;
    uint16_t height;
    uint8_t dataSize;
} tImage;



typedef struct {
    const uint16_t *data;
    uint16_t width;
    uint16_t height;
    uint8_t dataSize;
} tImage_RGB;

extern const tImage_RGB battery_big;
extern const tImage_RGB Image_Battery;
extern const tImage_RGB Image_Battery_2;
extern const tImage Image_battery_small;
    