/**
 * @file adc_display.h
 * @brief ADC Data Display Interface for ST7735
 * <AUTHOR> for CH32V003 Project
 * @date 2025
 */

#ifndef __ADC_DISPLAY_H__
#define __ADC_DISPLAY_H__

#include "ch32v00x_conf.h"
#include "st7735.h"
#include "adc_config.h"

// Display Layout Constants
#define ADC_DISPLAY_START_X     5
#define ADC_DISPLAY_START_Y     5
#define ADC_CHANNEL_HEIGHT      18
#define ADC_VALUE_COLUMN_X      80
#define ADC_VOLTAGE_COLUMN_X    120

// Display Colors for ADC Channels
#define ADC_CHANNEL_1_COLOR     CYAN
#define ADC_CHANNEL_2_COLOR     GREEN
#define ADC_CHANNEL_3_COLOR     YELLOW
#define ADC_CHANNEL_4_COLOR     MAGENTA
#define ADC_LABEL_COLOR         WHITE
#define ADC_VALUE_COLOR         LIGHTGREY
#define ADC_BACKGROUND_COLOR    BLACK

// Colors for the new logo-style display
#define LOGO_BORDER_COLOR       RGB(0, 150, 0)    // Green border
#define LOGO_BACKGROUND_COLOR   BLACK             // Black display area
#define LOGO_TEXT_COLOR         CYAN              // Cyan text
#define LOGO_VOLTAGE_COLOR      WHITE             // White for main voltage
#define LOGO_CHANNEL_COLOR      CYAN              // Cyan for channel indicators

// Vietnam flag colors
#define VN_FLAG_RED             RGB(218, 37, 29)  // Vietnam flag red
#define VN_FLAG_YELLOW          RGB(255, 255, 0)  // Vietnam flag yellow star

// Display Update Modes
typedef enum {
    ADC_DISPLAY_MODE_FULL = 0,      // Update all channels
    ADC_DISPLAY_MODE_VALUES_ONLY,   // Update only values, keep labels
    ADC_DISPLAY_MODE_SINGLE_CHANNEL // Update single channel
} ADC_DisplayMode_t;

// ADC Display Configuration
typedef struct {
    uint8_t show_raw_values;        // Show raw ADC values (0-4095)
    uint8_t show_voltage;           // Show voltage values (mV)
    uint8_t show_percentage;        // Show percentage (0-100%)
    uint8_t decimal_places;         // Decimal places for voltage display
    uint16_t update_interval_ms;    // Update interval in milliseconds
    uint32_t last_update_time;      // Last update timestamp
} ADC_DisplayConfig_t;

// Global ADC display configuration
extern ADC_DisplayConfig_t adc_display_config;

// Function Prototypes
void ADC_Display_Init(void);
void ADC_Display_Update(ADC_Data_t* adc_data, ADC_DisplayMode_t mode);
void ADC_Display_Draw_Header(void);
void ADC_Display_Draw_Channel_Labels(void);
void ADC_Display_Draw_Channel_Data(uint8_t channel, ADC_Data_t* adc_data);
void ADC_Display_Draw_All_Channels(ADC_Data_t* adc_data);
void ADC_Display_Clear_Values_Area(void);
void ADC_Display_Set_Config(uint8_t show_raw, uint8_t show_voltage, uint8_t show_percentage);
uint8_t ADC_Display_Should_Update(void);

// New logo-style display functions
void ADC_Display_Draw_Logo_Style(ADC_Data_t* adc_data);
void ADC_Display_Draw_Logo_Border(void);
void ADC_Display_Draw_Logo_Header(void);
void ADC_Display_Draw_Logo_Channels(ADC_Data_t* adc_data);
void ADC_Display_Draw_Logo_Main_Voltage(uint16_t voltage_mv);
void ADC_Display_Draw_Vietnam_Flag(uint16_t x, uint16_t y, uint16_t width, uint16_t height);

// Utility functions
uint16_t ADC_Display_Get_Channel_Color(uint8_t channel);
void ADC_Display_Format_Voltage(uint16_t voltage_mv, char* buffer, uint8_t buffer_size);
void ADC_Display_Format_Percentage(uint16_t raw_value, char* buffer, uint8_t buffer_size);

#endif // __ADC_DISPLAY_H__
