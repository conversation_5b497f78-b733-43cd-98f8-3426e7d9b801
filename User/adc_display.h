/**
 * @file adc_display.h
 * @brief ADC Data Display Interface for ST7735
 * <AUTHOR> for CH32V003 Project
 * @date 2025
 */

#ifndef __ADC_DISPLAY_H__
#define __ADC_DISPLAY_H__

#include "ch32v00x_conf.h"
#include "st7735.h"
#include "adc_config.h"
#include "battery_levels.h"

// Display Layout Constants
#define ADC_DISPLAY_START_X     5
#define ADC_DISPLAY_START_Y     5
#define ADC_CHANNEL_HEIGHT      18
#define ADC_VALUE_COLUMN_X      80
#define ADC_VOLTAGE_COLUMN_X    120

// Display Colors for ADC Channels
#define ADC_LABEL_COLOR         WHITE
#define ADC_VALUE_COLOR         WHITE
#define ADC_BACKGROUND_COLOR    BLACK

// Colors for the new logo-style display
#define LOGO_BACKGROUND_COLOR   BLACK
#define LOGO_TEXT_COLOR         GREEN
#define LOGO_VOLTAGE_COLOR      YELLOW
#define LOGO_CHANNEL_COLOR      GREEN

// Function Prototypes
void ADC_Display_Draw_Logo_Style(ADC_Data_t* adc_data);
void ADC_Display_Draw_Logo_Border(void);
void ADC_Display_Draw_Logo_Header(void);
void ADC_Display_Draw_Logo_Channels(ADC_Data_t* adc_data);
void ADC_Display_Draw_Logo_Main_Voltage(ADC_Data_t* adc_data);

// Value-only update functions (for performance)
void ADC_Display_Update_Logo_Channel_Values(ADC_Data_t* adc_data);
void ADC_Display_Update_Logo_Main_Voltage_Only(ADC_Data_t* adc_data);
void ADC_Display_Update_Logo_Values_Only(ADC_Data_t* adc_data);

// Utility functions
void ADC_Display_Format_Voltage(uint16_t voltage_mv, char* buffer, uint8_t buffer_size);
void ADC_Display_Format_Percentage(uint16_t raw_value, char* buffer, uint8_t buffer_size);

#endif // __ADC_DISPLAY_H__
