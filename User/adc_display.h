/**
 * @file adc_display.h
 * @brief ADC Data Display Interface for ST7735
 * <AUTHOR> for CH32V003 Project
 * @date 2025
 */

#ifndef __ADC_DISPLAY_H__
#define __ADC_DISPLAY_H__

#include "ch32v00x_conf.h"
#include "st7735.h"
#include "adc_config.h"

// Display Layout Constants
#define ADC_DISPLAY_START_X     5
#define ADC_DISPLAY_START_Y     5
#define ADC_CHANNEL_HEIGHT      18
#define ADC_VALUE_COLUMN_X      80
#define ADC_VOLTAGE_COLUMN_X    120

// Display Colors for ADC Channels
#define ADC_CHANNEL_1_COLOR     CYAN
#define ADC_CHANNEL_2_COLOR     GREEN
#define ADC_CHANNEL_3_COLOR     YELLOW
#define ADC_CHANNEL_4_COLOR     MAGENTA
#define ADC_LABEL_COLOR         WHITE
#define ADC_VALUE_COLOR         LIGHTGREY
#define ADC_BACKGROUND_COLOR    BLACK

// Display Update Modes
typedef enum {
    ADC_DISPLAY_MODE_FULL = 0,      // Update all channels
    ADC_DISPLAY_MODE_VALUES_ONLY,   // Update only values, keep labels
    ADC_DISPLAY_MODE_SINGLE_CHANNEL // Update single channel
} ADC_DisplayMode_t;

// ADC Display Configuration
typedef struct {
    uint8_t show_raw_values;        // Show raw ADC values (0-4095)
    uint8_t show_voltage;           // Show voltage values (mV)
    uint8_t show_percentage;        // Show percentage (0-100%)
    uint8_t decimal_places;         // Decimal places for voltage display
    uint16_t update_interval_ms;    // Update interval in milliseconds
    uint32_t last_update_time;      // Last update timestamp
} ADC_DisplayConfig_t;

// Global ADC display configuration
extern ADC_DisplayConfig_t adc_display_config;

// Function Prototypes
void ADC_Display_Init(void);
void ADC_Display_Update(ADC_Data_t* adc_data, ADC_DisplayMode_t mode);
void ADC_Display_Draw_Header(void);
void ADC_Display_Draw_Channel_Labels(void);
void ADC_Display_Draw_Channel_Data(uint8_t channel, ADC_Data_t* adc_data);
void ADC_Display_Draw_All_Channels(ADC_Data_t* adc_data);
void ADC_Display_Clear_Values_Area(void);
void ADC_Display_Set_Config(uint8_t show_raw, uint8_t show_voltage, uint8_t show_percentage);
uint8_t ADC_Display_Should_Update(void);

// Utility functions
uint16_t ADC_Display_Get_Channel_Color(uint8_t channel);
void ADC_Display_Format_Voltage(uint16_t voltage_mv, char* buffer, uint8_t buffer_size);
void ADC_Display_Format_Percentage(uint16_t raw_value, char* buffer, uint8_t buffer_size);

#endif // __ADC_DISPLAY_H__
