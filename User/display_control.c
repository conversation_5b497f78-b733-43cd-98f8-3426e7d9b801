/**
 * @file display_control.c
 * @brief Display Control Logic Implementation
 * <AUTHOR> for CH32V003 Project
 * @date 2025
 */

#include "display_control.h"
#include "touch_button.h"
#include "debug.h"

// Global display control instance
DisplayControl_t display_control = {0};

/**
 * @brief Initialize display control system
 */
void Display_Control_Init(void)
{
    // Initialize display control structure
    display_control.state = DISPLAY_STATE_OFF;
    display_control.initialized = 0;
    display_control.content_needs_update = 1;
    display_control.last_update_time = 0;
    
    // Initialize ST7735 display
    tft_init();
    
    // Clear screen
    Display_Control_Clear_Screen();
    
    // Show startup message
    Display_Control_Show_Startup_Message();
    
    display_control.initialized = 1;
    
    // Start with display off
    PWM_Turn_Off();
}

/**
 * @brief Update display control state machine
 */
void Display_Control_Update(void)
{
    if (!display_control.initialized) return;
    
    // Update PWM fade effects
    PWM_Update_Fade();
    
    switch (display_control.state) {
        case DISPLAY_STATE_OFF:
            // Display is off, nothing to do
            break;
            
        case DISPLAY_STATE_TURNING_ON:
            // Check if fade-in is complete
            if (!pwm_control.fade_enabled) {
                display_control.state = DISPLAY_STATE_ON;
                display_control.content_needs_update = 1;
            }
            break;
            
        case DISPLAY_STATE_ON:
            // Display is on and ready for content updates
            break;
            
        case DISPLAY_STATE_TURNING_OFF:
            // Check if fade-out is complete
            if (!pwm_control.fade_enabled && PWM_Get_Brightness() == 0) {
                display_control.state = DISPLAY_STATE_OFF;
                Display_Control_Show_Off_Message();
            }
            break;
    }
}

/**
 * @brief Turn on the display
 */
void Display_Control_Turn_On(void)
{
    if (display_control.state == DISPLAY_STATE_OFF) {
        display_control.state = DISPLAY_STATE_TURNING_ON;
        PWM_Turn_On();
    }
}

/**
 * @brief Turn off the display
 */
void Display_Control_Turn_Off(void)
{
    if (display_control.state == DISPLAY_STATE_ON) {
        display_control.state = DISPLAY_STATE_TURNING_OFF;
        PWM_Turn_Off();
    }
}

/**
 * @brief Toggle display state
 */
void Display_Control_Toggle(void)
{
    if (display_control.state == DISPLAY_STATE_OFF) {
        Display_Control_Turn_On();
    } else if (display_control.state == DISPLAY_STATE_ON) {
        Display_Control_Turn_Off();
    }
}

/**
 * @brief Set flag to indicate content needs updating
 */
void Display_Control_Set_Content_Update_Flag(void)
{
    display_control.content_needs_update = 1;
}

/**
 * @brief Check if display is currently on
 * @return 1 if display is on, 0 if off
 */
uint8_t Display_Control_Is_On(void)
{
    return (display_control.state == DISPLAY_STATE_ON || 
            display_control.state == DISPLAY_STATE_TURNING_ON);
}

/**
 * @brief Get current display state
 * @return Current display state
 */
DisplayState_t Display_Control_Get_State(void)
{
    return display_control.state;
}

/**
 * @brief Clear the entire screen
 */
void Display_Control_Clear_Screen(void)
{
    tft_fill_rect(0, 0, ST7735_WIDTH, ST7735_HEIGHT, BLACK);
}

/**
 * @brief Show startup message
 */
void Display_Control_Show_Startup_Message(void)
{
    Display_Control_Clear_Screen();
    
    // Set text properties
    tft_set_color(WHITE);
    tft_set_background_color(BLACK);
    
    // Display title
    tft_set_cursor(10, 10);
    tft_print("CH32V003 ADC");
    
    tft_set_cursor(10, 25);
    tft_print("Monitor v1.0");
    
    // Display instructions
    tft_set_cursor(5, 45);
    tft_set_color(YELLOW);
    tft_print("Touch: Turn On");
    
    tft_set_cursor(5, 60);
    tft_print("Hold: Toggle");
}

/**
 * @brief Show display off message
 */
void Display_Control_Show_Off_Message(void)
{
    Display_Control_Clear_Screen();
    
    // Set text properties
    tft_set_color(DARKGREY);
    tft_set_background_color(BLACK);
    
    // Display off message
    tft_set_cursor(20, 35);
    tft_print("Display Off");
}
