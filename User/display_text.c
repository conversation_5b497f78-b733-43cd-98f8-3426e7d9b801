/**
 * @file display_text.c
 * @brief Text display functions for ST7735 monitor
 * <AUTHOR> Project
 * @date 2024
 */

#include "display_text.h"
#include "st7735.h"

/**
 * @brief Display custom text on the monitor
 * @param text Text string to display
 * @param x X position for text
 * @param y Y position for text
 * @param color Text color
 * @param bg_color Background color
 * @param clear_screen Whether to clear screen before displaying text
 */
void Display_Text_Custom(const char* text, uint16_t x, uint16_t y, 
                        uint16_t color, uint16_t bg_color, uint8_t clear_screen)
{
    // Set text colors
    tft_set_color(color);
    tft_set_background_color(bg_color);
    
    // Clear the screen if requested
    if (clear_screen) {
        tft_fill_rect(0, 0, ST7735_WIDTH, ST7735_HEIGHT, bg_color);
    }
    
    // Set cursor position
    tft_set_cursor(x, y);
    
    // Display the text
    tft_print(text);
}

/**
 * @brief Display centered text on the monitor
 * @param text Text string to display
 * @param y Y position for text (0 for center)
 * @param color Text color
 * @param bg_color Background color
 * @param clear_screen Whether to clear screen before displaying text
 */
void Display_Text_Centered(const char* text, uint16_t y, 
                          uint16_t color, uint16_t bg_color, uint8_t clear_screen)
{
    // Calculate text length and center position
    uint16_t text_len = 0;
    const char* ptr = text;
    while (*ptr++) text_len++;  // Simple strlen
    
    uint16_t x = (ST7735_WIDTH - text_len * 6) / 2;  // 6 pixels per character
    
    // Use center Y position if y is 0
    if (y == 0) {
        y = ST7735_HEIGHT / 2 - 4;  // Center vertically
    }
    
    // Display the text
    Display_Text_Custom(text, x, y, color, bg_color, clear_screen);
}

/**
 * @brief Clear the entire screen
 * @param color Fill color
 */
void Display_Text_Clear_Screen(uint16_t color)
{
    tft_fill_rect(0, 0, ST7735_WIDTH, ST7735_HEIGHT, color);
}

/**
 * @brief Display a simple welcome message
 */
void Display_Text_Welcome(void)
{
    // Clear screen with black background
    Display_Text_Clear_Screen(BLACK);
    
    // Display title
    Display_Text_Centered("CH32V003", 20, CYAN, BLACK, 0);
    
    // Display subtitle
    Display_Text_Centered("ADC Monitor", 35, WHITE, BLACK, 0);
    
    // Display hello message
    Display_Text_Centered("hello", 50, YELLOW, BLACK, 0);
}
