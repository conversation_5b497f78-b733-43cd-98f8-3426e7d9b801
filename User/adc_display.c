/**
 * @file adc_display.c
 * @brief ADC Data Display Implementation
 * <AUTHOR> for CH32V003 Project
 * @date 2025
 */

#include "adc_display.h"
#include "touch_button.h"
#include "debug.h"
#include <stdio.h>
#include <string.h>

// Global ADC display configuration
ADC_DisplayConfig_t adc_display_config = {0};

/**
 * @brief Initialize ADC display system
 */
void ADC_Display_Init(void)
{
    // Initialize display configuration
    adc_display_config.show_raw_values = 0;
    adc_display_config.show_voltage = 1;
    adc_display_config.show_percentage = 1;
    adc_display_config.decimal_places = 2;
    adc_display_config.update_interval_ms = 100; // 10 Hz update rate
    adc_display_config.last_update_time = 0;
    
    // Draw initial display layout
    ADC_Display_Draw_Header();
    ADC_Display_Draw_Channel_Labels();
}

/**
 * @brief Update ADC display with new data
 * @param adc_data Pointer to ADC data structure
 * @param mode Display update mode
 */
void ADC_Display_Update(ADC_Data_t* adc_data, ADC_DisplayMode_t mode)
{
    if (adc_data == NULL) return;
    
    // Check if update is needed
    if (!ADC_Display_Should_Update()) return;
    
    switch (mode) {
        case ADC_DISPLAY_MODE_FULL:
            ADC_Display_Draw_Header();
            ADC_Display_Draw_Channel_Labels();
            ADC_Display_Draw_All_Channels(adc_data);
            break;
            
        case ADC_DISPLAY_MODE_VALUES_ONLY:
            ADC_Display_Clear_Values_Area();
            ADC_Display_Draw_All_Channels(adc_data);
            break;
            
        case ADC_DISPLAY_MODE_SINGLE_CHANNEL:
            // For now, update all channels
            ADC_Display_Draw_All_Channels(adc_data);
            break;
    }
    
    adc_display_config.last_update_time = Touch_Button_Get_Time_Ms();
}

/**
 * @brief Draw display header
 */
void ADC_Display_Draw_Header(void)
{
    tft_set_color(WHITE);
    tft_set_background_color(ADC_BACKGROUND_COLOR);
    
    tft_set_cursor(ADC_DISPLAY_START_X, 0);
    tft_print("ADC Monitor");
}

/**
 * @brief Draw channel labels
 */
void ADC_Display_Draw_Channel_Labels(void)
{
    uint16_t y_pos;
    
    tft_set_background_color(ADC_BACKGROUND_COLOR);
    
    for (uint8_t i = 0; i < ADC_NUM_CHANNELS; i++) {
        y_pos = ADC_DISPLAY_START_Y + 15 + (i * ADC_CHANNEL_HEIGHT);
        
        // Set channel color
        tft_set_color(ADC_Display_Get_Channel_Color(i));
        
        // Draw channel label
        tft_set_cursor(ADC_DISPLAY_START_X, y_pos);
        tft_print("CH");
        tft_print_number(i + 1, 1);
        tft_print(":");
    }
}

/**
 * @brief Draw data for a specific channel
 * @param channel Channel number (0-3)
 * @param adc_data Pointer to ADC data structure
 */
void ADC_Display_Draw_Channel_Data(uint8_t channel, ADC_Data_t* adc_data)
{
    if (channel >= ADC_NUM_CHANNELS || adc_data == NULL) return;
    
    uint16_t y_pos = ADC_DISPLAY_START_Y + 15 + (channel * ADC_CHANNEL_HEIGHT);
    char buffer[16];
    
    tft_set_color(ADC_VALUE_COLOR);
    tft_set_background_color(ADC_BACKGROUND_COLOR);
    
    // Display voltage
    if (adc_display_config.show_voltage) {
        tft_set_cursor(ADC_VALUE_COLUMN_X - 20, y_pos);
        ADC_Display_Format_Voltage(adc_data->voltage_mv[channel], buffer, sizeof(buffer));
        tft_print(buffer);
    }
    
    // Display percentage
    if (adc_display_config.show_percentage) {
        tft_set_cursor(ADC_VALUE_COLUMN_X + 30, y_pos);
        ADC_Display_Format_Percentage(adc_data->raw_values[channel], buffer, sizeof(buffer));
        tft_print(buffer);
    }
    
    // Display raw value (if enabled)
    if (adc_display_config.show_raw_values) {
        tft_set_cursor(ADC_VALUE_COLUMN_X + 60, y_pos);
        tft_print_number(adc_data->raw_values[channel], 4);
    }
}

/**
 * @brief Draw data for all channels
 * @param adc_data Pointer to ADC data structure
 */
void ADC_Display_Draw_All_Channels(ADC_Data_t* adc_data)
{
    for (uint8_t i = 0; i < ADC_NUM_CHANNELS; i++) {
        ADC_Display_Draw_Channel_Data(i, adc_data);
    }
}

/**
 * @brief Clear the values area of the display
 */
void ADC_Display_Clear_Values_Area(void)
{
    // Clear the area where values are displayed
    tft_fill_rect(ADC_VALUE_COLUMN_X - 25, ADC_DISPLAY_START_Y + 15, 
                  ST7735_WIDTH - (ADC_VALUE_COLUMN_X - 25), 
                  ADC_NUM_CHANNELS * ADC_CHANNEL_HEIGHT, 
                  ADC_BACKGROUND_COLOR);
}

/**
 * @brief Set display configuration
 * @param show_raw Show raw ADC values
 * @param show_voltage Show voltage values
 * @param show_percentage Show percentage values
 */
void ADC_Display_Set_Config(uint8_t show_raw, uint8_t show_voltage, uint8_t show_percentage)
{
    adc_display_config.show_raw_values = show_raw;
    adc_display_config.show_voltage = show_voltage;
    adc_display_config.show_percentage = show_percentage;
}

/**
 * @brief Check if display should be updated based on timing
 * @return 1 if update is needed, 0 otherwise
 */
uint8_t ADC_Display_Should_Update(void)
{
    uint32_t current_time = Touch_Button_Get_Time_Ms();
    return (current_time - adc_display_config.last_update_time) >= adc_display_config.update_interval_ms;
}

/**
 * @brief Get color for specific channel
 * @param channel Channel number (0-3)
 * @return Color value for the channel
 */
uint16_t ADC_Display_Get_Channel_Color(uint8_t channel)
{
    switch (channel) {
        case 0: return ADC_CHANNEL_1_COLOR;
        case 1: return ADC_CHANNEL_2_COLOR;
        case 2: return ADC_CHANNEL_3_COLOR;
        case 3: return ADC_CHANNEL_4_COLOR;
        default: return WHITE;
    }
}

/**
 * @brief Format voltage value for display
 * @param voltage_mv Voltage in millivolts
 * @param buffer Output buffer
 * @param buffer_size Size of output buffer
 */
void ADC_Display_Format_Voltage(uint16_t voltage_mv, char* buffer, uint8_t buffer_size)
{
    if (buffer == NULL) return;
    
    uint16_t volts = voltage_mv / 1000;
    uint16_t millivolts = voltage_mv % 1000;
    
    snprintf(buffer, buffer_size, "%d.%02dV", volts, millivolts / 10);
}

/**
 * @brief Format percentage value for display
 * @param raw_value Raw ADC value
 * @param buffer Output buffer
 * @param buffer_size Size of output buffer
 */
void ADC_Display_Format_Percentage(uint16_t raw_value, char* buffer, uint8_t buffer_size)
{
    if (buffer == NULL) return;

    uint16_t percentage = (uint16_t)((uint32_t)raw_value * 100 / ADC_RESOLUTION);
    snprintf(buffer, buffer_size, "%d%%", percentage);
}

/**
 * @brief Draw logo-style display similar to the reference image
 * @param adc_data Pointer to ADC data structure
 */
void ADC_Display_Draw_Logo_Style(ADC_Data_t* adc_data)
{
    // Clear screen first
    tft_fill_rect(0, 0, ST7735_WIDTH, ST7735_HEIGHT, LOGO_BORDER_COLOR);

    // Draw the main display area (black rectangle with border)
    ADC_Display_Draw_Logo_Border();

    // Draw header/logo area
    ADC_Display_Draw_Logo_Header();

    // Draw channel indicators
    ADC_Display_Draw_Logo_Channels(adc_data);

    // Draw main voltage display (use channel 1 as main)
    if (adc_data != NULL && adc_data->channel_ready[0]) {
        ADC_Display_Draw_Logo_Main_Voltage(adc_data->voltage_mv[0]);
    }
}

/**
 * @brief Draw the border and main display area
 */
void ADC_Display_Draw_Logo_Border(void)
{
    // Draw outer green border (3 pixel border)
    tft_fill_rect(0, 0, ST7735_WIDTH, ST7735_HEIGHT, LOGO_BORDER_COLOR);

    // Draw inner black display area
    tft_fill_rect(3, 3, ST7735_WIDTH - 6, ST7735_HEIGHT - 6, LOGO_BACKGROUND_COLOR);

    // Draw a thin cyan line around the display area
    // Top line
    tft_fill_rect(5, 5, ST7735_WIDTH - 10, 1, LOGO_TEXT_COLOR);
    // Bottom line
    tft_fill_rect(5, ST7735_HEIGHT - 6, ST7735_WIDTH - 10, 1, LOGO_TEXT_COLOR);
    // Left line
    tft_fill_rect(5, 5, 1, ST7735_HEIGHT - 10, LOGO_TEXT_COLOR);
    // Right line
    tft_fill_rect(ST7735_WIDTH - 6, 5, 1, ST7735_HEIGHT - 10, LOGO_TEXT_COLOR);
}

/**
 * @brief Draw header/logo area
 */
void ADC_Display_Draw_Logo_Header(void)
{
    tft_set_color(LOGO_TEXT_COLOR);
    tft_set_background_color(LOGO_BACKGROUND_COLOR);

    // Draw logo/brand text
    tft_set_cursor(10, 10);
    tft_print("CH32V");

    tft_set_cursor(10, 20);
    tft_print("LOGO");

    // Draw some decorative lines
    tft_fill_rect(60, 12, 30, 1, LOGO_TEXT_COLOR);
    tft_fill_rect(60, 15, 25, 1, LOGO_TEXT_COLOR);
    tft_fill_rect(60, 18, 20, 1, LOGO_TEXT_COLOR);
}

/**
 * @brief Draw channel indicators on the left side
 * @param adc_data Pointer to ADC data structure
 */
void ADC_Display_Draw_Logo_Channels(ADC_Data_t* adc_data)
{
    if (adc_data == NULL) return;

    tft_set_color(LOGO_CHANNEL_COLOR);
    tft_set_background_color(LOGO_BACKGROUND_COLOR);

    char buffer[8];

    // Draw 4 channel indicators vertically on the left
    for (uint8_t i = 0; i < 4; i++) {
        uint16_t y_pos = 35 + (i * 10);

        // Draw channel number
        tft_set_cursor(10, y_pos);
        snprintf(buffer, sizeof(buffer), "CH%d", i + 1);
        tft_print(buffer);

        // Draw voltage value next to channel
        tft_set_cursor(35, y_pos);
        if (adc_data->channel_ready[i]) {
            ADC_Display_Format_Voltage(adc_data->voltage_mv[i], buffer, sizeof(buffer));
            tft_print(buffer);
        } else {
            tft_print("-.--V");
        }
    }
}

/**
 * @brief Draw the main voltage display (large text)
 * @param voltage_mv Voltage in millivolts to display
 */
void ADC_Display_Draw_Logo_Main_Voltage(uint16_t voltage_mv)
{
    tft_set_color(LOGO_VOLTAGE_COLOR);
    tft_set_background_color(LOGO_BACKGROUND_COLOR);

    char buffer[12];

    // Format voltage with 3 decimal places for main display
    uint16_t volts = voltage_mv / 1000;
    uint16_t millivolts = voltage_mv % 1000;

    snprintf(buffer, sizeof(buffer), "%d.%03dV", volts, millivolts);

    // Position for large voltage display (right side)
    tft_set_cursor(100, 45);
    tft_print(buffer);

    // Draw a small indicator dot
    tft_fill_rect(145, 50, 2, 2, LOGO_TEXT_COLOR);
}
