/********************************** (C) COPYRIGHT *******************************
 * File Name          : main.c
 * Author             : WCH / Modified for ADC Monitor Project
 * Version            : V2.0.0
 * Date               : 2025/08/25
 * Description        : CH32V003 ADC Monitor with ST7735 Display and Touch Control
 *********************************************************************************
 * Copyright (c) 2021 Nanjing Qinheng Microelectronics Co., Ltd.
 * Attention: This software (modified or not) and binary are used for
 * microcontroller manufactured by Nanjing Qinheng Microelectronics.
 *******************************************************************************/

/*
 * @Note
 * CH32V003 ADC Monitor Project:
 * - Measures 4 ADC channels (PA0-PA3)
 * - Displays data on ST7735 80x160 TFT screen
 * - Touch button (TP223) control:
 *   * Touch: Turn on screen (auto-off after 2s)
 *   * Hold 1s: Toggle always-on mode
 *
 * Hardware Connections:
 * ST7735 Display:
 *   PC2 - RESET, PC3 - DC, PC4 - CS, PC5 - SCLK, PC6 - MOSI
 *   PA8 - LEDA (PWM brightness control)
 * Touch Button:
 *   PD7 - TP223 output
 * ADC Inputs:
 *   PD4 - ADC Channel 1, PD3 - ADC Channel 2
 *   PD7 - ADC Channel 3, PC4 - ADC Channel 4
 */

#include "debug.h"
#include "st7735.h"
#include "adc_config.h"
#include "touch_button.h"
#include "pwm_config.h"
#include "display_control.h"
#include "adc_display.h"
#include "display_text.h"

/* Global Variables */
ADC_Data_t adc_data = {0};
uint32_t last_adc_read_time = 0;
uint8_t system_initialized = 0;

static const unsigned char bitmap[1600] = {
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFE,0xFF,0xDD,0xFE,0xED,
0xB6,0xDB,0xBB,0x6D,0xF5,0x5F,0xAA,0xFA,0xAF,0xAA,0xFF,0xFF,0xFF,0xF5,0x4A,0x4B,
0xD5,0x7F,0x77,0xB7,0x6D,0xB6,0xED,0xDB,0xF4,0x96,0x84,0xEC,0x05,0xC0,0x3F,0xFE,
0xFF,0xD0,0x21,0x23,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xBF,0xFE,0xBA,0x0B,0xD1,0x3A,
0xAB,0xEA,0x97,0xFF,0xFF,0xEB,0xAD,0x57,0xFF,0xFF,0xFF,0xDA,0xB6,0xDA,0xEA,0xAF,
0xDC,0xA7,0xEA,0x5E,0x92,0xE9,0x2F,0xFB,0xBD,0xBD,0x77,0xBB,0xFF,0xFF,0xFF,0xBF,
0xFF,0x7F,0xFF,0xF7,0xBE,0x91,0x68,0x97,0x44,0xBA,0x4B,0xFE,0xFF,0xF7,0xDD,0xEF,
0xFF,0xFF,0xFF,0xEA,0xD5,0xD6,0xB6,0xDA,0xEB,0x44,0xF5,0x17,0xA1,0x79,0x0B,0xFF,
0xFA,0xDF,0x77,0x7F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xA2,0x5C,0x4B,
0xD4,0x2E,0x51,0xBD,0xBF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xF5,0xD5,0x3E,0x91,0x69,0x3F,0x84,0xFE,0xF7,0xFF,0xFF,0xFB,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFE,0xE0,0x97,0x25,0xF4,0x87,0x52,0x7F,0xBE,0xFD,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFA,0x4F,0x88,0x7A,0x2B,0xC8,0xAD,
0xEF,0xD6,0xAB,0x57,0x6E,0xED,0x7F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFE,0xBA,0x12,0xE2,
0x5D,0x12,0xE2,0x1E,0xBF,0xC9,0x3D,0xFD,0xBB,0xBB,0xBF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xDD,0x45,0xD4,0x2E,0xA2,0xF9,0x4D,0xFF,0xA5,0x57,0xEF,0xFF,0xFE,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xEE,0xAB,0x73,0x5F,0xAA,0xFD,0x2E,0xDF,0x5E,0x5F,0xBF,
0xFD,0xF7,0xBD,0xFF,0xFF,0xFF,0xFF,0xFF,0xFA,0xD7,0xB5,0xFD,0xED,0xDD,0xAE,0xF7,
0x7F,0xA5,0x7E,0xF4,0x96,0xBF,0xBE,0xFF,0xFF,0xFF,0xFF,0xFF,0xED,0xFA,0xDE,0xAB,
0x5B,0x6B,0x75,0xAA,0xFF,0x97,0x3C,0xF5,0xDD,0xA7,0x7A,0xFF,0xFF,0xFF,0xFF,0xFF,
0xF5,0x7F,0x77,0xDE,0xEE,0xDE,0xDF,0x7D,0xDF,0xC2,0xBA,0xB6,0xDD,0xB7,0xBD,0xFF,
0xFF,0xFF,0xFF,0xFF,0xF2,0xFD,0xFE,0xFF,0xFF,0xFF,0xFD,0xF6,0x7F,0x4B,0x3A,0xEA,
0xDD,0xCF,0xBD,0xFF,0xFF,0xFF,0xFF,0xFF,0xF5,0x7F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFB,
0xEF,0xA6,0xBE,0xB5,0xDE,0xEF,0xBC,0xFF,0xFF,0xFF,0xFF,0xFF,0xF5,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFD,0xFF,0x93,0x5F,0xFF,0xFF,0xFF,0x7E,0xFF,0xFF,0xFF,0xFF,0xFF,
0xF2,0x7F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFA,0x6F,0xC4,0x5F,0xFE,0xFB,0xFD,0xBA,0xFF,
0xFF,0xFF,0xFF,0xFF,0xED,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFD,0xFF,0xAB,0x36,0xDB,
0xEF,0xB7,0xFD,0x7F,0xFF,0xFF,0xFF,0xFF,0xF5,0x7F,0xFF,0xFF,0xBD,0xFF,0xFF,0xF6,
0xBF,0xFD,0xDF,0xFF,0xFF,0xFF,0x7F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFE,0xFF,0xFB,0xFE,
0xFF,0xF7,0xFF,0xFB,0xEF,0xEF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xEF,0xFB,0xFF,0xFF,0xBF,0xFD,0xBF,0xFE,0xFF,0xFF,0xDE,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFE,0xFF,0xEF,0xFF,0xFF,0xFF,0xBA,0xFF,0xAA,0x96,0xD6,
0xFB,0xDA,0xBF,0xFF,0xFF,0xFF,0xEF,0x7B,0xF7,0xEF,0xFF,0xBF,0xFF,0x7F,0xFF,0xFD,
0xBF,0xD2,0x5F,0xFF,0xFF,0xFF,0xFF,0xFF,0xEA,0xAA,0xBB,0xD6,0xAD,0x5A,0xAA,0xF6,
0xAA,0xD5,0x5B,0xFB,0xEF,0x5D,0x7F,0xFF,0xFF,0xFF,0x37,0xFF,0xFF,0xFF,0xEE,0xFF,
0xFF,0xFF,0xFF,0xBF,0xFF,0xFF,0xEF,0xFD,0xFF,0x8B,0xAE,0xF5,0x9A,0xB7,0xDF,0xFF,
0xD7,0x7D,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFD,0xBF,0xF5,0x5F,0xD3,0x5E,0x75,
0x5D,0x5B,0x7F,0xFF,0xFF,0xFF,0xFF,0xFF,0x7F,0xFF,0xFF,0xFF,0xFF,0x7F,0xFF,0xFE,
0xF7,0x45,0x7D,0x75,0xDE,0xAF,0xBF,0xFF,0xEF,0xFF,0xFF,0xFD,0xFE,0xFB,0xFF,0xFE,
0xFB,0xFF,0xDF,0xFA,0xD5,0xAB,0x38,0xB6,0xDA,0xD7,0xBF,0xEF,0x7F,0xFF,0xFF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xEF,0xFB,0xEF,0x96,0xBE,0xD5,0x5B,0xCF,0xBF,0xFF,
0xED,0xFF,0xFF,0xFF,0xFF,0xFF,0xF7,0xFF,0xFF,0xFF,0xBF,0xFD,0xB2,0xDE,0xAF,0x7B,
0xEC,0xFE,0xFF,0xFF,0xFF,0xFF,0xFF,0xDF,0xFF,0xFF,0xFF,0xEF,0xFF,0xFE,0xFF,0xED,
0xED,0xAB,0x5F,0xFF,0xFF,0xEF,0xAF,0xFF,0xEF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xDF,0xFA,0xF3,0xD5,0x35,0xDB,0x7B,0x7A,0xBF,0xFF,0xF7,0xFB,0xEF,0xFF,
0xFF,0xFF,0xFF,0xFF,0xDF,0xFF,0xFF,0xFF,0x6D,0xEA,0xAF,0x77,0xDF,0xDF,0xFF,0xFF,
0xDF,0xFF,0xFF,0x7F,0xFF,0xDF,0xBF,0xFF,0xFF,0xFF,0xDF,0xF9,0xF3,0xBF,0xFF,0xFE,
0xF6,0xFD,0xFF,0xBF,0xFF,0xFF,0xFF,0xFF,0xFD,0xFF,0xFF,0xFE,0xFF,0xF7,0xEE,0xFE,
0xAD,0xFF,0xFD,0xFF,0xFF,0xF7,0xFF,0xFE,0xEF,0xDF,0xFF,0xBF,0xFF,0x7F,0xF5,0xD5,
0xF7,0xFB,0xBF,0xF5,0xF5,0xEA,0xAF,0x56,0xB7,0x5E,0xBF,0xFF,0xF7,0xA1,0xFA,0x5F,
0xF4,0x17,0xEB,0xE0,0x33,0xB5,0xFF,0xFD,0xEB,0xAA,0x55,0xFB,0xDD,0xF7,0x7F,0xFF,
0xEF,0xCB,0xF8,0x5F,0xD9,0x8F,0xD3,0xEB,0xF3,0xF3,0xDF,0xFA,0x75,0xA4,0x9F,0xEF,
0xFF,0xFE,0xBF,0xFF,0xFF,0xB1,0xEB,0xFF,0xF3,0xEF,0xC1,0xCF,0xF9,0xEB,0xEF,0xFF,
0xD5,0x9B,0x3F,0x7E,0xFD,0xBB,0xDF,0xFF,0xEF,0xFA,0xF1,0x7F,0xF5,0xC7,0x57,0xE7,
0xF5,0xE7,0xFF,0xF5,0xF5,0xCD,0xBD,0x75,0x95,0x6F,0xBF,0x7F,0xF7,0xF3,0xF4,0xAF,
0xEB,0xEB,0x91,0xA4,0xB8,0xE7,0xBF,0xDD,0xB5,0xA6,0xB6,0xF5,0x5A,0xB7,0xFF,0xFF,
0xDF,0xF9,0xE1,0x1F,0xF3,0xE6,0x7B,0xD2,0x7D,0xD7,0xEF,0xFD,0xEB,0x97,0x5C,0xB6,
0xD7,0x4B,0x5F,0xFD,0xFF,0xF5,0xF3,0xCF,0xE5,0xEB,0x29,0xFE,0x3A,0x4F,0xDF,0xFA,
0xF5,0xAB,0x7A,0x75,0xD9,0x57,0xBF,0xFF,0xEF,0xF9,0xE7,0xE7,0xB3,0xE4,0x22,0xFF,
0x9C,0xAF,0xFF,0xFF,0xB5,0x8B,0x5F,0xB5,0xDD,0xEF,0xDF,0xFF,0xF7,0xF3,0xF3,0xCF,
0xF7,0xCE,0x90,0xFF,0x3E,0x9F,0xDF,0xF9,0xEA,0xD5,0xBE,0xFF,0x7F,0xFF,0x7F,0xFF,
0xEF,0xF9,0xEB,0xCE,0xF1,0xE7,0x6B,0xFE,0xBE,0x1F,0xEF,0xFE,0xF5,0xDE,0x5F,0xFF,
0xFF,0x7F,0xBF,0xFF,0xFF,0xF5,0xF1,0x2E,0x7A,0x8F,0xF9,0xC2,0x3E,0xAF,0xBF,0xDB,
0xB5,0xA2,0x97,0xED,0xF7,0xED,0x7F,0x7F,0xD7,0xF9,0xF8,0x2E,0x78,0x2F,0xF5,0xE9,
0x7F,0x3F,0xEF,0xFA,0xEB,0xFD,0xFF,0x7F,0xDD,0xFF,0xFD,0xFF,0xFF,0xF7,0xFF,0xFF,
0xBF,0xBF,0xFF,0xDD,0xFF,0xFF,0xFF,0xED,0xB5,0xFF,0xBD,0xFF,0xFF,0xFF,0xF7,0xFE,
0xEF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDF,0xB7,0xF5,0xDF,0xF7,0xFD,
0xFF,0xB7,0x7F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xEF,0xDA,
0xDB,0xE9,0x5F,0xAF,0x5A,0xFD,0xBF,0xFF,0xEF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xBE,0xB5,0xEF,0x55,0x2D,0xFB,0xFF,0xD7,0x7F,0xFF,0xFF,0xFF,0xFF,0xDF,
0xFF,0xFF,0xFF,0xFF,0xFF,0xFE,0xFF,0xB6,0x77,0xC5,0xBF,0xBF,0xDB,0xFF,0xBF,0xFF,
0xED,0xFF,0xFF,0x7F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xEF,0x5B,0xEF,0x97,0x5E,0xF4,
0xBC,0xBB,0xBF,0xFF,0xF7,0xFF,0xFB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xDF,0xFF,0xBD,
0xBF,0xCD,0x36,0x7B,0xCE,0xAF,0xBF,0xFF,0xDF,0xFF,0xEF,0xFF,0xFF,0xFF,0xEF,0xFF,
0xFF,0xFF,0xDA,0xD5,0xFF,0x5B,0xBD,0x6C,0xBE,0xD7,0x6F,0xFF,0xFF,0xF7,0xBF,0xFF,
0xFF,0xFF,0xBF,0xFF,0xFE,0xFF,0xEF,0x7E,0xDF,0xBF,0x5C,0xB7,0x5A,0xD7,0xBF,0xFF,
0xEF,0xFF,0xFF,0xFF,0xDF,0xFE,0xFF,0xBF,0xFF,0xFF,0xDF,0xAB,0x7F,0xD5,0xBA,0xB4,
0xED,0x6F,0xDF,0xFD,0xF7,0xFF,0xFF,0xFF,0xFE,0xF7,0xFF,0xFE,0xF7,0xFF,0xFF,0x7D,
0xFE,0xAB,0xBF,0xFB,0xDA,0xEE,0xBF,0xF7,0xEF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFF,0xAF,0xAA,0xDB,0xC5,0x5F,0xFF,0xFF,0xFF,0xFF,0xDF,0xFF,0xBF,0xFF,0xFE,
0xFF,0xFF,0xFF,0xFB,0xFF,0xFB,0xFE,0xBD,0x7F,0xD1,0x2F,0xBF,0xFF,0xFF,0x5F,0xFF,
0xEB,0xED,0xBF,0x6B,0xDB,0xBB,0xF6,0xDF,0xBB,0x6E,0xDF,0xB7,0xEF,0xD5,0x5D,0xF6,
0xF6,0xDB,0xBF,0xFF,0xFE,0xBB,0x6A,0xFE,0xEE,0xD6,0xAD,0xB5,0x6D,0xBB,0x7E,0xDD,
0xF7,0xFF,0xFF,0xFF,0xDF,0xFF,0xFF,0xFF,0xF7,0xD7,0x55,0x95,0x55,0x5A,0xD6,0xAA,
0x95,0x55,0x57,0x7A,0x6F,0xFD,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xED,0x52,0xAA,
0xAA,0xAA,0xAA,0xB5,0xAD,0x6B,0x5F,0xAD,0xF5,0xFF,0xDF,0xFF,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFB,0x8A,0xD6,0xB7,0xBD,0xEE,0xB6,0xDA,0xEB,0x5F,0x7B,0xAE,0xDF,0xFE,0xEE,
0xFF,0xFB,0xBD,0xBF,0x7F,0x7E,0xE3,0x6E,0xF5,0xAD,0x6E,0xF5,0xAE,0xD7,0x5F,0xAD,
0xFB,0x6A,0xAB,0x5B,0x55,0x4D,0x56,0xD1,0xA2,0xA5,0xB5,0xDB,0x77,0x5D,0xD7,0x3B,
0xDD,0x77,0x3F,0x7B,0xFD,0x55,0x55,0x6A,0xAA,0xB5,0x6A,0xAE,0xAD,0xB6,0xD1,0x56,
0xAA,0xB5,0x6D,0x6A,0xAD,0xD5,0xBF,0x5D,0x6F,0xB5,0x6A,0xAA,0xD5,0x55,0xAA,0xAA,
0xB5,0x5B,0x7E,0xED,0x75,0xDA,0xB6,0xAB,0x55,0x56,0xAF,0xB6,0xFE,0xEA,0x95,0x25,
0x2A,0x48,0x12,0x41,0x02,0x0D,0xAB,0x5B,0xAE,0xAF,0xDB,0xFD,0xFF,0x7B,0xFE,0xDB,
0xBB,0xB4,0x00,0x90,0x01,0x12,0xA4,0x94,0xA9,0x52,0xFD,0xEE,0xFB,0xF5,0x76,0xAE,
0xAB,0xDE,0xB7,0x6D,0xEF,0xDD,0xB5,0x4B,0x6C,0xD5,0x55,0x55,0x55,0x57,0x5F,0x7B,
0xAE,0xDF,0xDF,0xFB,0xFE,0xF7,0xFF,0xB7,0x7F,0x7F,0x7F,0xFF,0xFF,0xBF,0xFF,0xFF,
0xFF,0xFD,0xFB,0xFF,0xFF,0xFF,0xFF,0x7F,0xFF,0xDF,0xDD,0xFE,0xFF,0xFB,0xED,0xBB,
0x6E,0xF6,0xD6,0xDD,0xB7,0x77,0xFF,0xDD,0xDD,0xBA,0xB5,0xD6,0xAB,0x7A,0xF7,0xDB
};


/*********************************************************************
 * @fn      System_Init
 *
 * @brief   Initialize all system components
 *
 * @return  none
 */
void System_Init(void)
{
    // Initialize ADC system
    // ADC_Config_Init();
    printf("ADC initialized\r\n");

    // Initialize PWM for display brightness
    PWM_Config_Init();
    printf("PWM initialized\r\n");

    // Initialize touch button
    Touch_Button_Init();
    printf("Touch button initialized\r\n");

    // Initialize display control
    Display_Control_Init();
    printf("Display control initialized\r\n");

    // Initialize ADC display interface
    ADC_Display_Init();
    printf("ADC display initialized\r\n");

    // System is ready
    system_initialized = 1;
    printf("System initialization complete\r\n");
}

/*********************************************************************
 * @fn      main
 *
 * @brief   Main program - ADC Monitor with Touch Control
 *
 * @return  none
 */
int main(void)
{
    // Basic system initialization
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_1);
    SystemCoreClockUpdate();
    Delay_Init();

#if (SDI_PRINT == SDI_PR_OPEN)
    SDI_Printf_Enable();
#else
    USART_Printf_Init(115200);
#endif

    printf("\r\n=== CH32V003 ADC Monitor ===\r\n");
    printf("SystemClk: %d Hz\r\n", SystemCoreClock);
    printf("ChipID: %08x\r\n", DBGMCU_GetCHIPID());

    // Initialize all system components
    System_Init();

    // Show welcome message briefly
    Display_Text_Welcome();
    Delay_Ms(2000);

    // Demo the new logo-style display
    ADC_Data_t demo_data = {0};
    // Set some demo values
    demo_data.voltage_mv[0] = 16045;  // 16.045V
    demo_data.voltage_mv[1] = 3300;   // 3.300V
    demo_data.voltage_mv[2] = 1650;   // 1.650V
    demo_data.voltage_mv[3] = 5000;   // 5.000V
    for (int i = 0; i < 4; i++) {
        demo_data.channel_ready[i] = 1;
        demo_data.raw_values[i] = (demo_data.voltage_mv[i] * ADC_RESOLUTION) / ADC_VREF_MV;
    }

    // Display the logo-style interface
    ADC_Display_Draw_Logo_Style(&demo_data);

    // Main application loop
    while(1)
    {
        // Update touch button state machine
        Touch_Button_Update();

        // Handle touch button events
        TouchEvent_t touch_event = Touch_Button_Get_Event();
        switch (touch_event) {
            case TOUCH_EVENT_SHORT_PRESS:
                printf("Touch: Short press - turning on display\r\n");
                Display_Control_Turn_On();
                break;

            case TOUCH_EVENT_LONG_PRESS:
                printf("Touch: Long press - toggling display mode\r\n");
                Display_Control_Toggle();
                break;

            case TOUCH_EVENT_TIMEOUT:
                printf("Touch: Timeout - turning off display\r\n");
                Display_Control_Turn_Off();
                break;

            default:
                break;
        }

        // Update display control
        Display_Control_Update();

        // Read ADC data periodically (every 100ms)
        // uint32_t current_time = Touch_Button_Get_Time_Ms();
        // if (current_time - last_adc_read_time >= 100) {
        //     ADC_Read_All_Channels(&adc_data);
        //     last_adc_read_time = current_time;

        //     // Update display if screen is on
        //     if (Display_Control_Is_On()) {
        //         ADC_Display_Update(&adc_data, ADC_DISPLAY_MODE_VALUES_ONLY);
        //     }
        // }

        // Small delay to prevent excessive CPU usage
        Delay_Ms(1);
    }
}
