/**
 * @file adc_config.h
 * @brief ADC Configuration for CH32V003 - 4 Channel ADC Reading
 * <AUTHOR> for CH32V003 Project
 * @date 2025
 */

#ifndef __ADC_CONFIG_H__
#define __ADC_CONFIG_H__

#include "ch32v00x_conf.h"

// ADC Configuration Constants
#define ADC_NUM_CHANNELS    4
#define ADC_RESOLUTION      4096    // 12-bit ADC
#define ADC_VREF_MV         3300    // 3.3V reference voltage in mV

// ADC Channel Definitions (Mixed GPIO ports)
#define ADC_CHANNEL_1       ADC_Channel_4   // PD4
#define ADC_CHANNEL_2       ADC_Channel_3   // PD3
#define ADC_CHANNEL_3       ADC_Channel_7   // PD7
#define ADC_CHANNEL_4       ADC_Channel_2   // PC4

// GPIO Pin Definitions for ADC inputs (Mixed ports)
#define ADC_GPIO_PORT_D     GPIOD
#define ADC_GPIO_PORT_C     GPIOC
#define ADC_PIN_1           GPIO_Pin_4      // ADC Channel 1 (PD4)
#define ADC_PIN_2           GPIO_Pin_3      // ADC Channel 2 (PD3)
#define ADC_PIN_3           GPIO_Pin_7      // ADC Channel 3 (PD7)
#define ADC_PIN_4           GPIO_Pin_4      // ADC Channel 4 (PC4)

// ADC Data Structure
typedef struct {
    uint16_t raw_values[ADC_NUM_CHANNELS];      // Raw ADC values (0-4095)
    uint16_t voltage_mv[ADC_NUM_CHANNELS];      // Converted voltage in mV
    uint8_t channel_ready[ADC_NUM_CHANNELS];    // Channel data ready flags
} ADC_Data_t;

// Function Prototypes
void ADC_Config_Init(void);
void ADC_GPIO_Config(void);
uint16_t ADC_Read_Channel(uint8_t channel);
void ADC_Read_All_Channels(ADC_Data_t* adc_data);
uint16_t ADC_Convert_To_Voltage(uint16_t raw_value);
void ADC_Start_Conversion(uint8_t channel);
uint8_t ADC_Is_Conversion_Done(void);

#endif // __ADC_CONFIG_H__
