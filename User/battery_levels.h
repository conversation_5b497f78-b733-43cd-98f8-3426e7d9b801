/**
 * @file battery_levels.h
 * @brief Battery level icons with different fill levels and colors
 * <AUTHOR> Project
 * @date 2025
 */

#ifndef __BATTERY_LEVELS_H__
#define __BATTERY_LEVELS_H__

#include <stdint.h>
#include "picts.h"
#include "st7735.h"

// Battery level definitions
#define BATTERY_LEVEL_0     0   // 0% - Empty (Red)
#define BATTERY_LEVEL_20    1   // 20% - Low (Orange)
#define BATTERY_LEVEL_40    2   // 40% - Medium-Low (Yellow)
#define BATTERY_LEVEL_60    3   // 60% - Medium (More Yellow)
#define BATTERY_LEVEL_80    4   // 80% - High (Green)
#define BATTERY_LEVEL_100   5   // 100% - Full (More Green)

#define BATTERY_LEVELS_COUNT 6

// External declarations
extern const tImage battery_levels[BATTERY_LEVELS_COUNT];
extern const uint16_t battery_colors[BATTERY_LEVELS_COUNT];

// Function prototypes
uint8_t Get_Battery_Level_From_Voltage(uint16_t voltage_mv);
void Draw_Battery_Level(uint16_t x, uint16_t y, uint16_t voltage_mv, uint16_t bg_color);

#endif // __BATTERY_LEVELS_H__
