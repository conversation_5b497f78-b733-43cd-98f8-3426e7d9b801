/**
 * @file battery_levels.c
 * @brief Battery level icons with different fill levels and colors
 * <AUTHOR> Project
 * @date 2025
 */

#include <stdint.h>
#include "battery_levels.h"


static const uint8_t image_battery_0[86] = {
  0x3f,0xff,0xff,0xff,0x01,0xff,0xff,0xff,0xfe,0x0e,
  0x00,0x00,0x00,0x1c,0x30,0x00,0x00,0x00,0x30,0xc0,
  0x00,0x00,0x00,0xc3,0x00,0x00,0x00,0x03,0x40,0x00,
  0x00,0x00,0x0d,0x80,0x00,0x00,0x00,0x37,0x00,0x00,
  0x00,0x00,0xdf,0x00,0x00,0x00,0x03,0x7c,0x00,0x00,
  0x00,0x0d,0xf0,0x00,0x00,0x00,0x36,0xc0,0x00,0x00,
  0x00,0xd0,0x00,0x00,0x00,0x03,0x0c,0x00,0x00,0x00,
  0x0c,0x38,0x00,0x00,0x00,0x70,0x7f,0xff,0xff,0xff,
  0x80,0xff,0xff,0xff,0xfc,0x0f
};
static const uint8_t image_battery_20[86] = {
  0x3f,0xff,0xff,0xff,0x01,0xff,0xff,0xff,0xfe,0x0e,
  0x00,0x00,0x00,0x1c,0x33,0xc0,0x00,0x00,0x30,0xdf,
  0x00,0x00,0x00,0xc3,0x7c,0x00,0x00,0x03,0x4d,0xf0,
  0x00,0x00,0x0d,0xb7,0xc0,0x00,0x00,0x37,0xdf,0x00,
  0x00,0x00,0xdf,0x7c,0x00,0x00,0x03,0x7d,0xf0,0x00,
  0x00,0x0d,0xf7,0xc0,0x00,0x00,0x36,0xdf,0x00,0x00,
  0x00,0xd3,0x7c,0x00,0x00,0x03,0x0c,0xf0,0x00,0x00,
  0x0c,0x38,0x00,0x00,0x00,0x70,0x7f,0xff,0xff,0xff,
  0x80,0xff,0xff,0xff,0xfc,0x0f
};
static const uint8_t image_battery_40[86] = {
  0x3f,0xff,0xff,0xff,0x01,0xff,0xff,0xff,0xfe,0x0e,
  0x00,0x00,0x00,0x1c,0x33,0xff,0x00,0x00,0x30,0xdf,
  0xfc,0x00,0x00,0xc3,0x7f,0xf0,0x00,0x03,0x4d,0xff,
  0xc0,0x00,0x0d,0xb7,0xff,0x00,0x00,0x37,0xdf,0xfc,
  0x00,0x00,0xdf,0x7f,0xf0,0x00,0x03,0x7d,0xff,0xc0,
  0x00,0x0d,0xf7,0xff,0x00,0x00,0x36,0xdf,0xfc,0x00,
  0x00,0xd3,0x7f,0xf0,0x00,0x03,0x0c,0xff,0xc0,0x00,
  0x0c,0x38,0x00,0x00,0x00,0x70,0x7f,0xff,0xff,0xff,
  0x80,0xff,0xff,0xff,0xfc,0x0f
};
static const uint8_t image_battery_60[86] = {
  0x3f,0xff,0xff,0xff,0x01,0xff,0xff,0xff,0xfe,0x0e,
  0x00,0x00,0x00,0x1c,0x33,0xff,0xfc,0x00,0x30,0xdf,
  0xff,0xf0,0x00,0xc3,0x7f,0xff,0xc0,0x03,0x4d,0xff,
  0xff,0x00,0x0d,0xb7,0xff,0xfc,0x00,0x37,0xdf,0xff,
  0xf0,0x00,0xdf,0x7f,0xff,0xc0,0x03,0x7d,0xff,0xff,
  0x00,0x0d,0xf7,0xff,0xfc,0x00,0x36,0xdf,0xff,0xf0,
  0x00,0xd3,0x7f,0xff,0xc0,0x03,0x0c,0xff,0xff,0x00,
  0x0c,0x38,0x00,0x00,0x00,0x70,0x7f,0xff,0xff,0xff,
  0x80,0xff,0xff,0xff,0xfc,0x0f
};
static const uint8_t image_battery_80[86] = {
  0x3f,0xff,0xff,0xff,0x01,0xff,0xff,0xff,0xfe,0x0e,
  0x00,0x00,0x00,0x1c,0x33,0xff,0xff,0xf0,0x30,0xdf,
  0xff,0xfc,0xc0,0xc3,0x7f,0xff,0xf3,0x03,0x4d,0xff,
  0xff,0xcc,0x0d,0xb7,0xff,0xff,0x30,0x37,0xdf,0xff,
  0xfc,0xc0,0xdf,0x7f,0xff,0xf3,0x03,0x7d,0xff,0xff,
  0xcc,0x0d,0xf7,0xff,0xff,0x30,0x36,0xdf,0xff,0xfc,
  0xc0,0xd3,0x7f,0xff,0xf3,0x03,0x0c,0xff,0xff,0xcc,
  0x0c,0x38,0x00,0x00,0x00,0x70,0x7f,0xff,0xff,0xff,
  0x80,0xff,0xff,0xff,0xfc,0x0f
};
static const uint8_t image_battery_100[86] = {
  0x3f,0xff,0xff,0xff,0x01,0xff,0xff,0xff,0xfe,0x0e,
  0x00,0x00,0x00,0x1c,0x33,0xff,0xff,0xe0,0x30,0xdf,
  0xff,0xff,0x80,0xc3,0x7f,0xff,0xfe,0x03,0x4d,0xff,
  0xff,0xf8,0x0d,0xb7,0xff,0xff,0xe0,0x37,0xdf,0xff,
  0xff,0x80,0xdf,0x7f,0xff,0xfe,0x03,0x7d,0xff,0xff,
  0xf8,0x0d,0xf7,0xff,0xff,0xe0,0x36,0xdf,0xff,0xff,
  0x80,0xd3,0x7f,0xff,0xfe,0x03,0x0c,0xff,0xff,0xf8,
  0x0c,0x38,0x00,0x00,0x00,0x70,0x7f,0xff,0xff,0xff,
  0x80,0xff,0xff,0xff,0xfc,0x0f
};

// Battery level images array
const tImage battery_levels[6] = {
    { image_battery_0,   38, 18, 8 },  // 0% - Empty
    { image_battery_20,  38, 18, 8 },  // 20% - Low
    { image_battery_40,  38, 18, 8 },  // 40% - Medium-Low
    { image_battery_60,  38, 18, 8 },  // 60% - Medium
    { image_battery_80,  38, 18, 8 },  // 80% - High
    { image_battery_100, 38, 18, 8 }   // 100% - Full
};

// Battery level colors (RGB565 format)
const uint16_t battery_colors[6] = {
    RED,     // 0% - Red
    ORANGE,   // 20% - Orange
    YELLOW,   // 40% - Yellow
    GREENYELLOW, // 60% - More Yellow (lighter)
    DARKGREEN,     // 80% - Green
    GREEN    // 100% - More Green (brighter)
};

/**
 * @brief Get battery level index based on voltage
 * @param voltage_mv Total voltage in millivolts
 * @return Battery level index (0-5)
 */
uint8_t Get_Battery_Level_From_Voltage(uint16_t voltage_mv)
{
    // Define voltage thresholds for battery levels
    // Adjust these values based on your application needs
    if (voltage_mv < 5000) {        // < 5.0V
        return BATTERY_LEVEL_0;     // 0% - Red
    } else if (voltage_mv < 10000) { // 5.0V - 10.0V
        return BATTERY_LEVEL_20;    // 20% - Orange
    } else if (voltage_mv < 15000) { // 10.0V - 15.0V
        return BATTERY_LEVEL_40;    // 40% - Yellow
    } else if (voltage_mv < 20000) { // 15.0V - 20.0V
        return BATTERY_LEVEL_60;    // 60% - More Yellow
    } else if (voltage_mv < 25000) { // 20.0V - 25.0V
        return BATTERY_LEVEL_80;    // 80% - Green
    } else {                        // >= 25.0V
        return BATTERY_LEVEL_100;   // 100% - More Green
    }
}

/**
 * @brief Draw battery icon with level and color based on voltage
 * @param x X position
 * @param y Y position
 * @param voltage_mv Total voltage in millivolts
 * @param bg_color Background color
 */
void Draw_Battery_Level(uint16_t x, uint16_t y, uint16_t voltage_mv, uint16_t bg_color)
{
    uint8_t level = Get_Battery_Level_From_Voltage(voltage_mv);
    uint16_t fg_color = battery_colors[level];

    // Draw the battery icon with appropriate level and color
    const tImage* battery_image = &battery_levels[level];
    // Draw pixel by pixel with the appropriate color
    for (uint16_t row = 0; row < battery_image->height; row++) {
        for (uint16_t col = 0; col < battery_image->width; col++) {
            // Calculate bit position in bitmap data
            uint16_t pixel_index = row * battery_image->width + col;
            uint16_t byte_index = pixel_index / 8;
            uint8_t bit_index = 7 - (pixel_index % 8);

            // Check if bit is set
            if (battery_image->data[byte_index] & (1 << bit_index)) {
                // Draw foreground pixel with level-appropriate color
                tft_draw_pixel(x + col, y + row, fg_color);
            } else {
                // Draw background pixel
                tft_draw_pixel(x + col, y + row, bg_color);
            }
        }
    }
}
