/**
 * @file logo_display_example.c
 * @brief Example usage of the logo-style display interface
 * <AUTHOR> Project
 * @date 2025
 */

#include "adc_display.h"
#include "adc_config.h"
#include "st7735.h"
#include "debug.h"

/**
 * @brief Example function to demonstrate the logo-style display
 * This creates a display similar to the reference image with:
 * - Green border
 * - Black display area with cyan accents
 * - Vietnam flag logo in header
 * - Channel indicators (CH1-CH4) with voltages
 * - Large main voltage display
 */
void Logo_Display_Example(void)
{
    // Create sample ADC data
    ADC_Data_t sample_data = {0};
    
    // Set sample voltage values (in millivolts)
    sample_data.voltage_mv[0] = 16045;  // 16.045V - Main display voltage
    sample_data.voltage_mv[1] = 3300;   // 3.300V - CH2
    sample_data.voltage_mv[2] = 1650;   // 1.650V - CH3  
    sample_data.voltage_mv[3] = 5000;   // 5.000V - CH4
    
    // Calculate corresponding raw ADC values
    for (int i = 0; i < ADC_NUM_CHANNELS; i++) {
        sample_data.raw_values[i] = (sample_data.voltage_mv[i] * ADC_RESOLUTION) / ADC_VREF_MV;
        sample_data.channel_ready[i] = 1;  // Mark all channels as ready
    }
    
    // Display the logo-style interface
    ADC_Display_Draw_Logo_Style(&sample_data);
    
    printf("Logo-style display rendered with sample data\r\n");
}

/**
 * @brief Example with live ADC data
 * This function reads actual ADC values and displays them in logo style
 */
void Logo_Display_Live_Example(void)
{
    ADC_Data_t live_data = {0};
    
    // Read actual ADC values from all channels
    ADC_Read_All_Channels(&live_data);
    
    // Display with live data
    ADC_Display_Draw_Logo_Style(&live_data);
    
    printf("Logo-style display updated with live ADC data\r\n");
}

/**
 * @brief Example showing how to update just the main voltage
 * @param new_voltage_mv New voltage value in millivolts
 */
void Logo_Display_Update_Main_Voltage(uint16_t new_voltage_mv)
{
    // Clear the main voltage area
    tft_fill_rect(95, 40, 60, 15, LOGO_BACKGROUND_COLOR);
    
    // Draw updated voltage
    ADC_Display_Draw_Logo_Main_Voltage(new_voltage_mv);
    
    printf("Main voltage updated to: %d.%03dV\r\n", 
           new_voltage_mv / 1000, new_voltage_mv % 1000);
}

/**
 * @brief Example showing animated voltage display
 * This creates a simple animation effect by cycling through voltage values
 */
void Logo_Display_Animation_Example(void)
{
    uint16_t voltages[] = {12000, 13500, 15000, 16045, 18000, 16045};
    uint8_t num_voltages = sizeof(voltages) / sizeof(voltages[0]);
    
    ADC_Data_t anim_data = {0};
    
    // Initialize with base values
    anim_data.voltage_mv[1] = 3300;
    anim_data.voltage_mv[2] = 1650; 
    anim_data.voltage_mv[3] = 5000;
    for (int i = 1; i < 4; i++) {
        anim_data.channel_ready[i] = 1;
        anim_data.raw_values[i] = (anim_data.voltage_mv[i] * ADC_RESOLUTION) / ADC_VREF_MV;
    }
    
    // Animate the main voltage (CH1)
    for (uint8_t i = 0; i < num_voltages; i++) {
        anim_data.voltage_mv[0] = voltages[i];
        anim_data.raw_values[0] = (voltages[i] * ADC_RESOLUTION) / ADC_VREF_MV;
        anim_data.channel_ready[0] = 1;
        
        // Update display
        ADC_Display_Draw_Logo_Style(&anim_data);
        
        // Wait before next update
        Delay_Ms(1000);
        
        printf("Animation step %d: %d.%03dV\r\n", 
               i + 1, voltages[i] / 1000, voltages[i] % 1000);
    }
}

/**
 * @brief Test function to verify all logo display components
 */
void Logo_Display_Test_All_Components(void)
{
    printf("Testing logo display components...\r\n");
    
    // Test 1: Border and background
    printf("1. Testing border and background...\r\n");
    ADC_Display_Draw_Logo_Border();
    Delay_Ms(1000);
    
    // Test 2: Header
    printf("2. Testing header...\r\n");
    ADC_Display_Draw_Logo_Header();
    Delay_Ms(1000);
    
    // Test 3: Channel indicators with sample data
    printf("3. Testing channel indicators...\r\n");
    ADC_Data_t test_data = {0};
    test_data.voltage_mv[0] = 16045;
    test_data.voltage_mv[1] = 3300;
    test_data.voltage_mv[2] = 1650;
    test_data.voltage_mv[3] = 5000;
    for (int i = 0; i < 4; i++) {
        test_data.channel_ready[i] = 1;
    }
    ADC_Display_Draw_Logo_Channels(&test_data);
    Delay_Ms(1000);
    
    // Test 4: Main voltage display
    printf("4. Testing main voltage display...\r\n");
    ADC_Display_Draw_Logo_Main_Voltage(16045);
    Delay_Ms(1000);
    
    printf("All logo display components tested successfully!\r\n");
}

/**
 * @brief Demo function to show Vietnam flag in different sizes
 */
void Logo_Display_Vietnam_Flag_Demo(void)
{
    // Clear screen with black background
    tft_fill_rect(0, 0, ST7735_WIDTH, ST7735_HEIGHT, BLACK);

    printf("Demonstrating Vietnam flag in different sizes...\r\n");

    // Small flag
    ADC_Display_Draw_Vietnam_Flag(10, 10, 20, 12);
    tft_set_color(WHITE);
    tft_set_background_color(BLACK);
    tft_set_cursor(35, 15);
    tft_print("Small");

    // Medium flag
    ADC_Display_Draw_Vietnam_Flag(10, 30, 30, 18);
    tft_set_cursor(45, 35);
    tft_print("Medium");

    // Large flag
    ADC_Display_Draw_Vietnam_Flag(10, 55, 40, 24);
    tft_set_cursor(55, 65);
    tft_print("Large");

    // Extra large flag (right side)
    ADC_Display_Draw_Vietnam_Flag(100, 20, 50, 30);
    tft_set_cursor(105, 55);
    tft_print("XL Flag");

    printf("Vietnam flag demo complete!\r\n");
}
