/**
 * @file display_text.h
 * @brief Text display functions for ST7735 monitor
 * <AUTHOR> Project
 * @date 2024
 */

#ifndef __DISPLAY_TEXT_H__
#define __DISPLAY_TEXT_H__

#include <stdint.h>
#include "st7735.h"

/**
 * @brief Display "hello" text on the monitor
 * @param x X position for text (0 for auto-center)
 * @param y Y position for text (0 for auto-center)
 * @param clear_screen Whether to clear screen before displaying text
 */
void Display_Text_Hello(uint16_t x, uint16_t y, uint8_t clear_screen);

/**
 * @brief Display custom text on the monitor
 * @param text Text string to display
 * @param x X position for text
 * @param y Y position for text
 * @param color Text color
 * @param bg_color Background color
 * @param clear_screen Whether to clear screen before displaying text
 */
void Display_Text_Custom(const char* text, uint16_t x, uint16_t y, 
                        uint16_t color, uint16_t bg_color, uint8_t clear_screen);

/**
 * @brief Display centered text on the monitor
 * @param text Text string to display
 * @param y Y position for text (0 for center)
 * @param color Text color
 * @param bg_color Background color
 * @param clear_screen Whether to clear screen before displaying text
 */
void Display_Text_Centered(const char* text, uint16_t y, 
                          uint16_t color, uint16_t bg_color, uint8_t clear_screen);

/**
 * @brief Clear the entire screen
 * @param color Fill color
 */
void Display_Text_Clear_Screen(uint16_t color);

/**
 * @brief Display a simple welcome message
 */
void Display_Text_Welcome(void);

#endif // __DISPLAY_TEXT_H__
