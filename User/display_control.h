/**
 * @file display_control.h
 * @brief Display Control Logic for ST7735 with PWM Brightness
 * <AUTHOR> for CH32V003 Project
 * @date 2025
 */

#ifndef __DISPLAY_CONTROL_H__
#define __DISPLAY_CONTROL_H__

#include "ch32v00x_conf.h"
#include "st7735.h"
#include "pwm_config.h"
#include "touch_button.h"

// Display States
typedef enum {
    DISPLAY_STATE_OFF = 0,
    DISPLAY_STATE_TURNING_ON,
    DISPLAY_STATE_ON,
    DISPLAY_STATE_TURNING_OFF
} DisplayState_t;

// Display Control Structure
typedef struct {
    DisplayState_t state;
    uint8_t initialized;
    uint8_t content_needs_update;
    uint32_t last_update_time;
} DisplayControl_t;

// Global display control instance
extern DisplayControl_t display_control;

// Function Prototypes
void Display_Control_Init(void);
void Display_Control_Update(void);
void Display_Control_Turn_On(void);
void Display_Control_Turn_Off(void);
void Display_Control_Toggle(void);
void Display_Control_Set_Content_Update_Flag(void);
uint8_t Display_Control_Is_On(void);
DisplayState_t Display_Control_Get_State(void);

// Display content functions
void Display_Control_Clear_Screen(void);

#endif // __DISPLAY_CONTROL_H__
