
/*******************************************************************************
* generated by lcd-image-converter rev.030b30d from 2019-03-17 01:38:34 +0500
* image
* filename: /Users/<USER>/Fonts/In/Image_battery_small.xml
* name: Image_battery_small
*
* preset name: My_Settings
* data block size: 8 bit(s), uint8_t
* RLE compression enabled: no
* conversion type: Monochrome, Diffuse Dither 128
* split to rows: no
* bits per pixel: 1
*
* preprocess:
*  main scan direction: top_to_bottom
*  line scan direction: forward
*  inverse: yes
*******************************************************************************/

/*
 typedef struct {
     const uint8_t *data;
     uint16_t width;
     uint16_t height;
     uint8_t dataSize;
     } tImage;
*/
#include <stdint.h>
#include "picts.h"



static const uint8_t image_data_Image_battery_small[86] = {
    // ∙∙██████████████████████████████∙∙∙∙∙∙
    // ∙████████████████████████████████∙∙∙∙∙
    // ███∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙███∙∙∙∙
    // ██∙∙█████████████████████∙∙∙∙∙∙∙██∙∙∙∙
    // ██∙██████████████████████∙∙∙∙∙∙∙██∙∙∙∙
    // ██∙██████████████████████∙∙∙∙∙∙∙██∙█∙∙
    // ██∙██████████████████████∙∙∙∙∙∙∙██∙██∙
    // ██∙██████████████████████∙∙∙∙∙∙∙██∙███
    // ██∙██████████████████████∙∙∙∙∙∙∙██∙███
    // ██∙██████████████████████∙∙∙∙∙∙∙██∙███
    // ██∙██████████████████████∙∙∙∙∙∙∙██∙███
    // ██∙██████████████████████∙∙∙∙∙∙∙██∙██∙
    // ██∙██████████████████████∙∙∙∙∙∙∙██∙█∙∙
    // ██∙██████████████████████∙∙∙∙∙∙∙██∙∙∙∙
    // ██∙∙█████████████████████∙∙∙∙∙∙∙██∙∙∙∙
    // ███∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙███∙∙∙∙
    // ∙████████████████████████████████∙∙∙∙∙
    // ∙∙██████████████████████████████∙∙∙∙∙∙
    0x3f, 0xff, 0xff, 0xff, 0x01, 0xff, 0xff, 0xff, 0xfe, 0x0e, 0x00, 0x00, 0x00, 0x1c, 0x33, 0xff, 0xff, 0xe0, 0x30, 0xdf, 0xff, 0xff, 0x80, 0xc3, 0x7f, 0xff, 0xfe, 0x03, 0x4d, 0xff, 0xff, 0xf8, 0x0d, 0xb7, 0xff, 0xff, 0xe0, 0x37, 0xdf, 0xff, 0xff, 0x80, 0xdf, 0x7f, 0xff, 0xfe, 0x03, 0x7d, 0xff, 0xff, 0xf8, 0x0d, 0xf7, 0xff, 0xff, 0xe0, 0x36, 0xdf, 0xff, 0xff, 0x80, 0xd3, 0x7f, 0xff, 0xfe, 0x03, 0x0c, 0xff, 0xff, 0xf8, 0x0c, 0x38, 0x00, 0x00, 0x00, 0x70, 0x7f, 0xff, 0xff, 0xff, 0x80, 0xff, 0xff, 0xff, 0xfc, 0x0f
};

const tImage Image_battery_small = { image_data_Image_battery_small, 38, 18,
    8 };