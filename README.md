# CH32V003 ADC Monitor Project

A comprehensive ADC monitoring system for the CH32V003 microcontroller featuring:
- 4-channel ADC measurement (PA0-PA3)
- ST7735 TFT display (80x160 pixels)
- TP223 touch button control
- PWM brightness control
- Auto-sleep functionality

## Hardware Connections

### ST7735S Display (80x160 TFT)
| CH32V003 Pin | ST7735 Pin | Function |
|--------------|------------|----------|
| PC2          | RESET      | Display Reset |
| PC3          | DC         | Data/Command |
| PC4          | CS         | Chip Select |
| PC5          | SCLK       | SPI Clock |
| PC6          | MOSI       | SPI Data |
| PD2          | LEDA       | Backlight anode (PWM) |
| GND          | LEDK       | Backlight cathode |
| 3V3          | VDD        | Power |
| GND          | GND        | Ground |

### TP223 Touch Button
| CH32V003 Pin | TP223 Pin | Function |
|--------------|-----------|----------|
| PD0          | OUT       | Touch Output |
| 3V3          | VCC       | Power |
| GND          | GND       | Ground |

### ADC Inputs
| CH32V003 Pin | ADC Channel | Function |
|--------------|-------------|----------|
| PD4          | ADC1        | Analog Input 1 |
| PD3          | ADC2        | Analog Input 2 |
| PD7          | ADC3        | Analog Input 3 |
| PD5          | ADC4        | Analog Input 4 |

### Debug
| CH32V003 Pin | Function |
|--------------|----------|
| PD1          | SWIO     |
| PC5          | SCK      |

**Debug Method:** SDI Print (no additional pins required)
**Note:** PD5 is now used for ADC4. UART debug pins are not used since project uses SDI print.

## Features

### Touch Button Control
- **Short Touch**: Turn on display (auto-off after 2 seconds)
- **Long Touch (1s)**: Toggle between auto-off and always-on modes
- **Auto-timeout**: Display automatically turns off after 2 seconds of inactivity

### Display Features
- Real-time ADC values display
- Voltage readings (0-3.3V)
- Percentage readings (0-100%)
- Color-coded channels
- Smooth PWM brightness control
- Fade in/out effects

### ADC Monitoring
- 12-bit resolution (0-4095)
- 4 independent channels
- 10 Hz update rate
- Voltage conversion (mV)
- Percentage calculation

## Project Structure

```
User/
├── main.c              # Main application logic
├── adc_config.h/c      # ADC configuration and reading
├── touch_button.h/c    # TP223 touch button interface
├── pwm_config.h/c      # PWM brightness control
├── display_control.h/c # Display state management
├── adc_display.h/c     # ADC data visualization
├── st7735.h/c          # ST7735 display driver
├── ch32v00x_it.c       # Interrupt handlers
└── font5x7.h           # Font definitions
```

## Building the Project

This project is designed for MounRiver Studio IDE. To build:

1. Open MounRiver Studio
2. Import this project
3. Build the project (Ctrl+B)
4. Flash to CH32V003 device

## Usage

1. **Power On**: The system initializes and shows a startup message
2. **Touch Control**: 
   - Touch the TP223 sensor to turn on the display
   - Hold for 1 second to toggle always-on mode
3. **ADC Monitoring**: Connect analog signals (0-3.3V) to PA0-PA3
4. **Display**: View real-time ADC readings with voltage and percentage values

## Configuration

### ADC Settings
- Sample time: 241 cycles for stable readings
- Reference voltage: 3.3V
- Update rate: 100ms (10 Hz)

### Display Settings
- PWM frequency: 1kHz
- Default brightness: 50%
- Fade step: 10 (adjustable)
- Screen timeout: 2 seconds

### Touch Button Settings
- Debounce time: 50ms
- Hold time: 1000ms (1 second)
- Interrupt-driven for responsive control

## Troubleshooting

### Display Issues
- Check SPI connections (PC2-PC6)
- Verify 3.3V power supply
- Ensure PWM pin (PD2) is connected to LEDA

### Touch Button Issues
- Check PD0 connection to TP223 OUT
- Verify TP223 power connections
- Check interrupt configuration

### ADC Issues
- Ensure PD4, PD3, PD7, PD5 are configured as analog inputs
- Check input voltage range (0-3.3V)
- Verify ADC reference voltage

## License

This project is based on WCH's CH32V003 examples and includes modifications for the ADC monitoring application.
