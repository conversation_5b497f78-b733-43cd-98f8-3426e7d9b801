{"version": "0.2.0", "configurations": [{"type": "mrs-debugger", "request": "launch", "name": "CH32V003F4U6", "cwd": "/home/<USER>/mounriver-studio-projects/CH32V003", "openOCDCfg": {"useLocalOpenOCD": true, "executable": "/opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/OpenOCD/OpenOCD/bin/openocd", "configOptions": ["-f \"/opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/OpenOCD/OpenOCD/bin/wch-riscv.cfg\" -c \"chip_id CH32V003\""], "gdbport": 3333, "telnetport": 4444, "tclport": 6666, "host": "localhost", "port": 3333, "skipDownloadBeforeDebug": false, "enablePageEraser": false, "enableNoZeroWaitingAreaFlash": false}, "gdbCfg": {"executable": "/opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/riscv-none-embed-gdb", "commands": ["set mem inaccessible-by-default off", "set architecture riscv:rv32", "set remotetimeout unlimited", "set disassembler-options xw"], "options": []}, "startup": {"initCommands": {"initReset": true, "initResetType": "init", "armSemihosting": false, "additionalCommands": []}, "loadedFiles": {"executableFile": "/home/<USER>/mounriver-studio-projects/CH32V003/obj/CH32V003F4U6.elf", "symbolFile": "/home/<USER>/mounriver-studio-projects/CH32V003/obj/CH32V003F4U6.elf", "executableFileOffset": 0, "symbolFileOffset": 0}, "runCommands": {"runReset": true, "runResetType": "halt", "additionalCommands": [], "setBreakAt": "handle_reset", "continue": true, "setProgramCounterAt": 0}, "debugInRAM": false}, "svdpath": "/opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/SDK/default/RISC-V/CH32V003/NoneOS/CH32V003xx.svd", "output": {"showDebugGDBTrace": true, "saveDebugOutputToFile": false, "showDebugOutputTimestamps": true}, "isDualCoreDebug": false, "dualCoreDebugRole": null, "architecture": "RISC-V"}]}